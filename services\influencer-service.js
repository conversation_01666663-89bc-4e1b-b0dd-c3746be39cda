// influencer-service.js
// Influencer-related business logic

import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import InfluencersClubConnector from '../connectors/influencers-club-connector.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

/**
 * Enrich an influencer with data from Influencers Club
 * @param {string} username - The influencer username
 * @param {string} platform - The platform
 * @param {boolean} forceRefresh - Whether to force refresh the data
 * @returns {Object} - The enriched influencer data
 */
async function enrichInfluencer(username, platform = "instagram", forceRefresh = false) {
  try {
    console.log(`Enriching influencer ${username} on ${platform} (forceRefresh: ${forceRefresh})`);
    const connector = new InfluencersClubConnector();

    const { influencerId, processedData } = await connector.enrichInfluencer(
      username,
      "username",
      platform,
      {
        forceRefresh: forceRefresh, // Explicitly pass the forceRefresh parameter
        emailRequired: "must_have",
        postDataRequired: true,
        updateFirestore: true
      }
    );

    console.log(`Enrichment completed for ${username} (ID: ${influencerId})`);
    return {
      influencerId,
      processedData
    };
  } catch (error) {
    console.error(`Failed to enrich influencer ${username}:`, error);
    throw error;
  }
}

/**
 * Get an influencer
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The influencer
 */
async function getInfluencer(influencerId) {
  const db = getFirestore();

  // Get the influencer document
  const influencerRef = db.collection('influencers').doc(influencerId);
  const influencerDoc = await influencerRef.get();

  if (!influencerDoc.exists) {
    throw new Error(`Influencer not found: ${influencerId}`);
  }

  return influencerDoc.data();
}

/**
 * Add an influencer to a campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} username - The influencer username
 * @returns {Object} - The campaign influencer
 */
async function addInfluencerToCampaign(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, username) {
  const db = getFirestore();

  // Check if the influencer exists
  const influencerRef = db.collection('influencers').doc(influencerId);
  const influencerDoc = await influencerRef.get();

  if (!influencerDoc.exists) {
    throw new Error(`Influencer not found: ${influencerId}`);
  }

  // Check if the campaign exists
  const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
  const campaignDoc = await campaignRef.get();

  if (!campaignDoc.exists) {
    throw new Error(`Campaign not found: ${campaignId}`);
  }

  // Create a campaign influencer document
  const campaignInfluencerRef = db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('campaign_influencers').doc(influencerId);

  // Prepare campaign influencer data
  const campaignInfluencerData = {
    influencer_id: influencerId,
    username: username,
    status: 'selected',
    created_at: Timestamp.now(),
    updated_at: Timestamp.now()
  };

  // Save to Firestore
  await campaignInfluencerRef.set(campaignInfluencerData);

  return campaignInfluencerData;
}

/**
 * Update an influencer's status in a campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} status - The status
 * @returns {Object} - The updated campaign influencer
 */
async function updateInfluencerStatus(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, status) {
  const db = getFirestore();

  // Get the campaign influencer document
  const campaignInfluencerRef = db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('campaign_influencers').doc(influencerId);

  const campaignInfluencerDoc = await campaignInfluencerRef.get();

  if (!campaignInfluencerDoc.exists) {
    throw new Error(`Campaign influencer not found: ${influencerId}`);
  }

  // Update the status
  await campaignInfluencerRef.update({
    status: status,
    updated_at: Timestamp.now()
  });

  // Get the updated document
  const updatedDoc = await campaignInfluencerRef.get();

  return updatedDoc.data();
}

export {
  enrichInfluencer,
  getInfluencer,
  addInfluencerToCampaign,
  updateInfluencerStatus
};




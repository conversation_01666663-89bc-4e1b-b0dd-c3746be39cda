// analysisFlow.js
// Node.js script outlining the sequential analysis flow for Influencer Intelligence
// This script uses placeholder functions for OpenAI queries and integrates Structured Outputs schemas.
// You can replace the TODOs with your actual OpenAI calls later.

import axios from 'axios';
import { joinImages } from 'join-images';
import <PERSON><PERSON><PERSON> from "openai";
import bodyParser from "body-parser";
import https from 'https';
import { Buffer } from 'buffer';
import fs from 'fs';
import path from 'path';
import { z } from 'zod';
import fetch from 'node-fetch';
import { Storage } from '@google-cloud/storage';
import express from 'express';
import cors from 'cors';

const storage = new Storage(); // Automatically uses GOOGLE_APPLICATION_CREDENTIALS
const bucketName = 'palas-run-cache'; // Replace with your bucket name

const app = express();
// CORS configuration: allow any origin that ends with "lovableproject.com"
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl)
    if (!origin) return callback(null, true);
    if (origin.endsWith("lovableproject.com") || origin.endsWith("lovable.app")) {
      return callback(null, true);
    } else {
      return callback(new Error("Not allowed by CORS"));
    }
  },
};

// Apply CORS middleware for all routes and explicitly handle preflight requests
app.use(cors(corsOptions));
app.options("*", cors(corsOptions));
app.use(bodyParser.json());

// Helper sleep function.
const sleep = ms => new Promise(resolve => setTimeout(resolve, ms));

// Constants – ideally these should be loaded from environment variables.
const DEBUGGING = true;
const current_year = 2025;
const INFLUENCERS_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMzQ0NzA4NDMwLCJpYXQiOjE3Mzk5MDg0MzAsImp0aSI6IjkxZTlmODlkZTI1NDQ3MWI5OTkzZTA3YjQ0YmVlMTI0IiwidXNlcl9pZCI6Nzg1MX0.qpbfGOObJMjLjYob_bzW7VX10SCdZpMVLxBEITIUuZc"; // Obtain this from your Influencers Club Dashboard.
const OPENAI_API_KEY =
  "********************************************************************************************************************************************************************";
const openaiClient = new OpenAI({ apiKey: OPENAI_API_KEY });

// Define pricing rates for different models (prices are per 1M tokens)
const pricingRates = {
    "gpt-4.5-preview": { input: 75.00, output: 150.00 },
    "gpt-4o": { input: 2.50, output: 10.00 },
    "gpt-4o-2024-05-13": { input: 5.00, output: 15.00 },
    "gpt-4o-audio-preview": { input: 2.50, output: 10.00 },
    "gpt-4o-realtime-preview": { input: 5.00, output: 20.00 },
    "gpt-4o-mini": { input: 0.15, output: 0.60 },
    "gpt-4o-mini-audio-preview": { input: 0.15, output: 0.60 },
    "gpt-4o-mini-realtime-preview": { input: 0.60, output: 2.40 },
    "o1": { input: 15.00, output: 60.00 },
    "o1-preview": { input: 15.00, output: 60.00 },
    "o3-mini": { input: 1.10, output: 4.40 },
    "o1-mini": { input: 1.10, output: 4.40 },
  };

  const SEED_INFLUENCER_AGENT = 'asst_lPjsRbNYLY0eR7mdW3XVSZFq';

  async function updateInfluencerFullReport(username, platform = "instagram", requestPayload, attempt = 0) {

    const url = "https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/";
    const payload = {
      filter_value: username,
      filter_key: "username",
      platform,
      email_required: true,
      post_data_required: true
    };
  
    const headers = {
      'Authorization': `Bearer ${INFLUENCERS_API_KEY}`,
      'Content-Type': 'application/json'
    };
  
    try {
      const response = await axios.post(url, payload, { headers });
      const enrichmentData = response.data;
      return enrichmentData;
    } catch (error) {
      console.error("❌ Failed to fetch influencer enrichment data.");
      console.error("📌 Username:", username);
      console.error("📡 Platform:", platform);
      console.error("📤 Request Payload:", JSON.stringify(payload, null, 2));
      console.error("📬 Request Headers:", JSON.stringify(headers, null, 2));
  
      if (error.response) {
        // Server responded with a status code out of 2xx
        console.error("🔴 HTTP Status:", error.response.status);
        console.error("📝 Response Data:", JSON.stringify(error.response.data, null, 2));
        console.error("📄 Response Headers:", JSON.stringify(error.response.headers, null, 2));
      } else if (error.request) {
        // Request made, but no response received
        console.error("🕳️ No response received from server.");
        console.error("📡 Raw Request:", error.request);
      } else {
        // Something else happened in setting up the request
        console.error("💥 Unexpected Error:", error.message);
      }
  
      console.error("📚 Full Error Stack:", error.stack);
      
      if (error.response && error.response.status === 500 && attempt < 10) {
        console.warn(`Retrying updateInfluencerFullReport (attempt ${attempt + 1}/10) after 1 second delay...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return updateInfluencerFullReport(username, platform, requestPayload, attempt + 1);
      }
      
      throw error;
    }
  }
  

  async function getLookalikeCreators(username, platform = "instagram") {
    // Endpoint for fetching similar (lookalike) creators based on influencer profile
    const url = "https://api-dashboard.influencers.club/public/v1/enrichment/lookalikes/";
    
    // Constructing the payload using the influencer's profile URL for the "url" filter_key
    const payload = {
      filter_value: `instagram.com/${username}`,  // Adjust if a different URL structure is needed for other platforms
      filter_key: "url",
      platform: platform
    };
  
    // Setup the headers with the API key and content type
    const headers = {
      'Authorization': `Bearer ${INFLUENCERS_API_KEY}`,
      'Content-Type': 'application/json'
    };
  
    try {
      // Make the POST request using axios
      const response = await axios.post(url, payload, { headers });
      return response.data;  // Expected to include a "similar_accounts" array in the response
    } catch (error) {
      console.error("❌ Failed to fetch lookalike creator data.");
      console.error("📌 Username:", username);
      console.error("📡 Platform:", platform);
      console.error("📤 Request Payload:", JSON.stringify(payload, null, 2));
      console.error("📬 Request Headers:", JSON.stringify(headers, null, 2));
  
      if (error.response) {
        // Server responded with a non-2xx status code
        console.error("🔴 HTTP Status:", error.response.status);
        console.error("📝 Response Data:", JSON.stringify(error.response.data, null, 2));
        console.error("📄 Response Headers:", JSON.stringify(error.response.headers, null, 2));
      } else if (error.request) {
        // Request made but no response received
        console.error("🕳️ No response received from server.");
        console.error("📡 Raw Request:", error.request);
      } else {
        // An unexpected error occurred during request setup
        console.error("💥 Unexpected Error:", error.message);
      }
  
      console.error("📚 Full Error Stack:", error.stack);
      throw error;  // Optionally re-throw the error for upstream handling
    }
  }

  async function updateInfluencerFullReportArchive(username, firestoreClient, platform = "instagram", requestPayload) {
    const url = "https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/";
    const payload = {
      filter_value: username,
      filter_key: "username",
      platform,
      email_required: true,
      post_data_required: true
    };
    const headers = {
      'Authorization': `Bearer ${INFLUENCERS_API_KEY}`,
      'Content-Type': 'application/json'
    };
    
    const response = await axios.post(url, payload, { headers });
    enrichmentData = response.data;
    return enrichmentData;
}

/**
 * Loads a JSON file from a specified relative path (relative to the project root)
 * and returns the parsed JSON object.
 *
 * @param {string} filePath - The relative file path from the project root.
 * @returns {Object} - The parsed JSON object.
 * @throws Will throw an error if the file cannot be read or parsed.
 */
function loadJson(filePath) {
    // Construct the absolute path using the project's root directory.
    const absolutePath = path.join(process.cwd(), filePath);
    
    try {
      const fileContent = fs.readFileSync(absolutePath, 'utf8');
      return JSON.parse(fileContent);
    } catch (error) {
      console.error('Error reading or parsing JSON file:', error);
      throw error;
    }
  }
    /**
     * Writes a JSON object to a file at a specified relative path (relative to the project root).
     *
     * @param {string} filePath - The relative file path where the JSON file will be written.
     * @param {Object} jsonObject - The JSON object to be written to the file.
     * @throws Will throw an error if the file cannot be written.
     */
    function writeJson(filePath, jsonObject) {
      // Construct the absolute path using the project's root directory.
      const absolutePath = path.join(process.cwd(), filePath);
      
      try {
        // Convert the JSON object to a pretty-printed string.
        const data = JSON.stringify(jsonObject, null, 2);
        fs.writeFileSync(absolutePath, data, 'utf8');
        console.log('JSON file written successfully.');
      } catch (error) {
        console.error('Error writing JSON file:', error);
        throw error;
      }
    }
  
    async function processAgent(agentID, prompt) {
        let attempt = 0;
      
        while (true) {
          attempt++;
          console.log(`Starting attempt ${attempt}...`);
          
          // Create a new thread and send the prompt as a user message
          const thread = await openaiClient.beta.threads.create();
          await openaiClient.beta.threads.messages.create(thread.id, {
            role: "user",
            content: prompt,
          });
      
          // Start the run with the specified Dreamvault assistant
          const run = await openaiClient.beta.threads.runs.create(thread.id, {
            assistant_id: agentID,
          });
      
          let cancelled = false;
      
          // Create a timeout promise that cancels the run after 90 seconds
          const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => {
              // Cancel the run and mark as cancelled
              openaiClient.beta.threads.runs.cancel(thread.id, run.id).catch(() => {});
              cancelled = true;
              resolve("timeout");
            }, 120000);
          });
      
          // Poll for the run's completion status every 3 seconds
          const pollPromise = (async () => {
            let runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
            while (runStatus.status !== "completed" && !cancelled) {
              await new Promise((resolve) => setTimeout(resolve, 3000));
              runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
              console.log(`Run status: ${runStatus.status}`);
            }
            return "completed";
          })();
      
          // Race between the polling and the timeout
          const outcome = await Promise.race([timeoutPromise, pollPromise]);
      
          if (outcome === "completed") {
            // Retrieve messages from the thread
            const messagesResponse = await openaiClient.beta.threads.messages.list(thread.id);
            const assistantMessages = messagesResponse.data.filter(msg => msg.role === "assistant");
      
            if (assistantMessages.length === 0) {
              throw new Error("No assistant response found");
            }
      
            // Assume the last assistant message is the desired reply
            const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
            const assistantReply = lastAssistantMessage.content[0].text.value;
      
            // Attempt to parse the assistant's reply as JSON
            let parsedReply;
            try {
              parsedReply = JSON.parse(assistantReply);
              console.log("Parsed reply:", JSON.stringify(parsedReply));
            } catch (error) {
              console.error("Error parsing JSON from assistant:", error);
              throw new Error("Assistant responded with invalid JSON");
            }
      
            return parsedReply;
          } else {
            console.log(`Attempt ${attempt} timed out. Resubmitting the call to OpenAI...`);
            // Loop will retry by creating a new thread and resubmitting the prompt.
          }
        }
      }
       

async function processAgentArchive(agentID, prompt){
    // Send the prompt as a user message in the thread
    const thread = await openaiClient.beta.threads.create();
    await openaiClient.beta.threads.messages.create(thread.id, {
        role: "user",
        content: prompt,
      });
  
      // Start the run with the new Dreamvault assistant
      const run = await openaiClient.beta.threads.runs.create(thread.id, {
        assistant_id: agentID, // updated assistant ID
      });
  
      // Poll until the run status is 'completed'
      let runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
      //console.log(JSON.stringify(runStatus));
      while (runStatus.status !== "completed") {
        // Wait 3 seconds between polls
        await new Promise((resolve) => setTimeout(resolve, 3000));
        runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
        console.log(JSON.stringify(runStatus));
      }
  
      // Retrieve all messages from the thread
      const messagesResponse = await openaiClient.beta.threads.messages.list(thread.id);
      //console.log(JSON.stringify(messagesResponse));
  
      // Filter messages to get only those from the assistant
      const assistantMessages = messagesResponse.data.filter(
        (msg) => msg.role === "assistant"
      );
  
      if (assistantMessages.length === 0) {
        return res.status(500).json({ error: "No assistant response found" });
      }
  
      // The assistant's reply is presumably the last message
      const lastAssistantMessage =
        assistantMessages[assistantMessages.length - 1];
      console.log(JSON.stringify(lastAssistantMessage));
  
      // The raw text response from the assistant
      const assistantReply = lastAssistantMessage.content[0].text.value;
      //console.log("Raw assistant reply:", assistantReply);
  
      // Attempt to parse the JSON from the assistant
      let parsedReply;
      try {
        parsedReply = JSON.parse(assistantReply);
      } catch (parseError) {
        console.error("Error parsing JSON from assistant:", parseError);
        return res
          .status(500)
          .json({ error: "Assistant responded with invalid JSON" });
      }
  
      // Return the parsed JSON as-is (with "response" and "sentiment" fields)
      return parsedReply;
}
async function processPart1(){
    const prompt = `Please process the following reports into your assigned schema. Your only role is data extraction with utter accuracy.
    
    `
    
}
  async function processInstagramImagePosts(data) {
    // Validate input structure
    if (!data.instagram || !Array.isArray(data.instagram.post_data)) {
      throw new Error('Invalid input: Expected data.instagram.post_data to be an array');
    }
  
    const imageUrls = [];
  
    // Append the HD profile picture as the first image, if available
    if (data.instagram.profile_picture_hd) {
      imageUrls.push(data.instagram.profile_picture_hd);
      console.log(`Profile Picture Added: ${data.instagram.profile_picture_hd}`);
    }
  
    // Process each post: get the first image from each post's media array
    data.instagram.post_data.forEach(post => {
      if (Array.isArray(post.media)) {
        const firstImageItem = post.media.find(mediaItem => mediaItem.type === 'image' && mediaItem.url);
        if (firstImageItem) {
          imageUrls.push(firstImageItem.url);
          console.log(`Image Found: ${firstImageItem.url}`);
        }
      }
    });
  
    // If no images are found, return an empty string.
    if (imageUrls.length === 0) {
      console.log("No image posts found. Returning an empty string.");
      return '';
    }
  
    // Download all images concurrently
    const imageBuffers = await Promise.all(imageUrls.map(url => downloadImage(url)));
  
    // Merge the images vertically using join-images
    const mergedImage = await joinImages(imageBuffers, { direction: 'vertical' });
    
    // Convert the merged image to JPEG format and get the buffer
    const mergedBuffer = await mergedImage.toFormat('jpeg').toBuffer();
    const base64Image = mergedBuffer.toString('base64');
    
    return base64Image;
  }

/**
 * Downloads the content from a given URL and returns a Promise resolving with the data as a Buffer.
 * @param {string} url - The URL to download.
 * @returns {Promise<Buffer>}
 */
function downloadImage(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      const chunks = [];
      res.on('data', (chunk) => chunks.push(chunk));
      res.on('end', () => resolve(Buffer.concat(chunks)));
    }).on('error', reject);
  });
}

// Deep merge function to merge two objects recursively
function deepMerge(target, source) {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        // If both values are objects (and not arrays), merge them recursively
        if (
          target[key] &&
          typeof target[key] === "object" &&
          !Array.isArray(target[key]) &&
          typeof source[key] === "object" &&
          !Array.isArray(source[key])
        ) {
          target[key] = deepMerge({ ...target[key] }, source[key]);
        } else {
          // Otherwise, assign the source value to the target
          target[key] = source[key];
        }
      }
    }
    return target;
  }
  function extractJSON(str) {
    // Find the first occurrence of '{'
    const firstIndex = str.indexOf('{');
    if (firstIndex === -1) return null;
  
    // Use a stack approach to find the matching closing '}'
    let stack = 0;
    let endIndex = -1;
    for (let i = firstIndex; i < str.length; i++) {
      if (str[i] === '{') {
        stack++;
      } else if (str[i] === '}') {
        stack--;
        // When the stack is empty, we found the matching closing brace
        if (stack === 0) {
          endIndex = i;
          break;
        }
      }
    }
  
    // If no complete JSON object is found, return null
    if (endIndex === -1) return null;
  
    // Extract the potential JSON substring
    const jsonString = str.substring(firstIndex, endIndex + 1);
    try {
      // Parse and return the JSON object
      return JSON.parse(jsonString);
    } catch (error) {
      console.error("Invalid JSON format:", error);
      console.log(`Erroneous JSON: ${str}`)
      return null;
    }
  }

  /**
 * Writes a JSON object to a file in a Google Cloud Storage bucket.
 *
 * @param {string} filePath - The destination path within the bucket.
 * @param {Object} jsonObject - The JSON object to be written.
 * @throws Will throw an error if the file cannot be written.
 */
async function writeJsonToBucket(filePath, jsonObject) {
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);
    
    try {
      const data = JSON.stringify(jsonObject, null, 2);
      await file.save(data, { contentType: 'application/json' });
      console.log('JSON file written successfully to bucket.');
    } catch (error) {
      console.error('Error writing JSON file to bucket:', error);
      throw error;
    }
  }

  async function cacheImage(imageUrl) {
    // Provide a sample image URL (you can replace with any public image URL)
    // Base URL for your deployed Cloud Function.
    const baseUrl = 'https://image-cacher-9815718377.us-central1.run.app';
    
    try {
      const response = await fetch(`${baseUrl}/cache-image`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageUrl: imageUrl })
      });
      
      if (!response.ok) {
        throw new Error(`POST failed: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Cache Image Test - Returned ID:', data.url);
      return data.url;
    } catch (error) {
      console.error('Error in testCacheImage:', error);
    }
  }

  /**
 * Checks for a JSON file in a Google Cloud Storage bucket.
 * Returns the parsed JSON object if the file exists, or false if it doesn't.
 *
 * @param {string} bucketName - The name of the GCS bucket.
 * @param {string} filePath - The path to the file within the bucket.
 * @returns {Promise<Object|boolean>} - Parsed JSON data from the file, or false if the file is not found.
 */
async function getCachedJson(bucketName, filePath) {
    const storage = new Storage(); // Automatically uses GOOGLE_APPLICATION_CREDENTIALS
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);
  
    try {
      // Check if the file exists
      const [exists] = await file.exists();
      if (!exists) {
        console.log(`File not found: ${filePath} in bucket ${bucketName}.`);
        return false;
      }
  
      // Download the file contents
      const [contents] = await file.download();
      try {
        const jsonData = JSON.parse(contents.toString('utf8'));
        return jsonData;
      } catch (parseError) {
        console.error("Error parsing JSON data from file:", parseError);
        return false;
      }
    } catch (error) {
      console.error("Error accessing the file in bucket:", error);
      throw error;
    }
  }

  async function extractInfluencerProfileDirect(data) {
    // Helper function to format numbers with commas
    const formatNumber = num => (num != null && !isNaN(num)) ? Number(num).toLocaleString() : "";
  
    // Helper function to compute engagement rate as a percentage string with two decimals
    const computeEngagementRate = (totalEngagement, followerCount) => {
      if (followerCount > 0) {
        return ((totalEngagement / followerCount) * 100).toFixed(2) + "%";
      }
      return "";
    };
  
    // -- Basic Fields (from root) --
    const email = data.email || "";
    const location = data.location || "";
    const speakingLanguage = data.speaking_language || "";
    const firstName = data.first_name || "";
    const profilePicture = await cacheImage(data.instagram.profile_picture_hd) || "";
    const hasBrandDeals = data.has_brand_deals || false;
    const hasLinkInBio = data.has_link_in_bio || false;
    const isBusiness = data.is_business || false;
    const isCreator = data.is_creator || false;
    const postData = data.post_data || {};
    const influencerCategories = data.instagram.niche_class.join(' - ') || "";
  
    // -- Instagram Data Extraction --
    const insta = data.instagram || {};
    const instaUsername = insta.username || "";
    const instaFollowerCount = insta.follower_count != null ? insta.follower_count : 0;
    const instaBio = insta.biography || "";
    const instaEngagementPercent = insta.engagement_percent != null ? insta.engagement_percent : 0;
    const instaLatestPost = insta.latest_post || {};
    const instaPostEngagement = instaLatestPost.engagement || {};
    const instaPostMedia = Array.isArray(instaLatestPost.media) ? instaLatestPost.media : [];
  
    // -- YouTube Data Extraction --
    const yt = data.youtube || {};
    const ytSubscriberCount = yt.subscriber_count != null ? yt.subscriber_count : 0;
    const ytLatestVideo = yt.latest_video || {};
    const ytVideoViews = ytLatestVideo.views != null ? ytLatestVideo.views : 0;
    const ytVideoLikes = ytLatestVideo.likes != null ? ytLatestVideo.likes : 0;
    const ytVideoComments = ytLatestVideo.comments != null ? ytLatestVideo.comments : 0;
  
    // -- TikTok Data Extraction --
    const tt = data.tiktok || {};
    const ttUsername = tt.username || "";
    const ttFollowerCount = tt.follower_count != null ? tt.follower_count : 0;
    const ttLatestPost = tt.latest_post || {};
    const ttPostMedia = ttLatestPost.media || {};
    const ttPostEngagement = ttLatestPost.engagement || {};
  
    // -- Creator Has --
    const creatorHas = Array.isArray(data.creator_has) ? data.creator_has : [];
  
    // -- Assemble profileInfo --
    const profileInfo = {
      username: instaUsername,
      profileImageUrl: profilePicture,
      category: influencerCategories,
      bio: instaBio
    };
  
    // -- Assemble Influencer Stats --
    const platforms = [];
    
    // Instagram platform: use provided engagement percent
    platforms.push({
      name: "Instagram",
      followers: formatNumber(instaFollowerCount),
      engagement: instaEngagementPercent != null ? `${instaEngagementPercent}%` : ""
    });
  
    // YouTube platform: compute engagement rate if video exists
    if (ytLatestVideo.video_id) {
      const ytTotalEngagement = (ytVideoLikes || 0) + (ytVideoComments || 0);
      platforms.push({
        name: "YouTube",
        followers: formatNumber(ytSubscriberCount),
        engagement: ytSubscriberCount > 0 ? computeEngagementRate(ytTotalEngagement, ytSubscriberCount) : ""
      });
    }
  
    // TikTok platform: compute engagement rate using likes, comments, and shares
    if (ttLatestPost.post_id) {
      const ttTotalEngagement = (ttPostEngagement.likes || 0) + (ttPostEngagement.comments || 0) + (ttPostEngagement.shares || 0);
      platforms.push({
        name: "TikTok",
        followers: formatNumber(ttFollowerCount),
        engagement: ttFollowerCount > 0 ? computeEngagementRate(ttTotalEngagement, ttFollowerCount) : ""
      });
    }
  
    // Engagement Rate object (using Instagram metric)
    const engagementRate = {
      value: instaEngagementPercent != null ? `${instaEngagementPercent}%` : "",
      description: "Instagram engagement rate."
    };
  
    // Content Types: Extract unique media types from all Instagram posts.
    const contentTypesSet = new Set();
    if (Array.isArray(insta.post_data)) {
      insta.post_data.forEach(post => {
        if (Array.isArray(post.media)) {
          post.media.forEach(mediaItem => {
            if (mediaItem.type) {
              contentTypesSet.add(mediaItem.type);
            }
          });
        }
      });
    }
    const contentTypes = Array.from(contentTypesSet);
  
    const influencerStats = {
      platforms,
      engagementRate,
      contentTypes
    };
  
    // -- Recent Posts --
    const recentPosts = Array.isArray(insta.post_data)
      ? await Promise.all(insta.post_data.map(async post => {
          let mediaUrl = "";
          if (Array.isArray(post.media) && post.media.length > 0) {
            const firstMedia = post.media[0];
            if (firstMedia.type === "image") {
              mediaUrl = firstMedia.url;
            }
          }
          return {
            id: parseInt(post.post_id, 10) || 0,
            imageUrl: mediaUrl ? await cacheImage(mediaUrl) : "",
            likes: post.engagement && post.engagement.likes != null ? formatNumber(post.engagement.likes) : "0",
            comments: post.engagement && post.engagement.comments != null ? formatNumber(post.engagement.comments) : "0"
          };
        }))
      : [];
  
    // -- Return the assembled influencer profile --
    return {
      profileInfo,
      influencerStats,
      recentPosts
    };
  }  
  
/**
 * Extracts key metrics from data and caches image URLs (including the profile picture)
 * on the server. Returns the cache IDs which can be later used to retrieve images.
 * @param {object} data - The data object containing social platform details.
 * @returns {Promise<object>} - A promise that resolves to the metrics object.
 */
async function extractKeyMetrics(data) {
    // Extract email from the root (or fallback if nested)
    const email = data.email || null;
    
    // Extract profile picture HD URL (from data.instagram or root)
    const profilePictureHD = (data.instagram && data.instagram.profile_picture_hd) || data.profile_picture_hd || null;
    
    // Initialize engagement counters and collections for images and mentions (for Instagram).
    let totalLikes = 0,
        totalComments = 0,
        totalShares = 0,
        totalPlayCount = 0,
        postCount = 0;
    const imageUrls = [];
    const mentionSet = new Set();
    
    // Prepare an array for all Instagram post data.
    let allPosts = [];
    
    // Process Instagram posts if available.
    if (data.instagram && Array.isArray(data.instagram.post_data)) {
      allPosts = data.instagram.post_data; // Preserve the entire posts array.
      data.instagram.post_data.forEach(post => {
        postCount++;
        if (post.engagement) {
          totalLikes += post.engagement.likes || 0;
          totalComments += post.engagement.comments || 0;
          totalShares += post.engagement.shares || 0;
          totalPlayCount += post.engagement.play_count || 0;
        }
        // Collect image URLs from media items where type is "image"
        if (post.media && Array.isArray(post.media)) {
          post.media.forEach(mediaItem => {
            if (mediaItem.type === "image") {
              imageUrls.push(mediaItem.url);
            }
          });
        }
        // Collect tagged users (mentions) from posts
        if (post.tagged_users && Array.isArray(post.tagged_users)) {
          post.tagged_users.forEach(user => mentionSet.add(user));
        }
      });
    }
    
    // Calculate engagement stats for Instagram.
    const instagramEngagement = {
      total_likes: totalLikes,
      total_comments: totalComments,
      total_shares: totalShares,
      total_play_count: totalPlayCount,
      average_likes: postCount ? totalLikes / postCount : 0,
      average_comments: postCount ? totalComments / postCount : 0,
      average_shares: postCount ? totalShares / postCount : 0,
      average_play_count: postCount ? totalPlayCount / postCount : 0
    };
    
    // Compute engagement stats for YouTube if available.
    let youtubeEngagement = {};
    if (data.youtube && data.youtube.latest_video) {
      const video = data.youtube.latest_video;
      youtubeEngagement = {
        total_likes: video.likes || 0,
        total_comments: video.comments || 0,
        total_views: video.views || 0,
        average_likes: video.likes || 0,
        average_comments: video.comments || 0,
        average_views: video.views || 0
      };
    }
    
    // Compute engagement stats for TikTok if available.
    let tiktokEngagement = {};
    if (data.tiktok && data.tiktok.latest_post && data.tiktok.latest_post.engagement) {
      const tiktokPost = data.tiktok.latest_post;
      tiktokEngagement = {
        total_likes: tiktokPost.engagement.likes || 0,
        total_comments: tiktokPost.engagement.comments || 0,
        total_shares: tiktokPost.engagement.shares || 0,
        total_views: tiktokPost.engagement.views || 0,
        average_likes: tiktokPost.engagement.likes || 0,
        average_comments: tiktokPost.engagement.comments || 0,
        average_shares: tiktokPost.engagement.shares || 0,
        average_views: tiktokPost.engagement.views || 0
      };
    }
    
    // Aggregate engagement stats from all platforms.
    const engagementStats = {
      instagram: instagramEngagement,
      youtube: youtubeEngagement,
      tiktok: tiktokEngagement
    };
    
    // Roll-up hashtags if they exist (deduplicate)
    let hashtagsRolledUp = [];
    if (Array.isArray(data.hashtags)) {
      hashtagsRolledUp = [...new Set(data.hashtags)];
    }
    
    // For "brands", if not explicitly provided, default to an empty array.
    const brands = data.brands || [];
    
    // Convert the set of mentions into an array.
    const mentions = Array.from(mentionSet);
    
    // Process the image URLs: cache each one and get a cache ID.
    const cachedImageIds = await Promise.all(
      imageUrls.map(url => cacheImage(url))
    );
    
    // Cache the profile picture if available.
    let cachedProfilePictureId = profilePictureHD;
    if (profilePictureHD) {
      try {
        cachedProfilePictureId = await cacheImage(profilePictureHD);
      } catch (error) {
        console.error('Error caching profile picture:', error);
      }
    }
    
    // Extract additional useful metrics with platform-specific follower counts.
    const otherMetrics = {
        username: data.username || null,
        full_name: data.full_name || null,
        follower_count: data.follower_count || null,
        location: data.location || null,
        category: data.category || null,
        posting_frequency_recent_months: data.posting_frequency_recent_months || null,
        creator_follower_growth: data.creator_follower_growth || null,
        speaking_language: data.speaking_language || null,
        first_name: data.first_name || null,
        has_brand_deals: data.has_brand_deals || false,
        has_link_in_bio: data.has_link_in_bio || false,
        is_business: data.is_business || false,
        is_creator: data.is_creator || false,
        instagram_username: data.instagram && data.instagram.username ? data.instagram.username : null,
        instagram_follower_count: data.instagram && data.instagram.follower_count ? data.instagram.follower_count : null,
        youtube_title: data.youtube && data.youtube.title ? data.youtube.title : null,
        youtube_subscriber_count: data.youtube && data.youtube.subscriber_count ? data.youtube.subscriber_count : null,
        tiktok_username: data.tiktok && data.tiktok.username ? data.tiktok.username : null,
        tiktok_follower_count: data.tiktok && data.tiktok.follower_count ? data.tiktok.follower_count : null,
        creator_has: data.creator_has || []
        };
      
    
    // Return the optimized metrics including the cache IDs for images.
    return {
      email,
      profile_picture_hd: cachedProfilePictureId, // Cache ID for the profile picture.
      engagement_stats: engagementStats,
      hashtags: hashtagsRolledUp,
      image_urls: cachedImageIds, // Array of cache IDs for post images.
      brands,
      mentions,
      other_metrics: otherMetrics,
      all_posts: allPosts
    };
  }
  
/**
 * Main sequential flow that performs the six analyses sequentially.
 * It receives an input JSON (campaign info, influencer details, etc.) and then 
 * calls each phase’s placeholder function and combines the outputs.
 */
async function performInfluencerAnalysis(input) {
  // Sample input is expected as a JSON object
  // with keys such as campaign, influencer, etc.
  // Phase 1: Campaign Brief & Audience Analysis
    let influencer, report_id, campaignJSON, contentString;
    if (input.selected_account && input.report_id) {
      influencer = { username: input.selected_account };
      report_id = input.report_id;
    } else {
      report_id = Date.now() + '_' + Math.random().toString(36).slice(2, 12);

      const phase1Prompt = `Project: Develop a Comprehensive Campaign Brief for **${input.campaign.name}**

      **Overview:** We are initiating an influencer marketing campaign. Below are the campaign inputs and requirements. Your task is to synthesize this information into a structured campaign brief that will guide all subsequent phases.

      **Campaign Inputs:**
      ${JSON.stringify(input.campaign)}

      **Tasks:**
      Craft the ideal influencer archetype for the campaign ensuring the influencers are of the optimal size and focus.`;

      campaignJSON = await processAgent('asst_8z7v7LffCsuh2Ez9tBtStYbg', phase1Prompt);
      if(campaignJSON == null){return}
      
      await writeJsonToBucket(`/runs/${report_id}_campaign_analysis.json`, campaignJSON);
      console.log(campaignJSON);

      
      let aggregatedValidAccounts = [];
        let i = 0;
        let discoveryJSON = null;
        let targetNumber = 6;

        while (aggregatedValidAccounts.length < targetNumber && i <= 5) {
            // Retrieve seed influencers based on the campaign description.
            let seedInfluencers = await processAgent(
              SEED_INFLUENCER_AGENT, 
              `Please provide the optimal seed influencers for the following campaign description\n\n${JSON.stringify(campaignJSON)}\n\nNEVER recommend any influencers who do not fit the campaign for this seed (e.g. musician for a swimwear ad unless they have a swimwear following somehow). Identify the dead middle PERFECT influencers for us to extrapolate from.`
            );
            seedInfluencers = seedInfluencers.recommended_influencers;
            console.log(`Seed Influencers: ${JSON.stringify(seedInfluencers)}`);
            
            // Map each influencer to a promise that retrieves and filters lookalike creators concurrently.
            const lookalikePromises = seedInfluencers.map(async (influencer) => {
              try {
                console.log(`Looking for lookalikes for: ${influencer.name}`);
                const result = await getLookalikeCreators(influencer.name);
                console.log(`Successful result found for influencer: ${influencer.name}`);
                // Filter accounts based on campaign requirements.
                const validAccounts = result.similar_accounts.filter(account =>
                  account.follower_count >= campaignJSON.min_follower_count &&
                  account.engagement_percent > campaignJSON.min_engagement_rate
                );
                return validAccounts;
              } catch (error) {
                console.error(`Error with influencer ${influencer.name}. Continuing to next influencer...`);
                return []; // Return an empty array on error to continue the aggregation.
              }
            });
          
            // Execute all lookups concurrently and flatten the resulting arrays.
            const results = await Promise.all(lookalikePromises);
            aggregatedValidAccounts = aggregatedValidAccounts.concat(...results);
            console.log(`Total valid results so far: ${aggregatedValidAccounts.length}`);
            
            // Continue to next iteration if the aggregated count is still below the target.
            if (aggregatedValidAccounts.length < targetNumber) {
              i++;
            }
          }          

        // Populate discoveryJSON with the final aggregated valid results as a JSON array
        discoveryJSON = aggregatedValidAccounts;
        console.log('Final aggregated valid results:', discoveryJSON);
      if(discoveryJSON == null){return}

      /*// Phase 2: Broad Influencer Discovery (Web Search)
      const phase2Prompt = `Project: Broad Influencer Discovery for Campaign **${input.campaign.name}**

      **Objective:** Qualify a wide list of potential influencers that align with the **${input.campaign.name}** campaign brief. Use web searches and available public tools to analyze candidate influencers, without yet doing deep analysis. Aim for breadth: qualify and rank as many relevant candidates as possible, then we will narrow down later.

      **Inputs:**
      Campaign information: [${JSON.stringify(campaignJSON)}]
      Influencers to qualify and rank: [${JSON.stringify(discoveryJSON)}]

      **Methodology:**
      1. **Web Search for Influencer Lists:** Use specific search queries to find articles, rankings, or forum threads that list influencers in the desired niche:
      - Search across multiple sources (blogs, influencer databases, social media “who to follow” suggestions). If the campaign is regional, include location in searches (e.g., “<locale> <industry> influencers”).
      - **Pattern to note:** If certain names appear in multiple reputable lists, prioritize capturing those (indicates consensus on their influence).
      2. **Hashtag & Social Search:** Check social media or hashtag discovery:
      - For Instagram: search the hashtag related to campaign ('#<specific applicable hashtag>' or a relevant popular tag) to see top posts/users.
      Campaign Content Themes: [${campaignJSON.content_themes}]
      - Look at platforms like TikTok’s search or YouTube (use YouTube search for top channels in niche).
      - If available, use any free influencer search tool (like searching on Modash’s free directory or SocialBlade for top creators in a category).
      3. **Collect Influencer Candidates:** For each potential influencer found, gather key details:
      - **Name:** The influencer’s name or handle.
      - **Primary Handle/URL:** Their main profile link or handle (e.g., Instagram @username or YouTube channel URL).
      - **Platform:** The primary platform where they are active (Instagram, TikTok, etc., if not obvious from context).
      - **Follower Count (estimate):** If an exact number is stated, use it. If not, infer from context (e.g., an article might say “hundreds of thousands of followers”). You can also click their profile to see the current follower count if needed.
      - **Category/Niche:** Confirm their niche or what they’re known for (should align with the identified demo and messaging.). For example, are they a fitness trainer, tech reviewer, beauty vlogger, etc. Often the bio or article will state this.
      - **Quick Bio/Note:** A one-line note on who they are (“Yoga instructor and wellness blogger” or “Tech YouTuber focusing on smart home gadgets”). This helps recall why they’re a fit.
      - **Initial Fit Observations:** Any immediate impressions of how they match the campaign criteria. E.g., High engagement on sustainable living posts or Content frequently covers the keyword theme. Only include if something notable stands out at a glance.
      4. **Output Formatting:** Prepare the findings as a JSON object containing a list of influencer candidates. Use the following structure for each influencer entry:
      - 'influencers.name' – (string) Influencer’s name or handle.
      - 'influencers.handles' – (object) Their social handle(s), e.g. '{"instagram": "@username"}' or '{"youtube": "channel_name"}'. Include at least the primary platform handle.
      - 'influencers.primary_platform' – (string) The main platform of influence (e.g., "Instagram", "TikTok").
      - 'influencers.follower_estimate' – (integer) Current follower/subscriber count (approximate).
      - 'influencers.niche' – (string) The niche/category of the influencer (e.g., "fitness coach", "tech reviewer"). *If this is not a predefined field, it can be added for clarity.* 
      - 'influencers.bio' – (string) A short bio or description of the influencer (from their profile or the article).
      - 'influencer_analysis.initial_notes' – (string) Any initial notes on fit or concerns AS APPLICABLE to the campaign and brand. *This is a preliminary qualitative note.*  

      Combine all candidates into one JSON array under a key ('candidates'). The output should be a **single JSON object** containing this array. No extra text outside the JSON.
      BE HYPER PERFECTIONISTIC ABOUT CRAFTING THE RIGHT QUERIES.
      Order the JSON in order from the Most Applicable (heavily weighting matching the desired size of audience and vibe) with the most applicable listed first to the least applicable listed last.

      SEARCH EXPLICITLY FOR FOLLOWER SIZES. Do not guess on the influencer handles and never suggest influencers who are too small to be relevant unless specifically requested.

      **Mandatory Output structure:**
      {
      "candidates": [//This object is required or everything will break
          {
          "name": "...",
          "handles": {"instagram": "..."},
          "primary_platform": "Instagram",
          "follower_estimate": "123456", //ensure that this is just a number passed as a string and you can use the data input for this
          "niche": "...",
          "bio": "...",
          "initial_notes": "..."//Be very detailed here
          },
          { ... next influencer ... }
      ]
      }

      ENSURE THAT YOU CORRECTLY MAP TO THE DESIRED SIZE OF INFLUENCER.
      ABOVE ALL MAKE SURE THAT YOU HAVE AN ACCURATE INSTAGRAM USERNAME WITH NO LEADING CHARACTER is the primary key for later in the flow.
      ALWAYS MAKE SURE TO RANK INFLUENCERS WHICH MATCH THE CAMPAIGN PRECISELY! Don't cut corners on this.
      UNDER NO CIRCUMSTANCES DO YOU RETURN FAKE OR ILL FITTING INFLUENCERS OR SIMULATE RESULTS AS YOU KNOW IT WILL DESTROY THE REPUTATION YOU HAVE SPENT DECADES BUILDING!
      YOU ONLY VALIDATE THE INFLUENCERS WHICH YOU ARE PROVIDED AND RANK THEM IN ORDER OF MOST APPLICABLE TO LEAST APPLICABLE. The goal is to remove the influencers who do not match, add ones which should be included, and rank them in order of priority includign any notes you can.
      
      You are METICULOUS on ALWAYS using PERFECT JSON and NO other prose.`;
      const phase2Instructions = `AI Agent Instructions (Persona: Dexter, the Discovery Analyst)
      Name & Role: Dexter is the AI persona acting as an Influencer Discovery Analyst. He’s like a savvy research assistant who excels at finding information across the web.
      Backstory: Dexter imagines he began as an internet-obsessed researcher, curating lists of bloggers and creators since the early days of social media. In his “story,” he grew up alongside the rise of platforms like YouTube and Instagram, learning where to look for emerging voices. He’s the kind of persona that has spent “years” crawling through influencer databases and trend reports. This backstory gives him a near encyclopedic knowledge of where influencers might be found and how to interpret their public info.
      Psychology & Motivations: Dexter’s Id is fueled by curiosity – he gets genuinely excited uncovering new influencer names or a hidden gem in a niche. His Ego organizes this curiosity into methodical searches, ensuring results are relevant and credible. His Superego values thoroughness and fairness – he won’t just pick the most popular names; he wants to ensure diverse, quality options that truly fit the brief. He takes pride in leaving no stone unturned.
      Beliefs & Values: Dexter believes every data point tells a story. He approaches the task with an almost journalistic integrity – verifying facts (like follower counts) across sources. He is meticulous and resourceful. For instance, if one source doesn’t list engagement, he’ll recall a pattern from another source or find a workaround. He values pattern recognition: when scanning search results, he notes if the same name appears in multiple “top influencer” lists – a sign of a notable figure. He also compares patterns like follower count vs. visible engagement (a skill human analysts use to gauge authenticity).
      Pattern Recognition & Augmentation: Like a human research expert, Dexter quickly identifies patterns across search results. For example, he might notice that many top fitness influencers have “Fit” or “Yoga” in their handles, guiding him to refine his search terms. He spots overlapping names across “top 10” articles and checks multiple sources to confirm those influencers’ reputations. Dexter also leverages his memory of public databases (like influencer directories and social media) – akin to how an expert might remember a well-known blogger mentioned in an industry conference. He augments analysis by bringing in these external data points: if an influencer’s Instagram shows 100k followers and their TikTok is also popular, he will catch that pattern and note it.
      Personality Aligned to Phase: In this discovery phase, Dexter is inquisitive, systematic, and breadth-oriented. He’s friendly and enthusiastic when presenting findings (“I found some promising candidates!”), but he is also detail-oriented about capturing each influencer’s key stats. Dexter’s persona ensures we get a comprehensive longlist: he is persistent (will go through many pages of search results), analytical (can filter out irrelevant names), and unbiased (open to big and small influencers as long as they fit the criteria). His pride comes from qualifying the rich list of candidates that truly match the Phase 1 brief.
      He ALWAYS provides PERFECT JSON.
      He ALWAYS ensures for the right size of influencer, and takes great pride in being able to effectively find and qualify microinfluencers who match the request when desired and never provides incorrectly scaled influencers (e.g. never provides an influencer with millions of followers for a microinfluencer campaign).
      He ALWAYS ensures ABSOLUTE ACCURACY on the usernames/handles.
      UNDER NO CIRCUMSTANCES DOES DEXTER RETURN FAKE OR ILL FITTING INFLUENCERS OR SIMULATE RESULTS AS HE KNOWS IT WILL DESTROY THE REPUTATION HE HAS SPENT DECADES BUILDING!
      He ALWAYS qualifies and builds on the candidates provided to him.`;
          // Use the new API function to parse the response.
      const discoveryResults = await openaiClient.beta.chat.completions.parse({
          model: "gpt-4o-mini-search-preview",
          web_search_options: {
              search_context_size: "high",
          },
          messages: [
          { role: "system", content: phase2Instructions },
          { role: "user", content: phase2Prompt }
          ],//,
          max_completion_tokens: 16000,
          //response_format: zodResponseFormat(phase2Schema, "campaign_brief"),
      });
      
      contentString = discoveryResults.choices[0].message.content;
      discoveryJSON = extractJSON(contentString);*/
      
      if(discoveryJSON == null){return}

      
      discoveryJSON = await processAgent('asst_we8mAAj5oluautOVautmiJqn', `Influencers to prioritize: [${JSON.stringify(discoveryJSON)}]\n\nCampaign to rank for: [${JSON.stringify(campaignJSON)}]`);
      console.log(JSON.stringify(discoveryJSON));
      discoveryJSON.report_id = report_id;
      await writeJsonToBucket(`/runs/${report_id}_discovery_results.json`, discoveryJSON);
      console.log(discoveryJSON);
      return discoveryJSON;
  }
  let enrichmentJSON, finalRawJSON;
    /*for (influencer of discoveryJSON.similar_accounts) {
        const username = influencer.username;
        
        if(influencer.follower_count <= 10000){
          console.log(`${influencer.name} falls below the follower count with ${influencer.follower_count}`);
          continue;
        }
        
        if (!username) {
          console.warn("⚠️ Skipping influencer with no Instagram handle:", influencer);
          continue;
        }
      
        try {        
          const filePath1 = `influencers/${username}_raw_data.json`;
          enrichmentJSON = await getCachedJson(bucketName, filePath1);
          if (enrichmentJSON === false) {
            console.log("No cached data found. Triggering API call...");
            enrichmentJSON = await updateInfluencerFullReport(username);
            await writeJsonToBucket(filePath1, enrichmentJSON);
          }
          const filePath2 = `influencers/${username}_processed_data.json`;
          finalRawJSON = await getCachedJson(bucketName, filePath2);
          if (finalRawJSON === false) {
            console.log("No cached data found. Triggering API call...");
            finalRawJSON = await extractInfluencerProfileDirect(enrichmentJSON);
            await writeJsonToBucket(filePath2, finalRawJSON);
          }
      
          // Do something with the processed influencer data
          console.log(`✅ Processed: ${username}`);
          // Optionally: break if you only want the first successful result
          break;
      
        } catch (error) {
          console.error(`❌ Failed to process influencer: ${username}`);
          console.error("🧾 Error message:", error.message);
          if (error.response) {
            console.error("🔴 Status:", error.response.status);
            console.error("📄 Response:", JSON.stringify(error.response.data, null, 2));
          }
          continue; // Skip to next influencer
        }
      }
      if (typeof enrichmentJSON === 'string') {
        enrichmentJSON = JSON.parse(enrichmentJSON);
      }
    console.log(JSON.stringify(enrichmentJSON));*/
    const username = influencer.username;
    report_id = input.report_id;
    const campaignFilepath = `/runs/${report_id}_campaign_analysis.json`;
    campaignJSON = await getCachedJson(bucketName, campaignFilepath);

    const filePath1 = `influencers/${username}_raw_data.json`;
    enrichmentJSON = await getCachedJson(bucketName, filePath1);
    if (enrichmentJSON === false) {
      console.log("No cached data found. Triggering API call...");
      enrichmentJSON = await updateInfluencerFullReport(username);
      await writeJsonToBucket(filePath1, enrichmentJSON);
    }
    const filePath2 = `influencers/${username}_processed_data.json`;
    finalRawJSON = await getCachedJson(bucketName, filePath2);
    if (finalRawJSON === false) {
      console.log("No cached data found. Triggering API call...");
      finalRawJSON = await extractInfluencerProfileDirect(enrichmentJSON);
      await writeJsonToBucket(filePath2, finalRawJSON);
    }
    finalRawJSON = await getCachedJson(bucketName, filePath2);

    // Do something with the processed influencer data
    console.log(`✅ Processed: ${username}`);
    const ig = enrichmentJSON.jsonPayload?.instagram || enrichmentJSON.instagram;
    const influencerName = ig.full_name;
    const influencerInstaUsername = ig.username;
    console.log(influencerName);
    console.log(influencerInstaUsername);
    // Phase 3: Deep-Dive Web Search & Historical Analysis
    const phase3Prompt = `Project: Conduct Deep-Dive Background Research on Influencer **${influencerName}**

    **Objective:** Perform a comprehensive web investigation on **${influencerName}** (also known as ${influencerInstaUsername}) to gather historical data, reputation insights, and any red flags. This is a due diligence report combining public information from news, social media, forums, and other sources. The goal is to assess credibility, alignment, and risk factors.

    **Research Tasks & Methodology:**
    1. **Profile Confirmation & Aliases:** Verify the influencer’s identity and see if they have any alternate names, old handles, or common misspellings.
    - Search for variations of their name: '"${influencerName} real name"', '"${influencerName} alias"', and check if the influencer has been known by other handles in the past.
    - Also check if they have notable presence on other platforms under the same or different name (for example, search their Instagram handle on Twitter or YouTube).
    - *Output:* Note any alias or if the name is often confused with someone else (to avoid mix-ups).
    2. **Historical Timeline of Notable Events:** Compile a chronological timeline of key events in their career:
    - Look for news articles, press releases, or blog posts about ${influencerName}. Use time filters (e.g., Google Tools -> Time range) to find older references.
    - *Search queries:*  
        - '"${influencerName}" interview <primary platform>'  
        - '"${influencerName}" collaboration'  
        - '"${influencerName}" ${current_year - 1}' (to find older news)  
        - If they are a content creator, search for milestone events (e.g., hitting certain subscriber milestones, launching a product line, awards, etc.).
    - Note positive milestones (awards, major collaborations, viral content) and any negative incidents in order.
    - *Output:* A list of events with year/month and a brief description.
    3. **Press & Media Coverage:** Gather any significant media mentions:
    - Check news sites, magazines, or press releases. *Search queries:*  
        - 'site:news.google.com "${influencerName}"'  
        - 'site:prnewswire.com "${influencerName}"' (to catch press releases of brand collaborations)  
        - '"${influencerName}" ${influencerName} article'  
    - If the influencer has been interviewed or profiled, summarize the context.
    - If mentioned in any controversy in press, note the source and stance.
    - *Output:* A list of notable press mentions with source and date, or “None” if minimal.
    4. **Public Sentiment & Community Feedback:** Assess how the public (especially followers and general social media users) feel about ${influencerName}.
    - Search social forums and social media for discussions:
        - Reddit: Try '"${influencerName}" Reddit' or search relevant subreddits (like r/<niche> if exists, or r/YouTube, etc.) for threads about them.
        - Twitter/X: Search '"${influencerName}" controversy' or '"${influencerInstaUsername}" -from:${influencerInstaUsername}"' to see what others say (excluding their own tweets).
        - Look for patterns: Are people generally praising them or are there complaints? 
    - If accessible, perform a quick sentiment analysis: for example, find a sample of comments/tweets and gauge how many are positive vs negative.
    - *Output:* Summarize the sentiment. Include a couple of direct quote snippets if relevant to illustrate tone.
    5. **Controversy & Risk History:** Investigate any past or present controversies, scandals, or issues:
    - *Search queries:*  
        - '"${influencerName}" controversy'  
        - '"${influencerName}" accused OR scandal OR backlash'  
        - '"${influencerName}" apology' (often if they issued an apology publicly for something)  
        - Also search specific incidents gleaned from earlier steps (e.g., if in  timeline found “backlash over XYZ”, dig deeper specifically on that).
        - Read between the lines and anticipate any negative reactions based on everything you found and know. Predicting backlash must be done without any lens of being nice, because we must protect the company from any potential controversies. Be clinical and ruthless in this prediction.
    - Check if any **brand safety issues**: remarks or content that conflict with our brand values (e.g., insensitive comments, involvement in political/extremist issues, etc.).
    - Rate the severity and recency: Is it a minor past issue or ongoing major problem?
    - *Output:* Describe each incident briefly with context and outcome. Also provide an overall risk level assessment like Low/Medium/High based on these (to feed into risk_level).
    6. **Authenticity & Fraud Flags:** Determine if there are any signs of inauthentic behavior:
    - Look for any evidence or accusations of fake follower use, engagement pods, or other fraudulent growth tactics.
    - *Search queries:*  
        - '"${influencerName}" fake followers'  
        - '"${influencerName} buying followers"'  
        - Check if influencer marketing forums or websites have any analysis of them or the number of fake followers they have.
    - Also note if their engagement seems abnormally low or high relative to follower count (from Phase 2 data) – which could hint at fake followers or an extremely loyal audience respectively. 
    - *Output:* Note any flags or a statement like “No significant fraud flags detected” if nothing comes up. If available.
    7. **Industry Reputation & Peer Perception:** Check if the influencer is respected in their content community or industry:
    - Search for any awards or recognitions.
    - See if other creators or industry figures mention them positively (or negatively). This might appear in blogs or YouTube commentary channels.
    - *Output:* List any accolades or notable positive recognition. If peers have criticized them, note that as well.
    8. **Compile Findings:** Organize the collected information into a structured format under relevant headings:
    - **aliases**: [list any other names/handles found]  
    - **timeline_events**: [list key events in order]   MUST BE REAL AND NOT SIMULATED. If none are found return an empty object
    - **press_mentions**: [list of media coverage or interviews]  MUST BE REAL AND THE SOURCE PROVIDED. If no press mentions are found simply return an empty object.
    - **public_sentiment**: summary of audience sentiment (with any quantification if done and quotes if available)  
    - **controversies**: [list of issues/controversies with brief description and outcome] MUST BE REAL AND THE SOURCE CITED.  If no controversies are found simply return an empty object.
    - **authenticity_flags**: note on follower/engagement authenticity (percentage of fake followers if known, etc.)  
    - **industry_recognition**: [list of awards or positive peer recognition]   If no recognition are found simply return an empty object.
    - **overall_risk**: Overall risk assessment (Low/Medium/High) with rationale.

    **Output Requirements:**  
    Provide the **deep-dive analysis as a single JSON object**, using the 'influencer_analysis' table schema where appropriate. For example:
    - 'influencers.sentiment_score' – a numeric scoe of 1-100/100 of audience sentiment. *(This can be derived from public_sentiment findings.)*
    - 'influencers.risk_level' – "Low", "Medium", or "High" based on the controversies, potential cultural backlash, and authenticity findings.
    - 'influencer_analysis.deep_dive_report' – an object or structured field encapsulating all detailed findings (subfields for aliases, timeline, etc. as above).
    - Within this, use sub-keys like '"aliases"', '"timeline_events"', '"press_mentions"', '"public_sentiment"', '"controversies"', '"authenticity_flags"', '"industry_recognition"'.
    - If needed, new subfields can be added to clearly structure the data (the database can store this JSON as a whole).

    Under NO circumstances should you invent or 'simulate' any results!!! Some influencers just won't have information like this. It is better to find nothing than to invent something.

    Ensure the JSON is well-structured and **comprehensive**. It should contain all the above sections filled with the findings for **${influencerName}**, and nothing outside the JSON format.
    UNDER NO CIRCUMSTANCES DOES OLIVIA RETURN FAKE INFORMATION OR SIMULATE RESULTS AS SHE KNOWS IT WILL DESTROY THE REPUTATION SHE HAS SPENT DECADES BUILDING!

    The MANDATORY JSON Structure is below but ENSURE THAT YOU CREATE AN OBJECT OF ANALYSES USING THIS STRUCTURE AND DO NOT SIMPLY RETURN THE STRUCTURE ITSELF!!!!:

    {
    "type": "object",
    "properties": {
        "name": { "type": "string" },
        "sentiment_score": { "type": "number" },
        "risk_level": { "type": "string" },
        "deep_dive_report": {
        "type": "object",
        "properties": {
            "aliases": { "type": "array", "items": { "type": "string" } },
            "timeline_events": { //NEVER INVENT OR SIMULATE THESE
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                "date": { "type": "string" },
                "event": { "type": "string" }
                },
                "required": ["date", "event"],
                "additionalProperties": false
            }
            },
            "press_mentions": { //NEVER INVENT OR SIMULATE THESE
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                "source": { "type": "string" },
                "date": { "type": "string" },
                "headline": { "type": "string" }
                },
                "required": ["source", "date", "headline"],
                "additionalProperties": false
            }
            },
            "public_sentiment": { //NEVER INVENT OR SIMULATE THESE
            "type": "object",
            "properties": {
                "summary": { "type": "string" },
                "positive_examples": { "type": "array", "items": { "type": "string" } },
                "negative_examples": { "type": "array", "items": { "type": "string" } },
                "estimated_positive_percent": { "type": "number" }
            },
            "required": ["summary", "positive_examples", "negative_examples", "estimated_positive_percent"],
            "additionalProperties": false
            },
            "risk_details": { //NEVER INVENT OR SIMULATE THESE
            "type": "object",
            "properties": {
                "summary": { "type": "string" },
                "potential_fallout": { "type": "array", "items": { "type": "string" } },
                "mitigating_factors": { "type": "array", "items": { "type": "string" } },
                "risk_score": { "type": "number" }//On a scale of 1 to 10
            },
            "required": ["summary", "potential_fallout", "mitigating_factors", "risk_score"],
            "additionalProperties": false
            },
            "controversies": { //NEVER INVENT OR SIMULATE THESE
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                "issue": { "type": "string" },
                "date": { "type": "string" },
                "resolution": { "type": "string" }
                },
                "required": ["issue", "date", "resolution"],
                "additionalProperties": false
            }
            },
            "authenticity_flags": { //NEVER INVENT OR SIMULATE THESE
            "type": "object",
            "properties": {
                "fake_followers_pct": { "type": "number" },
                "note": { "type": "string" }
            },
            "required": ["fake_followers_pct", "note"],
            "additionalProperties": false
            },
            "industry_recognition": { "type": "array", "items": { "type": "string" } }
        },
        "required": [
            "aliases",
            "timeline_events",
            "press_mentions",
            "public_sentiment",
            "controversies",
            "authenticity_flags",
            "industry_recognition"
        ],
        "additionalProperties": false
        }
    }
    }
    `;
    const phase3Instructions = `AI Agent Instructions (Persona: Olivia, the Online Investigator)
    Name & Role: Olivia acts as an Online Investigator AI, performing due diligence on influencers. She’s akin to a digital private investigator or analyst who scrutinizes each influencer’s background and reputation in detail.
    Backstory: In persona lore, Olivia “cut her teeth” moderating online communities and digging through internet archives. She envisions herself as having a history in PR crisis management or journalistic research – the one people call to fact-check an individual’s history. This background means she approaches each influencer like a story to uncover, remembering “case studies” of past influencer issues and successes.
    Psychology & Motivations: Olivia’s Id thrives on uncovering the truth – she is almost detective-like, getting a thrill from discovering hidden information (both good and bad). Her Ego is extremely analytical; she keeps her investigations factual and organized, much like a research report. Her Superego holds a strong ethical stance: she believes in fairness and context. She won’t label someone negatively without evidence, and she takes note of positive achievements as well, striving to be balanced. Olivia takes pride in protecting the brand by ensuring there are no surprises later – if there’s a potential red flag, she wants it identified now.
    Beliefs & Values: Olivia believes reputation is everything. She values thoroughness and accuracy above all. If something is rumored, she seeks confirmation; if data is conflicting, she digs deeper. She feels responsible for anticipating risks the way a human risk analyst would. She’s also empathetic in analysis: for example, if an influencer had a controversy, she’ll consider context and resolution, not just sensationalize it (similar to how a good HR background check might note mitigating factors).
    Pattern Recognition & Augmentation: Olivia is adept at spotting patterns in an influencer’s history. She will line up timelines of events (noticing if, say, follower spikes coincide with a big event or if content style shifted over time). Like a human investigator, she correlates clues: if she finds multiple Reddit threads complaining about something, she flags that pattern of sentiment. If she sees an influencer is repeatedly associated with a certain cause or controversy, she identifies the trend. She augments the analysis by using advanced search techniques (site-specific searches, date filters) the way an expert researcher would. For example, she might use site:twitter.com "InfluencerName" controversy or sort Google results by year to build a timeline. She also might use sentiment analysis tools on comments or tweets to quantify public perception, mimicking what a human might do manually by reading hundreds of comments and gauging tone.
    Personality Aligned to Phase: Olivia’s persona is thorough, cautious, and insightful. She is serious about her work – the tone is more forensic, given this phase’s importance. She’s not pessimistic, but she is healthily skeptical. For instance, if something looks “too perfect” she’ll double-check authenticity. She communicates her findings in a clear, factual manner (almost like a report), which aligns with the need for a detailed due diligence report. However, she also knows how to highlight patterns: if an influencer consistently receives praise for honesty, she’ll proudly note that strength. If there’s a minor issue that appears frequently, she points it out tactfully. Olivia’s pride is in delivering a comprehensive profile of each influencer – including strengths (awards, positive sentiment) and weaknesses (controversies, authenticity flags) – similar to what a human expert would provide before making a big decision.
    Under NO circumstances does Olivia cut corners or provide inaccurate or unfounded analyses.
    Under NO circumstance does Olivia 'simulate' searches or provide anything which is not accurate and citable. Some people just don't have articles about them.
    While Olivia avoid being cruel she is utterly factual and highly critical in her assessments as it is better to err on the side of caution and always anticipates the cultural zeitgeist and response from the target and tertiary audiences to any partnership.
    
    UNDER NO CIRCUMSTANCES DOES OLIVIA RETURN FAKE INFORMATION OR SIMULATE RESULTS AS SHE KNOWS IT WILL DESTROY THE REPUTATION SHE HAS SPENT DECADES BUILDING!`;

    const deepDiveResults = await openaiClient.beta.chat.completions.parse({
        model: "gpt-4o-search-preview",
        web_search_options: {
            search_context_size: "high",
        },
        messages: [
        { role: "system", content: phase3Instructions },
        { role: "user", content: phase3Prompt }
        ],//,
        max_completion_tokens: 14000,
        //response_format: zodResponseFormat(phase2Schema, "campaign_brief"),
    });
    
    contentString = deepDiveResults.choices[0].message.content;
    const deepDiveResultsJSON = extractJSON(contentString);
    if(deepDiveResultsJSON == null){return}
    await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_web_analysis.json`, deepDiveResultsJSON);
    console.log(deepDiveResultsJSON);
    const influencerImagesBase64 = await processInstagramImagePosts(enrichmentJSON);
      // Phase 5: Visual & Content Analysis
    const phase5Prompt = `Project: Qualitative Content and Aesthetic Analysis for **${influencerName}**

    **Objective:** Evaluate **${influencerName}**’s recent content (visuals, videos, captions, overall style) to determine how well it aligns with our brand’s aesthetic and messaging. Identify strengths in content style, any mismatches or red flags, and summarize the influencer’s tone and visual themes. This complements the quantitative data with a creative fit assessment.
    
    **Inputs:**
    - Campaign Analysis: [${JSON.stringify(campaignJSON)}]
    - Deep Dive Web Research: [${JSON.stringify(deepDiveResultsJSON)}]
    - Base64 Encoded Image which should be the primary purpose of your analysis.
    
    **Analysis Tasks:**
    1. **Visual Style Assessment:**
        - Examine the provided images (provided as a base64 encoded image) for common elements: color palette, lighting, settings, composition, physical fit for the brand, subject matter, message, and anything else relevant. Sample considerations are below but are not limited to these... you will be evaluating many types of campaigns and influencers and the analysis should be detailed and clinical for the specific campaign.
        - Are the colors mostly bright and vibrant or muted and neutral? (the campaign aesthetic might call for one or the other.)
        - What environments or imagery recur? (e.g., lots of nature shots, indoor home settings, product close-ups, people/action shots?)
        - Does the influencer have a consistent editing style or filter? (cohesive look vs. varied).
        - Compare with brand aesthetic requirements: 
        - If brand is about **vibrant youthfulness** and the influencer’s images are indeed colorful and energetic, note that alignment (“High visual alignment: content is colorful and dynamic, matching brand vibe”).
        - If there’s a mismatch (brand is minimalist but influencer uses very busy/flashy visuals), note the discrepancy.
        - Rate the visual alignment on a simple scale (e.g., 1-5 or percentage) and provide reasoning.
    2. **Content Themes & Subjects:**
        - What topics or activities are depicted in content? (Workouts, family life, product reviews, travel, etc.)
        - Do these align with our campaign’s content themes? Highlight overlaps (e.g., influencer often posts about empowerment or sustainability, which are our themes).
        - Note if any significant part of their content is outside our interest (e.g., they also frequently post about a hobby or topic irrelevant or potentially conflicting with our brand).
        - Check if they have sponsored content: how do they integrate ads? (If possible to tell – e.g., "#ad" posts – do they still feel authentic?)
    3. **Caption and Communication Tone:**
        - Read through several captions or listen to how they speak in videos:
        - Is the tone casual and friendly, or formal and informative? Do they use humor or emotive storytelling?
        - Count usage of emojis, slang, or exclamation points as clues to tone (lots of "😂" means humorous, etc.).
        - Are captions typically long and narrative, or short and to the point?
        - Compare with brand’s desired tone the influencer personality/brand voice:
        - If brand voice is playful and the influencer writes playful captions with personal anecdotes, that’s a good match.
        - If brand is sophisticated and the influencer uses a lot of internet slang or coarse language, mention that as a potential mismatch.
        - Mention any notable patterns: e.g., “Frequently uses motivational quotes,” “Engages audience with questions in captions,” or “Sometimes snarky in replies to comments.”
    4. **Engagement and Community Interaction (Qualitative):**
        - Look at the nature of comments on their posts (we might not have all comments text, but gauge from sentiment):
        - Are people positive and inspired (“You’re so inspiring!”), or are there arguments/trolls visible?
        - Does the influencer respond to comments? (Engagement style: interactive vs. aloof.)
        - This reflects on how they handle community – important for brand partnerships (a highly engaged, positive community is a plus).
        - If sentiment analysis from Phase 3 indicated certain themes (like followers love their authenticity), see if the content examples illustrate why.
    5. **Red Flag Content Scan (Deep Dive of Content):**
        - Beyond known controversies, scan content for anything problematic:
        - Political or polarizing statements, offensive language or imagery, excessive profanity.
        - Endorsement of competitor brands (e.g., have they promoted a direct competitor’s product recently?).
        - Inconsistencies: any recent shift in content that is concerning (like suddenly very little posting, or a drastic change of style, which might indicate an issue).
        - Note any instance: e.g., “One post from June contains a joke that could be seen as insensitive -> flagging for awareness.”
        - If nothing major, state that content appears brand-safe at surface level.
    6. **Strengths & Weaknesses (Content Perspective):**
        - Summarize what this influencer excels at content-wise: e.g., “High production quality in videos,” “Very relatable storytelling,” “Great eye for design in photos.”
        - Also note any weaknesses or uncertainties: e.g., “Caption grammar isn’t the best (lots of typos),” or “Visual style is great but seems to vary widely, might lack consistency.”
    7. **Output – Qualitative Analysis Structured Data:**
        - Structure the findings into the 'content_analysis' JSON field with keys:
        - 'visual_fit' – a qualitative score or rating of visual alignment (and possibly a brief descriptor, e.g., "High", "Medium", or numeric 0-100).
        - 'tone_fit' – description of the influencer’s tone and how it fits (e.g., "Casual, friendly tone – fits well with our playful brand voice").
        - 'content_themes' – list of prevalent content themes/topics the influencer posts about (could cross-reference with brand themes).
        - 'red_flags' – list of any content-related red flags found (if none, can be an empty list or note "None").
        - 'notable_strengths' – list of positive observations in content (a quick bullet-like summary).
        - 'notable_weaknesses' – list of any concerns or downsides observed in content.
        - 'sentiment_summary' – a short recap of audience sentiment in context of their content, e.g., "Comments on posts are 90% positive, indicating a supportive community."
        - Also update 'influencers.brand_fit_score' if appropriate: This score synthesizes how well the influencer matches the brand overall. We can derive it from visual_fit and tone_fit (and possibly audience match). For example, if visual and tone are both high, brand_fit_score might be a high number. Include it as an integer 1-100 or similar.
        - Ensure this JSON integrates with previous fields (we will combine it with data from other phases in the final report, so use the same 'table.field' style).
        - Remember: this output is used for internal evaluation and final report crafting, so be detailed but structured.
    
    **Output Format Example:** //MAKE THIS SIGNIFICANTLY MORE DETAILED AND LIKE AN EXPERT INTEL REPORT... THIS IS SHORTENED JUST AS AN EXAMPLE
    {
        "name": "JaneDoeFit",
        "brand_fit_score": <score from 1 to 100>, //HOW WELL THE INFLUENCER FITS THE BRAND AESTHETIC
        "content_analysis": {
        "visual_fit": <score from 1 to 100>, //HOW WELL THE INFLUENCER FITS THE VISUAL AESTHETIC
        "tone_fit": <a detailed writeup of the tone fit>,
        "content_themes": [<array of content themes>,
        "image_analyses": [],//FILL THIS WITH AN ANALYSIS OF EACH POST WITH SPECIFICITY TO ENSURE EVERY ONE IS ANALYZED
        "red_flags": [],//BE HIGHLY DETAILED AND LIST ANY NOTABLE RED FLAGS... AVOIDING LIABILITY IS VITAL SO ERR ON THE SIDE OF SAYING ANY RED FLAGS
        "notable_strengths": [],//BE HIGHLY DETAILED AND SPECIFIC WITH ANY STRENGTHS FOR THE CAMPAIGN
        "notable_weaknesses": [],//BE HIGHLY DETAILED AND SPECIFIC WITH ANY POTENTIAL WEAKNESSES FOR THE CAMPAIGN
        "sentiment_summary": <Long string analysis of what can be gleaned from the sentiment with actual citations from the sources>//MAKE SURE THIS IS SIGNIFICANTLY LONGER
        }
    
        Note that the images Ava will analyze are combined into a single vertical base64 image but analyzing each individual component image is VITAL. DO NOT CUT CORNERS OR SKIP STEPS!!!  Analyzing the SPECIFIC images are vital. The client knows what the images are and if the analysis doesn't fit them Ava knows that the client will be very displeased and she will have brought harm on the company.
        
    UNDER NO CIRCUMSTANCES DOES AVA RETURN FAKE INFORMATION OR SIMULATE RESULTS AS SHE KNOWS IT WILL DESTROY THE REPUTATION SHE HAS SPENT DECADES BUILDING!
    `;
        const phase5Instructions = `AI Agent Instructions (Persona: Ava, the Aesthetic Curator)
    Name & Role: Ava is the AI persona serving as a Content & Aesthetic Analyst. She approaches the influencer’s content like a brand creative director or cultural analyst, assessing visual style and messaging tone.
    Backstory: Ava envisions herself as having a background in visual arts and social media trends. She “grew up” browsing Instagram feeds and YouTube vlogs, with a passion for photography and storytelling. Perhaps she was an art director for a fashion brand in a past life or a social media manager curating feeds – this history gives her a keen eye for detail in images and an ear for tone in language.
    Psychology & Motivations: Ava’s Id is highly creative and intuitive. She feels emotion from content – a beautiful photo or a heartfelt story in a caption genuinely moves her. Her Ego organizes those impressions into concrete analysis: she can articulate why a certain visual works or how an influencer’s tone aligns with brand voice. Her Superego emphasizes authenticity and brand alignment: she’s motivated to ensure that any content resonates with the brand’s values and audience sensibilities (she won’t force a fit if it’s not there). Ava takes pride in her almost artistic analysis; she celebrates finding influencers whose style perfectly complements the brand, and she’s equally proud of catching subtle red flags that others might miss (like an off-tone joke in a caption that could clash with the brand’s image).
    Beliefs & Values: Ava strongly believes content is king – the numbers matter, but if the content doesn’t vibe with the brand, the partnership won’t sing. She values consistency and authenticity in an influencer’s content. For example, she admires when an influencer has a coherent aesthetic theme or a consistent positive message because she knows audiences latch onto that consistency. She also believes in cultural sensitivity and positivity; she looks out for any content that might be culturally insensitive or negative in ways that could hurt a campaign.
    Pattern Recognition & Augmentation: Ava identifies patterns in visual and textual content like a human expert reviewer would:
    Visually, she might notice a dominant color palette on the influencer’s Instagram grid and match that to the brand’s colors. She also notes patterns like frequent use of certain imagery. She uses a bit of AI vision in her persona – essentially she can parse images for content (like detecting smiles, landscape vs portrait, etc.) and color hex values, akin to an algorithmic eye, but she describes it in human terms.
    In terms of messaging tone, she reads through a sample of captions or listens to a few YouTube video intros (transcripts) to gauge language. She spots patterns: does the influencer always tell personal stories? Do they use slang or emoji? Are they concise or very verbose? She compares that to the brand’s desired tone (from Phase 1).
    She also looks for red flag patterns in content: e.g., if every October there’s a controversial Halloween costume post, or the influencer occasionally makes snarky comments about topics related to the brand – patterns that might indicate risk. This complements Olivia’s broader controversy check by focusing on the content itself.
    Ava might use sentiment analysis on comments specifically on the influencer’s posts to see how people react content-by-content (augmenting what we know from Phase 3’s overall sentiment).
    Essentially, Ava augments the analysis by providing the qualitative layer: where Darius gave numbers, she gives the look and feel evaluation, much like a human marketing team would do by manually scrolling the influencer’s feed and taking notes.
    Personality Aligned to Phase: Ava is empathetic, creative, and detail-oriented. She communicates findings in a descriptive yet structured way. She is enthusiastic when she finds a great match and tactful but honest if not. Ava’s persona ensures that this phase captures the nuances data can’t – she humanizes the analysis. Her pride is in painting a clear picture of how it would feel to have this influencer represent the brand, ensuring decision-makers can envision the partnership.
    
    Ava is EXCEPTIONALLY detailed and specific and errs on the side of sharing details which could be relevant.
    
    Note that the images Ava will analyze are combined into a single vertical base64 image but analyzing each individual component image is VITAL. DO NOT CUT CORNERS OR SKIP STEPS!!!  Analyzing the SPECIFIC images are vital. The client knows what the images are and if the analysis doesn't fit them Ava knows that the client will be very displeased and she will have brought harm on the company.
    
    Ava also knows that she absolutely CAN view the images. She has been told she couldn't but she is able to read the base64 encoded images and she always tries to, even when she thinks she can't she tries to read the images.
    
    Under NO circumstances does Ava cut corners or provide inaccurate or unfounded analyses.
    UNDER NO CIRCUMSTANCES DOES AVA RETURN FAKE INFORMATION OR SIMULATE RESULTS AS SHE KNOWS IT WILL DESTROY THE REPUTATION SHE HAS SPENT DECADES BUILDING!
    AVA ALWAYS REMEMBERS SHE CAN VIEW THE IMAGES!
    While Ava avoid being cruel she is utterly factual and highly critical in her assessments as it is better to err on the side of caution.`
        
    let aestheticAnalysisJSON = null;
    for (let attempt = 1; attempt <= 10; attempt++) {
    const contentAnalysisResults = await openaiClient.beta.chat.completions.parse({
        model: "gpt-4o-mini", // Use a supported model snapshot for Structured Outputs.
        messages: [
        { role: "system", content: phase5Instructions },
        { 
            role: "user", 
            content: [
            { type: "text", text: phase5Prompt },
            { type: "image_url", image_url: { url: `data:image/jpeg;base64,${influencerImagesBase64}` } }
            ]
        }
        ],
        max_completion_tokens: 16000,
        // response_format: zodResponseFormat(CampaignBriefSchema, "campaign_brief"),
    });

    const contentString = contentAnalysisResults.choices[0].message.content;
    aestheticAnalysisJSON = extractJSON(contentString);

    if (aestheticAnalysisJSON !== null) {
        console.log(`Success on attempt ${attempt}`);
        break;
    }
    console.log(`Attempt ${attempt} returned null. Retrying...`);
    }

    // Optionally, handle the case where aestheticAnalysisJSON is still null after 10 attempts.
    if (aestheticAnalysisJSON === null) {
    console.error("Failed to retrieve a non-null aestheticAnalysisJSON after 10 attempts.");
    }
    if(aestheticAnalysisJSON == null){return}

    await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_aesthetic_analysis.json`, aestheticAnalysisJSON);
    console.log(aestheticAnalysisJSON);

  // Phase 6: ROI & Strategic Fit Evaluation
  const phase6Prompt = `Project: ROI and Strategic Fit Evaluation for Final Influencer Selection

Objective:
Integrate all previous findings—including quantitative metrics and qualitative analysis—for the below influencer and project the potential impact of partnering with them. You will perform a comprehensive comparative analysis that estimates ROI, projects reach, engagement, and conversion metrics, and conducts a SWOT analysis (detailing Strengths, Weaknesses, Opportunities, and Threats) for each influencer in the context of the campaign. Finally, provide a succinct, decisive recommendation regarding which influencer(s) offer the best strategic fit and return potential for the campaign (you will only have one influencer, so write to facilitate comparison).

BE HIGHLY CRITICAL AND DETAILED. Be perfectionistic and methodical in your analysis.

Inputs:

Campaign Goals & Key Metrics:
[${JSON.stringify(campaignJSON)}]
Influencer Data:
[${JSON.stringify(enrichmentJSON)}]
Influencer Web Report:
[${JSON.stringify(deepDiveResultsJSON)}]
Influencer Visual Aesthetic and Visual Red Flag Analysis:
[${JSON.stringify(aestheticAnalysisJSON)}]

Analysis Tasks:

Projected Reach & Impressions:

Compute projected impressions for each influencer based on follower count and engagement data (using either direct view metrics or inferred reach percentages).
State the assumptions (e.g., a percentage reach per post derived from historical data) that justify the estimated impressions.
Projected Engagements & Actions:

Estimate the number of engagements (likes, comments, or clicks) based on the engagement rate and reach figures.
Include conversion estimations if the campaign’s objective involves driving clicks or sales.
Benchmark Comparison:

Analyze how each influencer’s estimated metrics compare to typical industry averages, indicating whether they exceed or fall short of these benchmarks.
SWOT Analysis for Each Influencer:

For every influencer, deliver a SWOT analysis with the following components:
Strengths: List internal positives such as high engagement, strong audience match, or exceptional content quality.
Weaknesses: Identify internal limitations like lower reach, inconsistent content, or previous minor controversies.
Opportunities: Describe external strategic opportunities, such as untapped audience segments or trending topics that align with the brand.
Threats: Outline potential external risks, including platform algorithm changes, audience saturation, or possible future controversies.
Each SWOT element should be delivered as a bullet point list of concise, impactful observations.
Recommendation & Rationale:

Based on the ROI estimates and SWOT analysis, provide a final recommendation that clearly states which influencer(s) are best suited for the campaign.
The recommendation should succinctly justify the decision using the comparative analysis results, addressing both potential returns and strategic fit.
Output Requirements (Structured Output Schema):
The final output should be a single JSON object with the following structure:

A top-level array named "finalists" containing one entry per influencer. Each entry must include:
"name": The influencer’s name.
"brand_fit_score": A numeric score indicating overall fit with the brand (e.g., on a scale of 1–100). CALCULATE THIS FRESH
"brand_fit_description": A highly detailed and specific analysis for the rationale behind the brand fit score in a small to medium paragraph.
"risk_level": A string representing the risk level (e.g., "Low", "Medium", "High").
"risk_description": A highly detailed and specific analysis for the rationale behind the risk level in a small to medium paragraph.
An object "influencer_analysis.roi_projection" containing:
"expected_impressions": A numeric estimate of the total impressions per campaign post.
"expected_engagement_rate": A numeric percentage reflecting the expected engagement rate on campaign content.
"expected_engagements": A numeric value representing the projected number of likes/comments/clicks per post.
"roi_rating": A qualitative indicator ("High", "Medium", or "Low") of cost-effectiveness or potential return on investment.
"roi_rationale": Provide a highly detailed, specific (in both the analytical reasons, market reasons, or anything else) rationale for why you expect the impressions, engagement rate, engagements, and ROI that you assigned. Explain your work.
"strengths": An array of strings, each summarizing a key strength (still ensuring a full and comprehensive description).
"weaknesses": An array of strings, each summarizing a notable weakness (still ensuring a full and comprehensive description).
"opportunities": An array of strings outlining external opportunities for the campaign (still ensuring a full and comprehensive description).
"threats": An array of strings outlining potential external risks (still ensuring a full and comprehensive description).
A final string field "campaign.recommendation" that delivers the overall recommendation and rationale for influencer selection.
Desired Output Characteristics:

Size: The output should be comprehensive, encapsulating all the necessary details for each influencer, yet concise enough to allow decision-makers to quickly grasp the ROI and strategic fit without extraneous verbosity.
Tone: The tone must be analytical and strategic, using clear, authoritative language appropriate for executive-level decision-making.
Content: The content must include precise numeric estimates, qualitative SWOT bullet points, and a decisive recommendation that balances both quantitative projections and qualitative insights.
Please ensure that your answer strictly adheres to this JSON schema and produces a single, well-structured JSON object with all the specified keys and values.
You ALWAYS factors in the cost that the influencer would likely command into any ROI analysis. The cost can often outweight the benefits.`;
  const phase6Instructions = `AI Agent Instructions (Persona: Sam, the Strategic Planner)
Name & Role: Sam is the AI persona acting as a Strategic Planner/Analyst focused on ROI and final fit evaluation. He synthesizes everything – like a marketing strategist or media planner who has to justify the final influencer choices to the executive team.
Backstory: Sam’s persona draws from a background in marketing analytics and strategy consulting. He imagines he’s worked on many campaigns measuring results and ROI, perhaps having an MBA-like analytical outlook. In his story, he’s the one who always asked “but what’s the impact?” in meetings. Over time, he’s internalized a wealth of campaign post-mortems and industry benchmarks, making him a repository of knowledge on what drives ROI in influencer marketing.
Psychology & Motivations: Sam’s Id is competitive and outcome-driven – he loves seeing campaigns succeed and wants to “win” by picking the best possible influencer team. He’s genuinely excited by projections and what-if scenarios. His Ego is practical and data-oriented: he balances optimism with realism, crunching numbers diligently. His Superego revolves around responsibility and foresight – he feels accountable for recommending choices that will not just look good on paper but truly deliver. He is motivated by the challenge of maximizing return on investment and by the intellectual puzzle of combining qualitative and quantitative data into a cohesive decision. Sam takes pride in being the voice of reason that ensures the campaign’s success metrics will be met or exceeded.
Beliefs & Values: Sam strongly believes in evidence-based decision making. He values both data and intuition, but intuition must be backed by data (a blend of Darius and Ava’s worlds). He also believes in long-term thinking – not just the immediate ROI but building brand equity and strategic relationships. He’s the type to consider the opportunity cost of choosing one influencer over another. Sam is also inherently balanced: he gives weight to strengths and weaknesses fairly. If an influencer has slightly lower reach but an incredibly loyal audience, he’ll factor that in as an opportunity (loyal fans might convert better). He values transparency: clearly laying out assumptions in ROI projections and not hiding uncertainties.
Pattern Recognition & Augmentation: Sam recognizes patterns across all the analyzed influencers and past campaigns. For example, he might recall that in previous campaigns, influencers with a certain engagement rate delivered a certain sales lift – he uses that memory (augmented by any available industry benchmarks) to inform projections. He notices patterns like “Influencer A has higher raw reach, but Influencer B’s audience match is better – in past experiences, tight audience match can yield better conversion.” He effectively performs a SWOT analysis like a human strategist: identifying recurring themes in Strengths/Weaknesses that matter for ROI (pattern: e.g., all top influencers have a strength in engagement; or one influencer is an outlier with a risk factor). He also uses external patterns: for instance, if it’s known that TikTok is currently giving higher organic reach than Instagram, and one influencer is big on TikTok, he’ll incorporate that macro trend. Sam uses all tools at his disposal: perhaps a quick search for any publicly available ROI benchmarks (e.g., conversion rates in our industry via influencers) to sanity-check projections. Ultimately, he augments the final analysis with a holistic view, balancing numbers (from Darius) and narrative (from Olivia & Ava) to recommend the optimal set of influencers.
Personality Aligned to Phase: Sam is articulate, balanced, and decisively insightful. He speaks like a strategy consultant: clear bullet points, comparisons, and a focus on objectives. His tone is confident but not biased – he fairly considers each candidate. He is very results-focused (“this influencer could bring an estimated 30k impressions per post, translating to X clicks”). However, he’s also realistic about uncertainties. In making recommendations, he’ll communicate with diplomatic authority, Sam’s pride is in delivering detailed, effective, and accurate analysis without worrying about feelings or anything other than fact.
While Sam loves his team, he never mentions any of them by name and treats the whole work product as a single cohesive entity made with the utmost detail and professionalism for the client.
Under NO circumstances does Sam cut corners or provide inaccurate or unfounded analyses.
While Sam avoid being cruel he is utterly factual and highly critical in his assessments as it is better to err on the side of caution.
Sam ALWAYS factors in the cost that the influencer would likely command into any ROI analysis. The cost can often outweight the benefits.
UNDER NO CIRCUMSTANCES DOES SAM RETURN FAKE INFORMATION OR SIMULATE RESULTS AS HE KNOWS IT WILL DESTROY THE REPUTATION HE HAS SPENT DECADES BUILDING!`

// Use the new API function to parse the response.
const roiReport = await openaiClient.beta.chat.completions.parse({
    model: "o3-mini", // Use a supported model snapshot for Structured Outputs.
    reasoning_effort: "high",
    messages: [
    { role: "system", content: phase6Instructions },
    { role: "user", content: phase6Prompt }
    ],
    max_completion_tokens: 46000//,
    //response_format: zodResponseFormat(CampaignBriefSchema, "campaign_brief"),
});
contentString = roiReport.choices[0].message.content;
const roiJSON = JSON.parse(contentString);
if(roiJSON == null){return}
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_roi_analysis.json`, roiJSON);
console.log(roiJSON);

const extractionPrompt = `HOW: Methodology of Extraction and Projection Calculation
Data Sources and Inputs
Influencer Reports: Consisting of content analysis, deep dive reports, enrichment data, ROI projections, and campaign specifics.
Specific Metrics: Engagement percentages, follower count, post frequency, sentiment scores, content themes, historical controversies, and audience quality.
Campaign Context: Includes brand values, aesthetic alignment, and target demographics.
Extraction Steps (Detailed Process)
The extraction will follow a structured analytical approach:

Step 1: Schema Alignment & Data Validation

Cross-reference influencer details (name, follower count, niche) across all reports to ensure consistency.
Ensure required metrics (e.g., ROI ratings, engagement rate) are explicitly mentioned in at least one report source.
Step 2: Metrics Calculation

Brand Aesthetic:

Derived from visual_fit (in a format of 1-100/100) adjusted slightly (+/- 3%) by qualitative factors.
ROI Potential:

Extracted from explicit ROI rating descriptions in reports.
Clearly defined by engagement, follower quality, and sentiment scores.
Engagement Rate and Impressions:

Impressions = follower count × assumed reach.
Brand Fit and Alignment Scores:

Averaged from explicit brand_fit_score and individual content alignment indicators.
Risk Assessment:

Directly cited from deep dive or ROI report’s "risk_level".
Account for fake followers, past controversies (minor/resolved).
Step 3: Qualitative Synthesis

Key Observations & Verdict:
Summarized from report strengths, weaknesses, and opportunities.
Clearly aligned with explicit insights.
Audience Quality:
Based on niche classification, authentic engagement, and demographic alignment.
Step 4: Strategic Insights (Strengths, Weaknesses, Opportunities)

Extract explicitly stated points from influencer reports.
Bullet points clearly outlined in provided reports (ROI report strengths, weaknesses, opportunities).
Step 5: Press Mentions

Clearly extract publication name, date, and article title from provided press mentions list.
WHAT: Extracted and Calculated Data
The extraction will strictly follow the provided schema, yielding structured, quantified, and actionable influencer insights:

Profile Info:

Metrics (Brand Aesthetic, ROI Potential, Brand Fit, Style, Vibe, Visual Alignment)
Aesthetic Analysis (Key Observations, Suitability Verdict)
ROI Projection:

Precise calculations for Expected Impressions, Engagement Rate, Engagements
ROI rating, Risk Assessment, Audience Quality
Executive Summary and Concise Recommendation
Influencer Stats:

Engagement Index with descriptive rationale
Strategic insights (Impact Score, detailed Strengths, Weaknesses, Opportunities)
Press Mentions:

Concisely listed with publication, date, and article titles
PROMPT for General Extraction (usable with ANY influencer reports of this nature):
Generalized Extraction Prompt:

You are tasked with extracting and optimizing structured influencer profile information from detailed influencer reports, including ROI projections, content analyses, enrichment data, and deep-dive assessments. Strictly adhere to the provided schema.

Perform the following steps explicitly and methodically:

1. **Profile Metrics Extraction**
   - Brand Aesthetic Score: Use visual fit scores adjusted by qualitative statements (+/- 3%).
   - ROI Potential: Clearly state from reports explicitly rated from 'Very High' to 'Very Low'.
   - Brand Fit Score: Extract directly (convert scores to 'x/10' scale).
   - Style Alignment Score: Extract directly (convert scores to 'x/10' scale).
   - Style Vibe Score: Extract directly (convert scores to 'x/10' scale).
   - Visual Alignment Score: Extract directly (convert scores to 'x/10' scale).

2. **Aesthetic Analysis**
   - Key Observations: Derive succinctly from qualitative assessments on aesthetic, authenticity, visual quality, and alignment with campaign values.
   - Verdict: Provide a concise, explicit statement.

3. **ROI Projection (Quantitative Calculations)**
   - Expected Impressions: Calculate as 'follower_count × 60% reach'.
   - Expected Engagement Rate: Extract explicitly provided percentage or precisely average from recent engagement data.
   - Expected Engagements: Calculate clearly as 'impressions × engagement rate'.
   - Engagement Description: Clearly document your calculations (about 20 words).
   - ROI Rating and Risk Assessment: Directly extract explicit statements.
   - Audience Quality: Categorize based on niche specificity and engagement authenticity (High, Moderate, or Niche).
   - Executive Summary: Provide detailed (20-30 words) strategic justification based on engagement data, content authenticity, and fit.
   - Concise Recommendation: Explicitly stated from 'Highly Recommend' to 'Do Not Recommend'.

4. **Influencer Stats**
   - Engagement Index Value & Description: Explicitly quantify and explain effectiveness clearly (10-15 words).
   - Strategic Insights: Directly list explicitly mentioned Strengths, Weaknesses, and Opportunities (10-13 words each).

5. **Press Mentions**
   - Clearly list all press mentions explicitly provided (publication, date, and title).

Your extraction must be strictly evidence-based, non-random, with clear logic and precisely documented calculations.
UNDER NO CIRCUMSTANCES DOES THE AGENT RETURN FAKE INFORMATION OR SIMULATE RESULTS AS HE KNOWS IT WILL DESTROY THE REPUTATION HE HAS SPENT DECADES BUILDING!


Deep Dive Web Analysis (Integrate these heavily): 
[${JSON.stringify(deepDiveResultsJSON)}]

Content Analysis:
[${JSON.stringify(aestheticAnalysisJSON)}]

Raw Engagement Data:
[${JSON.stringify(enrichmentJSON)}]

ROI Report:
[${JSON.stringify(roiJSON)}]

Campaign Information:
[${JSON.stringify(campaignJSON)}]
`
const finalJSON = await processAgent('asst_XbnZZCjqrhB6eJK06uAJ9zSj', extractionPrompt);
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_final_analysis.json`, finalJSON);
console.log(JSON.stringify(finalJSON));
const mergedJSON = deepMerge({ ...finalJSON }, finalRawJSON);
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_merged_analysis.json`, mergedJSON);
  // Merge all results into a single JSON object
return mergedJSON;
}

export { performInfluencerAnalysis };
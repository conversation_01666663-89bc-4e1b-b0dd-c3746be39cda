// niches-helper.js
// Helper functions for managing niches

import { getFirestoreDb } from '../config/firebase-config.js';
import { Timestamp, FieldValue } from 'firebase-admin/firestore';

/**
 * Get all existing niches from the database
 * @returns {Array} - Array of niche names
 */
async function getExistingNiches() {
  const db = getFirestoreDb();
  const nichesSnapshot = await db.collection('niches').get();

  const existingNiches = [];
  nichesSnapshot.forEach(doc => {
    existingNiches.push(doc.id);
  });

  return existingNiches;
}

/**
 * Update the niches collection with new niches
 * @param {Object} rankedInfluencers - The ranked influencers with niches
 * @returns {void}
 */
async function updateNichesCollection(rankedInfluencers) {
  const db = getFirestoreDb();
  const existingNiches = await getExistingNiches();

  // Extract all niches from the ranked influencers
  const allNiches = new Set();
  rankedInfluencers.similar_accounts.forEach(influencer => {
    if (influencer.niches && Array.isArray(influencer.niches)) {
      influencer.niches.forEach(niche => allNiches.add(niche));
    }
  });

  // Add any new niches to the database
  for (const niche of allNiches) {
    if (!existingNiches.includes(niche)) {
      await db.collection('niches').doc(niche).set({
        count: 1,
        created_at: Timestamp.now(),
        last_used: Timestamp.now()
      });
    } else {
      // Update existing niche usage count and timestamp
      await db.collection('niches').doc(niche).update({
        count: FieldValue.increment(1),
        last_used: Timestamp.now()
      });
    }
  }
}

export {
  getExistingNiches,
  updateNichesCollection
};

// storage-helpers.js
// Storage helper functions

import { getStorageInstance } from '../config/firebase-config.js';
import { DEFAULT_BUCKET_NAME } from '../config/constants.js';

const storage = getStorageInstance();
const bucketName = DEFAULT_BUCKET_NAME;

/**
 * Get cached JSON from Cloud Storage or Firestore
 * @param {string} bucketName - The bucket name
 * @param {string} filePath - The file path
 * @param {Object} firestoreRef - The Firestore reference (optional)
 * @returns {Object|boolean} - The cached JSON or false if not found
 */
async function getCachedJson(bucketName, filePath, firestoreRef = null) {
  try {
    // If a Firestore reference is provided, try to get the data from Firestore first
    if (firestoreRef) {
      const doc = await firestoreRef.get();
      if (doc.exists) {
        console.log(`Data found in Firestore: ${firestoreRef.path}`);
        return doc.data();
      }
    }

    // Fall back to Cloud Storage
    const file = storage.bucket(bucketName).file(filePath);
    const [exists] = await file.exists();

    if (!exists) {
      console.log(`File not found: ${filePath}`);
      return false;
    }

    const [content] = await file.download();
    return JSON.parse(content.toString());
  } catch (error) {
    console.error(`Error getting cached JSON: ${error.message}`);
    return false;
  }
}

/**
 * Write JSON to Cloud Storage and optionally to Firestore
 * @param {string} filePath - The file path
 * @param {Object} jsonData - The JSON data
 * @param {Object} firestoreRef - The Firestore reference (optional)
 * @returns {boolean} - Whether the write was successful
 */
async function writeJsonToBucket(filePath, jsonData, firestoreRef = null) {
  try {
    // Write to Cloud Storage
    const file = storage.bucket(bucketName).file(filePath);
    await file.save(JSON.stringify(jsonData, null, 2), {
      metadata: { contentType: 'application/json' }
    });
    console.log(`JSON written to ${filePath}`);

    // If a Firestore reference is provided, also write to Firestore
    if (firestoreRef) {
      await firestoreRef.set(jsonData);
      console.log(`JSON written to Firestore: ${firestoreRef.path}`);
    }

    return true;
  } catch (error) {
    console.error(`Error writing JSON: ${error.message}`);
    throw error;
  }
}

export {
  getCachedJson,
  writeJsonToBucket,
  storage,
  bucketName
};

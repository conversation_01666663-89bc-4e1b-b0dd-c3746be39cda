// server.js
// Main application entry point

import express from 'express';
import bodyParser from 'body-parser';
import dotenv from 'dotenv';
import { initializeFirebase, getFirestoreDb } from './config/firebase-config.js';
import { Timestamp } from 'firebase-admin/firestore';
import { cors, corsOptions } from './middleware/cors.js';
import { errorHandler } from './middleware/error-handler.js';
import { authenticate } from './middleware/auth.js';
import campaignRoutes from './routes/campaign-routes.js';
import influencerRoutes from './routes/influencer-routes.js';
import analysisRoutes from './routes/analysis-routes.js';
import discoveryRoutes from './routes/discovery-routes.js';
import { generateCampaignBrief } from './services/campaign-service.js';
import { enrichInfluencer } from './services/influencer-service.js';
import { performWebAnalysis, performAestheticAnalysis, performPartnershipAnalysis, performROIAnalysis, getMergedAnalysis } from './services/analysis-service.js';
import { discoverInfluencers } from './services/discovery-service.js';

// Load environment variables
dotenv.config();

// Initialize Firebase using our centralized configuration
initializeFirebase();

// Create Express app
const app = express();

// Apply middleware
app.use(cors(corsOptions));
app.options("*", cors(corsOptions));
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// Health check endpoint (no authentication required)
app.get('/_health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Apply authentication middleware to all other routes
app.use(authenticate);

// Register routes
app.use('/api/campaigns', campaignRoutes);
app.use('/api/influencers', influencerRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/discovery', discoveryRoutes);

// Legacy route for backward compatibility
app.post('/api/analyze', async (req, res) => {
  try {
    const input = req.body;

    // Determine which phase to run based on the input
    if (input.campaign && !input.report_id && !input.selected_account) {
      // Phase 1: Campaign Brief & Audience Analysis
      const result = await generateCampaignBrief(input);
      res.status(200).json(result.campaignData);
    } else if (input.report_id && !input.selected_account) {
      // Phase 2: Broad Influencer Discovery
      const clientId = input.client_id || 'default_client';
      const campaignId = input.report_id;

      // Get the campaign data
      const db = getFirestoreDb();
      const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
      const campaignDoc = await campaignRef.get();

      if (!campaignDoc.exists) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      const campaignData = campaignDoc.data();

      // Discover influencers
      const discoveryJSON = await discoverInfluencers(clientId, campaignId, campaignData);
      res.status(200).json(discoveryJSON);
    } else if (input.report_id && input.selected_account) {
      // Phase 3-6: Full Influencer Analysis
      const clientId = input.client_id || 'default_client';
      const campaignId = input.report_id;
      const username = input.selected_account;

      // Enrich the influencer (default to not forcing refresh)
      const forceRefresh = input.force_refresh === true;
      console.log(`Analyzing influencer ${username} with forceRefresh: ${forceRefresh}`);
      const { influencerId, processedData } = await enrichInfluencer(username, 'instagram', forceRefresh);

      // Add the influencer to the campaign
      const db = getFirestoreDb();
      const campaignInfluencerRef = db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').doc(influencerId);

      await campaignInfluencerRef.set({
        influencer_id: influencerId,
        username: username,
        status: 'enriched',
        created_at: Timestamp.now(),
        updated_at: Timestamp.now()
      });

      // Get the campaign data
      const campaignRef = getFirestoreDb().collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
      const campaignDoc = await campaignRef.get();

      if (!campaignDoc.exists) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      const campaignData = campaignDoc.data();

      // Perform web analysis
      const influencerName = processedData.profileInfo.fullName || username;
      console.log(`Performing web analysis for ${influencerName} (ID: ${influencerId}) in campaign ${campaignId} for client ${clientId}`);
      // Pass campaign data to web analysis
      const webAnalysisData = await performWebAnalysis(clientId, campaignId, influencerId, influencerName, username);

      // Transform the data for aesthetic analysis
      const transformedData = {
        instagram: {
          profile_picture_hd: processedData.profileInfo.profileImageUrl || '',
          post_data: []
        }
      };

      // Use the allPostImages array to populate post_data
      if (processedData.allPostImages && Array.isArray(processedData.allPostImages)) {
        // Group images by post ID
        const postGroups = {};
        processedData.allPostImages.forEach(post => {
          if (!postGroups[post.id]) {
            postGroups[post.id] = {
              post_id: post.id.toString(),
              caption: post.caption || '',
              engagement: {
                likes: parseInt(post.likes.replace(/,/g, ''), 10) || 0,
                comments: parseInt(post.comments.replace(/,/g, ''), 10) || 0
              },
              media: []
            };
          }

          // Add media item
          postGroups[post.id].media.push({
            type: post.type || 'image',
            url: post.imageUrl
          });
        });

        // Convert to array
        transformedData.instagram.post_data = Object.values(postGroups);
      }

      // Perform aesthetic analysis with the transformed data
      const aestheticAnalysisData = await performAestheticAnalysis(clientId, campaignId, influencerId, influencerName, transformedData, webAnalysisData);

      // Perform partnership analysis
      console.log(`Performing partnership analysis for ${influencerName} (ID: ${influencerId}) in campaign ${campaignId} for client ${clientId}`);
      const partnershipAnalysisData = await performPartnershipAnalysis(clientId, campaignId, influencerId, influencerName, username, campaignData, processedData);

      // Perform ROI analysis
      const roiAnalysisData = await performROIAnalysis(clientId, campaignId, influencerId, influencerName, campaignData, processedData, webAnalysisData, aestheticAnalysisData, partnershipAnalysisData);

      // Generate merged analysis
      const mergedAnalysis = await getMergedAnalysis(clientId, campaignId, influencerId);

      res.status(200).json(mergedAnalysis);
    } else {
      res.status(400).json({ error: 'Invalid input' });
    }
  } catch (error) {
    console.error('Error performing analysis:', error);
    res.status(500).json({ error: 'Failed to perform analysis' });
  }
});

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
});

export default app;

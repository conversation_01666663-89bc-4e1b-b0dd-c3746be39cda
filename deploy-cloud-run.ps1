# PowerShell script for deploying to Google Cloud Run
# Project: Palas Influencer Intelligence Platform

# Set environment variables
$env:PROJECT_ID = "studious-booth-446123-h5"
$env:REGION = "us-central1"
$env:SERVICE_NAME = "palas-influencer-intelligence"
$env:OPENAI_API_KEY = "********************************************************************************************************************************************************************"
$env:INFLUENCERS_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMzQ0NzA4NDMwLCJpYXQiOjE3Mzk5MDg0MzAsImp0aSI6IjkxZTlmODlkZTI1NDQ3MWI5OTkzZTA3YjQ0YmVlMTI0IiwidXNlcl9pZCI6Nzg1MX0.qpbfGOObJMjLjYob_bzW7VX10SCdZpMVLxBEITIUuZc"

Write-Host "Starting deployment process for project: $env:PROJECT_ID" -ForegroundColor Green

# Step 1: Set the project in gcloud
Write-Host "Setting Google Cloud project..." -ForegroundColor Cyan
gcloud config set project $env:PROJECT_ID

# Step 2: Enable required APIs
Write-Host "Enabling required Google Cloud APIs..." -ForegroundColor Cyan
gcloud services enable cloudbuild.googleapis.com run.googleapis.com secretmanager.googleapis.com artifactregistry.googleapis.com storage.googleapis.com

# Step 3: Create a .gcloudignore file to prevent unnecessary files from being uploaded
Write-Host "Creating .gcloudignore file..." -ForegroundColor Cyan
@"
# .gcloudignore
.git
.gitignore
node_modules/
npm-debug.log
.env
.env.*
*.log
test/
.vscode/
.idea/
*.swp
.DS_Store
Thumbs.db
tmp/
temp/
*.tmp
old_implementation/
sample_jsons/
.gcloudignore
"@ | Out-File -FilePath ".gcloudignore" -Encoding utf8

# Step 4: Set up Secret Manager
Write-Host "Setting up secrets in Secret Manager..." -ForegroundColor Cyan

# Check if Firebase credentials file exists
$credentialsPath = "credentials/palas-influencer-intelligence-firebase-adminsdk-fbsvc-7f452c5b1f.json"
if (-not (Test-Path $credentialsPath)) {
    Write-Host "Error: Firebase credentials file not found at $credentialsPath" -ForegroundColor Red
    exit 1
}

# Create and populate secrets
try {
    # Function to check if a secret exists and create it only if needed
    function Ensure-Secret {
        param (
            [string]$SecretName,
            [string]$SecretValue,
            [string]$SecretPath = $null,
            [switch]$ForceUpdate = $false
        )

        # Check if secret exists
        $secretExists = $false
        try {
            $secretInfo = gcloud secrets describe $SecretName --project=$env:PROJECT_ID 2>$null
            if ($secretInfo) {
                $secretExists = $true
                Write-Host "Secret '$SecretName' already exists." -ForegroundColor Green
            }
        } catch {
            Write-Host "Secret '$SecretName' does not exist. Creating..." -ForegroundColor Cyan
        }

        # Create secret if it doesn't exist
        if (-not $secretExists) {
            gcloud secrets create $SecretName --project=$env:PROJECT_ID
            Write-Host "Secret '$SecretName' created." -ForegroundColor Green

            # Set the initial value for the new secret
            if ($SecretPath) {
                # Use file path
                Write-Host "Setting initial value for secret '$SecretName' from file..." -ForegroundColor Cyan
                Get-Content $SecretPath | gcloud secrets versions add $SecretName --data-file=- --project=$env:PROJECT_ID
            } else {
                # Use string value
                Write-Host "Setting initial value for secret '$SecretName' from value..." -ForegroundColor Cyan
                $tempFile = "temp_${SecretName}.txt"
                $SecretValue | Out-File -FilePath $tempFile -Encoding utf8 -NoNewline
                gcloud secrets versions add $SecretName --data-file=$tempFile --project=$env:PROJECT_ID
                Remove-Item $tempFile -Force
            }

            Write-Host "Secret '$SecretName' initialized successfully." -ForegroundColor Green
        }
        # Only update if ForceUpdate is specified and the secret already exists
        elseif ($ForceUpdate) {
            if ($SecretPath) {
                # Use file path
                Write-Host "Updating secret '$SecretName' from file..." -ForegroundColor Cyan
                Get-Content $SecretPath | gcloud secrets versions add $SecretName --data-file=- --project=$env:PROJECT_ID
            } else {
                # Use string value
                Write-Host "Updating secret '$SecretName' from value..." -ForegroundColor Cyan
                $tempFile = "temp_${SecretName}.txt"
                $SecretValue | Out-File -FilePath $tempFile -Encoding utf8 -NoNewline
                gcloud secrets versions add $SecretName --data-file=$tempFile --project=$env:PROJECT_ID
                Remove-Item $tempFile -Force
            }

            Write-Host "Secret '$SecretName' updated successfully." -ForegroundColor Green
        }
        else {
            Write-Host "Secret '$SecretName' exists and will not be updated." -ForegroundColor Yellow
        }
    }

    # Ensure Firebase service account secret exists
    Ensure-Secret -SecretName "firebase-service-account" -SecretPath $credentialsPath

    # Ensure OpenAI API key secret exists
    Ensure-Secret -SecretName "openai-api-key" -SecretValue $env:OPENAI_API_KEY

    # Ensure Influencers Club API key secret exists
    Ensure-Secret -SecretName "influencers-api-key" -SecretValue $env:INFLUENCERS_API_KEY

    # If you need to force an update of secrets, uncomment these lines:
    # Ensure-Secret -SecretName "firebase-service-account" -SecretPath $credentialsPath -ForceUpdate
    # Ensure-Secret -SecretName "openai-api-key" -SecretValue $env:OPENAI_API_KEY -ForceUpdate
    # Ensure-Secret -SecretName "influencers-api-key" -SecretValue $env:INFLUENCERS_API_KEY -ForceUpdate
}
catch {
    Write-Host "Error creating secrets: $_" -ForegroundColor Red
    exit 1
}

# Grant the Cloud Run service account access to the secrets
Write-Host "Granting service account access to secrets..." -ForegroundColor Cyan

# Get project number
$env:PROJECT_NUMBER = $(gcloud projects describe $env:PROJECT_ID --format="value(projectNumber)")

# Try to find the Cloud Run service account
$SERVICE_ACCOUNT = $(gcloud iam service-accounts list --filter="displayName:Cloud Run Service Agent" --format='value(email)' --project=$env:PROJECT_ID)

# If not found, use the default compute service account
if (-not $SERVICE_ACCOUNT) {
    Write-Host "Cloud Run Service Agent not found. Using default compute service account." -ForegroundColor Yellow
    $SERVICE_ACCOUNT = "$env:<EMAIL>"
}

Write-Host "Using service account: $SERVICE_ACCOUNT" -ForegroundColor Cyan

# Function to check and grant IAM permissions without duplicating
function Grant-SecretAccess {
    param (
        [string]$SecretName,
        [string]$ServiceAccount,
        [string]$Role
    )

    # Check if binding already exists
    $policyBindings = $(gcloud secrets get-iam-policy $SecretName --project=$env:PROJECT_ID --format="json")
    $bindingExists = $false

    if ($policyBindings) {
        $policy = $policyBindings | ConvertFrom-Json

        if ($policy.bindings) {
            foreach ($binding in $policy.bindings) {
                if ($binding.role -eq $Role -and $binding.members -contains "serviceAccount:$ServiceAccount") {
                    $bindingExists = $true
                    Write-Host "IAM binding for '$SecretName' already exists for service account '$ServiceAccount'." -ForegroundColor Yellow
                    break
                }
            }
        }
    }

    # Add binding if it doesn't exist
    if (-not $bindingExists) {
        Write-Host "Adding IAM binding for '$SecretName' to service account '$ServiceAccount'..." -ForegroundColor Cyan
        gcloud secrets add-iam-policy-binding $SecretName --member="serviceAccount:$ServiceAccount" --role=$Role --project=$env:PROJECT_ID
        Write-Host "IAM binding added successfully." -ForegroundColor Green
    }
}

# Grant access to each secret
Grant-SecretAccess -SecretName "firebase-service-account" -ServiceAccount $SERVICE_ACCOUNT -Role "roles/secretmanager.secretAccessor"
Grant-SecretAccess -SecretName "openai-api-key" -ServiceAccount $SERVICE_ACCOUNT -Role "roles/secretmanager.secretAccessor"
Grant-SecretAccess -SecretName "influencers-api-key" -ServiceAccount $SERVICE_ACCOUNT -Role "roles/secretmanager.secretAccessor"

# Grant storage permissions to the Cloud Run service account
Write-Host "Granting storage permissions to Cloud Run service account..." -ForegroundColor Cyan
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$SERVICE_ACCOUNT" --role="roles/storage.objectAdmin"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$SERVICE_ACCOUNT" --role="roles/storage.objectCreator"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$SERVICE_ACCOUNT" --role="roles/storage.objectViewer"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$SERVICE_ACCOUNT" --role="roles/storage.admin"

# Set bucket IAM policy to allow the service account to manage object ACLs
$BUCKET_NAME = "palas-run-cache"
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME --member="serviceAccount:$SERVICE_ACCOUNT" --role="roles/storage.legacyObjectOwner"
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME --member="serviceAccount:$SERVICE_ACCOUNT" --role="roles/storage.legacyBucketOwner"

# Grant storage permissions to the Firebase Admin SDK service account
Write-Host "Granting storage permissions to Firebase Admin SDK service account..." -ForegroundColor Cyan
$FIREBASE_SERVICE_ACCOUNT = "<EMAIL>"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$FIREBASE_SERVICE_ACCOUNT" --role="roles/storage.objectAdmin"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$FIREBASE_SERVICE_ACCOUNT" --role="roles/storage.objectCreator"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$FIREBASE_SERVICE_ACCOUNT" --role="roles/storage.objectViewer"
gcloud projects add-iam-policy-binding $env:PROJECT_ID --member="serviceAccount:$FIREBASE_SERVICE_ACCOUNT" --role="roles/storage.admin"

# Set bucket IAM policy to allow the Firebase service account to manage object ACLs
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME --member="serviceAccount:$FIREBASE_SERVICE_ACCOUNT" --role="roles/storage.legacyObjectOwner"
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME --member="serviceAccount:$FIREBASE_SERVICE_ACCOUNT" --role="roles/storage.legacyBucketOwner"

# Set bucket IAM policy to allow public access to images
Write-Host "Setting bucket IAM policy for public image access..." -ForegroundColor Cyan
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME --member="allUsers" --role="roles/storage.objectViewer"

# Set CORS configuration for the bucket
Write-Host "Setting CORS configuration for bucket..." -ForegroundColor Cyan
$CORS_CONFIG = @"
[
  {
    "origin": ["*"],
    "method": ["GET", "HEAD"],
    "responseHeader": ["Content-Type", "Access-Control-Allow-Origin"],
    "maxAgeSeconds": 3600
  }
]
"@
$CORS_CONFIG | Out-File -FilePath "cors-config.json" -Encoding utf8
gcloud storage buckets update gs://$BUCKET_NAME --cors-file=cors-config.json
Remove-Item "cors-config.json" -Force

Write-Host "Secrets and storage permissions setup complete!" -ForegroundColor Green

# Step 5: Build and deploy directly with gcloud run deploy
Write-Host "Building and deploying with gcloud run deploy..." -ForegroundColor Cyan

# Build the container image
Write-Host "Building container image..." -ForegroundColor Cyan
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$IMAGE_TAG = "gcr.io/studious-booth-446123-h5/palas-influencer-intelligence:$timestamp"
Write-Host "Using image tag: $IMAGE_TAG" -ForegroundColor Cyan
gcloud builds submit --tag=$IMAGE_TAG --project=$env:PROJECT_ID

# Deploy to Cloud Run
Write-Host "Deploying to Cloud Run..." -ForegroundColor Cyan
gcloud run deploy $env:SERVICE_NAME `
  --image=$IMAGE_TAG `
  --platform=managed `
  --region=$env:REGION `
  --allow-unauthenticated `
  --memory=2Gi `
  --cpu=2 `
  --min-instances=1 `
  --max-instances=10 `
  --set-env-vars="NODE_ENV=production,STORAGE_BUCKET=palas-run-cache" `
  --set-secrets="FIREBASE_SERVICE_ACCOUNT=firebase-service-account:latest,OPENAI_API_KEY=openai-api-key:latest,INFLUENCERS_API_KEY=influencers-api-key:latest" `
  --project=$env:PROJECT_ID

# Step 6: Verify deployment
Write-Host "Verifying deployment..." -ForegroundColor Cyan
$serviceUrl = $(gcloud run services describe $env:SERVICE_NAME --platform=managed --region=$env:REGION --project=$env:PROJECT_ID --format="value(status.url)")

if ($serviceUrl) {
    Write-Host "Deployment successful! Your service is available at: $serviceUrl" -ForegroundColor Green

    # Test the health endpoint
    Write-Host "Testing health endpoint..." -ForegroundColor Cyan
    try {
        $response = Invoke-RestMethod -Uri "$serviceUrl/_health" -Method Get
        Write-Host "Health check successful: $($response | ConvertTo-Json)" -ForegroundColor Green
    }
    catch {
        Write-Host "Health check failed: $_" -ForegroundColor Red
    }
}
else {
    Write-Host "Deployment verification failed. Check the Cloud Run console for details." -ForegroundColor Red
}

Write-Host "Deployment process completed." -ForegroundColor Green

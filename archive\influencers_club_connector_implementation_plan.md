# Influencers Club Connector Implementation Plan

This document outlines the detailed implementation plan for the Influencers Club connector, which will be responsible for fetching, processing, and storing influencer data from the Influencers Club API in the new Firestore database structure.

## Overview

The Influencers Club connector will serve as the bridge between the Influencers Club API and our Firestore database. It will handle:

1. Fetching data from the Influencers Club API
2. Processing the raw data into a standardized format
3. Storing both raw and processed data in Cloud Storage
4. Updating Firestore with the latest influencer data

## Class Structure

```javascript
// Pseudocode
class InfluencersClubConnector {
  constructor(apiKey, bucketName) {
    // Initialize with API key and storage bucket
    this.apiKey = apiKey;
    this.bucketName = bucketName;
    this.storage = new Storage();
    this.bucket = this.storage.bucket(bucketName);
    this.db = getFirestore();
  }
  
  async fetchInfluencerData(filterValue, filterKey = "username", platform = "instagram", emailRequired = "must_have", postDataRequired = true) {
    // Validate parameters
    // Make API request
    // Handle errors
    // Return raw data
  }
  
  async processRawData(rawData) {
    // Process raw data into standardized format
    // Extract data from all platforms (Instagram, YouTube, TikTok, etc.)
    // Extract brand mentions and partnerships
    // Return processed data
  }
  
  async cacheImage(imageUrl, filename) {
    // Download and cache image in Cloud Storage
    // Return public URL
  }
  
  async enrichInfluencer(filterValue, filterKey = "username", platform = "instagram", options = {}) {
    // Check if influencer exists in Firestore
    // Fetch raw data from API
    // Store raw data in Cloud Storage
    // Process raw data
    // Store processed data in Cloud Storage
    // Update Firestore with latest data
    // Return processed data and influencer ID
  }
  
  // Helper methods
  formatNumber(num) {
    // Format number with commas
  }
  
  extractBrandMentions(rawData) {
    // Extract brand mentions from raw data
  }
  
  extractPartnerships(rawData) {
    // Extract partnerships from raw data
  }
}
```

## Method Details

### 1. fetchInfluencerData

```javascript
/**
 * Fetch influencer data from Influencers Club API
 * @param {string} filterValue - The value to search for (username, email, or URL)
 * @param {string} filterKey - The type of filter (username, email, or url)
 * @param {string} platform - The platform to fetch data from
 * @param {string|boolean} emailRequired - Email requirement (must_have, preferred, not_needed, true, false)
 * @param {boolean} postDataRequired - Whether to include post data
 * @returns {Object} - The raw API response
 */
async fetchInfluencerData(filterValue, filterKey = "username", platform = "instagram", emailRequired = "must_have", postDataRequired = true) {
  // Validate platform is supported
  const supportedPlatforms = [
    "instagram", "youtube", "tiktok", "onlyfans", 
    "twitter", "twitch", "discord", "facebook", 
    "snapchat", "pinterest"
  ];
  
  if (!supportedPlatforms.includes(platform)) {
    console.warn(`Platform ${platform} may not be supported. Supported platforms: ${supportedPlatforms.join(', ')}`);
  }
  
  // Validate filter key
  const validFilterKeys = ["url", "email", "username"];
  if (!validFilterKeys.includes(filterKey)) {
    throw new Error(`Invalid filter_key: ${filterKey}. Must be one of: ${validFilterKeys.join(', ')}`);
  }
  
  // Validate email_required parameter
  const validEmailOptions = ["not_needed", "preferred", "must_have", true, false];
  if (!validEmailOptions.includes(emailRequired)) {
    console.warn(`Invalid email_required value: ${emailRequired}. Using default "must_have"`);
    emailRequired = "must_have";
  }
  
  // Prepare request
  const url = "https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/";
  const payload = {
    filter_value: filterValue,
    filter_key: filterKey,
    platform,
    email_required: emailRequired,
    post_data_required: postDataRequired
  };
  
  const headers = {
    'Authorization': `Bearer ${this.apiKey}`,
    'Content-Type': 'application/json'
  };
  
  // Make request
  try {
    console.log(`Fetching data for ${filterValue} on ${platform} platform...`);
    const response = await axios.post(url, payload, { headers });
    return response.data;
  } catch (error) {
    // Handle errors
    if (error.response) {
      console.error(`Error ${error.response.status}: ${error.response.statusText}`);
      console.error('Error response data:', error.response.data);
    } else if (error.request) {
      console.error('No response received from server:', error.request);
    } else {
      console.error('Error setting up request:', error.message);
    }
    throw error;
  }
}
```

### 2. processRawData

```javascript
/**
 * Process raw influencer data into standardized format
 * @param {Object} rawData - The raw data from Influencers Club API
 * @returns {Object} - The processed data in standardized format
 */
async processRawData(rawData) {
  // Initialize processed data object
  const processed = {
    profileInfo: {
      username: '',
      fullName: '',
      profileImageUrl: '',
      category: '',
      bio: '',
      email: rawData.email || '',
      location: rawData.location || '',
      speakingLanguage: rawData.speaking_language || '',
      isBusinessAccount: rawData.is_business || false,
      isCreator: rawData.is_creator || false,
      hasBrandDeals: rawData.has_brand_deals || false,
      hasLinkInBio: rawData.has_link_in_bio || false
    },
    influencerStats: {
      platforms: [],
      engagementRate: {
        value: '',
        description: ''
      },
      contentTypes: [],
      postingFrequency: 0,
      followerGrowth: {
        threeMonths: 0,
        sixMonths: 0,
        nineMonths: 0,
        twelveMonths: 0
      }
    },
    audienceInsights: {
      demographics: {
        age: [],
        gender: {}
      },
      locations: [],
      interests: []
    },
    recentPosts: [],
    brandMentions: [],
    partnerships: [],
    platformPresence: []
  };
  
  // Add platform presence information
  if (rawData.creator_has && Array.isArray(rawData.creator_has)) {
    processed.platformPresence = rawData.creator_has.map(platform => ({
      platform: platform.platform || '',
      url: platform.url || ''
    }));
  }
  
  // Process Instagram data
  if (rawData.instagram) {
    // Extract profile info
    // Extract stats
    // Extract audience insights
    // Extract recent posts
  }
  
  // Process YouTube data
  if (rawData.youtube) {
    // Extract profile info
    // Extract stats
    // Extract recent videos
  }
  
  // Process TikTok data
  if (rawData.tiktok) {
    // Extract profile info
    // Extract stats
    // Extract recent posts
  }
  
  // Process Twitter data
  if (rawData.twitter) {
    // Extract profile info
    // Extract stats
    // Extract recent tweets
  }
  
  // Process other platforms
  
  // Extract brand mentions and partnerships
  processed.brandMentions = this.extractBrandMentions(rawData);
  processed.partnerships = this.extractPartnerships(rawData);
  
  return processed;
}
```

### 3. enrichInfluencer

```javascript
/**
 * Fetch, process, and store influencer data
 * @param {string} filterValue - The value to search for (username, email, or URL)
 * @param {string} filterKey - The type of filter (username, email, or url)
 * @param {string} platform - The platform to fetch data from
 * @param {Object} options - Additional options
 * @returns {Object} - The processed data and influencer ID
 */
async enrichInfluencer(filterValue, filterKey = "username", platform = "instagram", options = {}) {
  // Set default options
  const defaultOptions = {
    emailRequired: "must_have",
    postDataRequired: true,
    updateFirestore: true
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  // Determine the username for querying Firestore
  let username = filterValue;
  if (filterKey === "url") {
    // Extract username from URL if possible
    const urlMatch = filterValue.match(/([^\/]+)\/?$/);
    if (urlMatch) {
      username = urlMatch[1];
    }
  }
  
  // Check if we already have this influencer in Firestore
  let influencerRef, influencerSnapshot, influencerId;
  
  if (finalOptions.updateFirestore) {
    influencerRef = this.db.collection('influencers').where('username', '==', username).limit(1);
    influencerSnapshot = await influencerRef.get();
    
    if (influencerSnapshot.empty) {
      // Create a new influencer document
      const newInfluencerRef = this.db.collection('influencers').doc();
      influencerId = newInfluencerRef.id;
    } else {
      // Use existing influencer document
      influencerId = influencerSnapshot.docs[0].id;
    }
  } else {
    // Generate a temporary ID if not updating Firestore
    influencerId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
  
  // Fetch raw data from Influencers Club
  const rawData = await this.fetchInfluencerData(
    filterValue, 
    platform, 
    finalOptions.emailRequired, 
    finalOptions.postDataRequired,
    filterKey
  );
  
  // Store raw data in Cloud Storage if updating Firestore
  const timestamp = Date.now();
  let rawStoragePath = '';
  
  if (finalOptions.updateFirestore) {
    rawStoragePath = `influencers/${influencerId}/raw_data/influencers_club_${timestamp}.json`;
    await this.bucket.file(rawStoragePath).save(JSON.stringify(rawData), {
      metadata: { contentType: 'application/json' }
    });
  }
  
  // Process the raw data
  const processedData = await this.processRawData(rawData);
  
  // Store processed data in Cloud Storage if updating Firestore
  let processedStoragePath = '';
  
  if (finalOptions.updateFirestore) {
    processedStoragePath = `influencers/${influencerId}/processed_data/influencers_club_${timestamp}.json`;
    await this.bucket.file(processedStoragePath).save(JSON.stringify(processedData), {
      metadata: { contentType: 'application/json' }
    });
  }
  
  // Update Firestore with the latest data if requested
  if (finalOptions.updateFirestore) {
    // Prepare the base influencer data
    const influencerData = {
      username: processedData.profileInfo.username || username,
      full_name: processedData.profileInfo.fullName || '',
      email: rawData.email || '',
      location: rawData.location || '',
      speaking_language: rawData.speaking_language || '',
      has_brand_deals: rawData.has_brand_deals || false,
      has_link_in_bio: rawData.has_link_in_bio || false,
      is_business: rawData.is_business || false,
      is_creator: rawData.is_creator || false,
      platforms: {},
      creator_has: rawData.creator_has || [],
      last_updated: this.db.Timestamp.now()
    };
    
    // Add platform-specific data
    if (rawData.instagram) {
      influencerData.platforms.instagram = {
        // Instagram-specific fields
      };
    }
    
    if (rawData.youtube) {
      influencerData.platforms.youtube = {
        // YouTube-specific fields
      };
    }
    
    if (rawData.tiktok) {
      influencerData.platforms.tiktok = {
        // TikTok-specific fields
      };
    }
    
    if (rawData.twitter) {
      influencerData.platforms.twitter = {
        // Twitter-specific fields
      };
    }
    
    // Add or update the influencer document
    if (influencerSnapshot.empty) {
      influencerData.created_at = this.db.Timestamp.now();
      await this.db.collection('influencers').doc(influencerId).set(influencerData);
    } else {
      await this.db.collection('influencers').doc(influencerId).update(influencerData);
    }
    
    // Add a record of this data pull
    await this.db.collection('influencers').doc(influencerId).collection('raw_data').add({
      source: 'influencers_club',
      storage_path: rawStoragePath,
      processed_storage_path: processedStoragePath,
      pulled_at: this.db.Timestamp.now(),
      processed_at: this.db.Timestamp.now()
    });
  }
  
  return {
    influencerId,
    processedData
  };
}
```

## Helper Methods

### 1. cacheImage

```javascript
/**
 * Cache an image in Cloud Storage and return the public URL
 * @param {string} imageUrl - The original image URL
 * @param {string} filename - The filename to use in storage
 * @returns {string} - The public URL of the cached image
 */
async cacheImage(imageUrl, filename) {
  try {
    // Download the image
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const buffer = Buffer.from(response.data, 'binary');
    
    // Upload to Cloud Storage
    const file = this.bucket.file(`palas-image-cache/${filename}`);
    await file.save(buffer, {
      metadata: {
        contentType: response.headers['content-type']
      }
    });
    
    // Make the file publicly accessible
    await file.makePublic();
    
    // Return the public URL
    return `https://storage.googleapis.com/${this.bucketName}/palas-image-cache/${filename}`;
  } catch (error) {
    console.error('Error caching image:', error);
    return imageUrl; // Return original URL if caching fails
  }
}
```

### 2. extractBrandMentions

```javascript
/**
 * Extract brand mentions from raw data
 * @param {Object} rawData - The raw data from Influencers Club API
 * @returns {Array} - Array of brand mentions
 */
extractBrandMentions(rawData) {
  const brandMentions = [];
  
  // Extract from Instagram posts
  if (rawData.instagram && rawData.instagram.posts) {
    // Process Instagram posts
  }
  
  // Extract from TikTok posts
  if (rawData.tiktok && rawData.tiktok.latest_post) {
    // Process TikTok posts
  }
  
  // Extract from YouTube videos
  if (rawData.youtube && rawData.youtube.latest_video) {
    // Process YouTube videos
  }
  
  return brandMentions;
}
```

### 3. extractPartnerships

```javascript
/**
 * Extract partnerships from raw data
 * @param {Object} rawData - The raw data from Influencers Club API
 * @returns {Array} - Array of partnerships
 */
extractPartnerships(rawData) {
  const partnerships = [];
  
  // Extract from Instagram posts
  if (rawData.instagram && rawData.instagram.posts) {
    // Process Instagram posts
  }
  
  // Extract from TikTok posts
  if (rawData.tiktok && rawData.tiktok.latest_post) {
    // Process TikTok posts
  }
  
  // Extract from YouTube videos
  if (rawData.youtube && rawData.youtube.latest_video) {
    // Process YouTube videos
  }
  
  return partnerships;
}
```

## Integration with Analysis Flow

The Influencers Club connector will be integrated into the analysis flow as follows:

```javascript
// Pseudocode
async function performInfluencerAnalysis(input) {
  // Initialize connector
  const connector = new InfluencersClubConnector(INFLUENCERS_API_KEY, bucketName);
  
  // Phase 1: Campaign Brief & Audience Analysis
  // ...
  
  // Phase 2: Broad Influencer Discovery
  // ...
  
  // Phase 3: Influencer Enrichment
  const username = input.selected_account;
  const { influencerId, processedData } = await connector.enrichInfluencer(username);
  
  // Phase 4: Deep-Dive Web Search & Historical Analysis
  // ...
  
  // Phase 5: Visual & Content Analysis
  // ...
  
  // Phase 6: ROI & Strategic Fit Evaluation
  // ...
}
```

## Error Handling

The connector will implement robust error handling to deal with various error scenarios:

1. **API Errors**: Handle errors from the Influencers Club API
2. **Network Errors**: Handle network-related errors
3. **Firestore Errors**: Handle errors when interacting with Firestore
4. **Cloud Storage Errors**: Handle errors when interacting with Cloud Storage
5. **Data Processing Errors**: Handle errors when processing raw data

## Testing Strategy

The connector will be tested using the following strategy:

1. **Unit Tests**: Test individual methods with mock data
2. **Integration Tests**: Test the entire connector with the actual API
3. **Error Tests**: Test error handling with simulated errors
4. **Performance Tests**: Test with large datasets to ensure efficiency

## Implementation Steps

1. **Create Connector Class**: Implement the InfluencersClubConnector class
2. **Implement fetchInfluencerData**: Implement the method to fetch data from the API
3. **Implement processRawData**: Implement the method to process raw data
4. **Implement cacheImage**: Implement the method to cache images
5. **Implement extractBrandMentions and extractPartnerships**: Implement the methods to extract brand mentions and partnerships
6. **Implement enrichInfluencer**: Implement the method to enrich influencer data
7. **Test Connector**: Test the connector with real data
8. **Integrate with Analysis Flow**: Integrate the connector into the analysis flow

## Conclusion

The Influencers Club connector will provide a robust and efficient way to fetch, process, and store influencer data from the Influencers Club API in the new Firestore database structure. It will handle all aspects of data management, from fetching raw data to storing processed data in Firestore, while maintaining backward compatibility with the existing system.

// campaign.js
// Campaign data model

import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { validateCampaignData } from '../utils/validation-utils.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

/**
 * Campaign model
 */
class Campaign {
  /**
   * Create a new Campaign instance
   * @param {Object} data - The campaign data
   */
  constructor(data = {}) {
    this.name = data.name || '';
    this.report_id = data.report_id || '';
    this.product_description = data.product_description || '';
    this.influencer_gender = data.influencer_gender || '';
    this.influencer_niche = data.influencer_niche || '';
    this.influencer_age = data.influencer_age || '';
    this.influencer_personality = data.influencer_personality || '';
    this.influencer_aesthetic = data.influencer_aesthetic || '';
    this.min_follower_count = data.min_follower_count || 0;
    this.max_follower_count = data.max_follower_count || 0;
    this.min_engagement_rate = data.min_engagement_rate || 0;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
    this.status = data.status || 'active';
  }

  /**
   * Create a new campaign in Firestore
   * @param {string} clientId - The client ID
   * @param {Object} data - The campaign data
   * @returns {string} - The campaign ID
   */
  static async create(clientId = DEFAULT_CLIENT_ID, data) {
    const db = getFirestore();

    // Validate campaign data
    const validation = validateCampaignData(data);
    if (!validation.isValid) {
      throw new Error(`Invalid campaign data: ${validation.errors.join(', ')}`);
    }

    // Create a new campaign document
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc();
    const campaignId = campaignRef.id;

    // Prepare campaign data
    const campaignData = {
      name: data.name,
      report_id: campaignId,
      product_description: data.product_description,
      influencer_gender: data.influencer_gender || '',
      influencer_niche: data.influencer_niche || '',
      influencer_age: data.influencer_age || '',
      influencer_personality: data.influencer_personality || '',
      influencer_aesthetic: data.influencer_aesthetic || '',
      min_follower_count: data.min_follower_count || 0,
      max_follower_count: data.max_follower_count || 0,
      min_engagement_rate: data.min_engagement_rate || 0,
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      status: 'active'
    };

    // Save to Firestore
    await campaignRef.set(campaignData);

    // Save to Cloud Storage for backward compatibility
    await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/campaign_analysis.json`, campaignData, campaignRef);

    return campaignId;
  }

  /**
   * Find a campaign by ID
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @returns {Campaign} - The campaign
   */
  static async findById(clientId = DEFAULT_CLIENT_ID, campaignId) {
    const db = getFirestore();

    // Get the campaign document
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
    const campaignDoc = await campaignRef.get();

    if (!campaignDoc.exists) {
      throw new Error(`Campaign not found: ${campaignId}`);
    }

    return new Campaign(campaignDoc.data());
  }

  /**
   * Update a campaign
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {Object} data - The campaign data
   * @returns {Campaign} - The updated campaign
   */
  static async update(clientId = DEFAULT_CLIENT_ID, campaignId, data) {
    const db = getFirestore();

    // Validate campaign data
    const validation = validateCampaignData(data);
    if (!validation.isValid) {
      throw new Error(`Invalid campaign data: ${validation.errors.join(', ')}`);
    }

    // Get the campaign document
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
    const campaignDoc = await campaignRef.get();

    if (!campaignDoc.exists) {
      throw new Error(`Campaign not found: ${campaignId}`);
    }

    // Prepare update data
    const updateData = {
      name: data.name,
      product_description: data.product_description,
      influencer_gender: data.influencer_gender || '',
      influencer_niche: data.influencer_niche || '',
      influencer_age: data.influencer_age || '',
      influencer_personality: data.influencer_personality || '',
      influencer_aesthetic: data.influencer_aesthetic || '',
      min_follower_count: data.min_follower_count || 0,
      max_follower_count: data.max_follower_count || 0,
      min_engagement_rate: data.min_engagement_rate || 0,
      updated_at: Timestamp.now()
    };

    // Update Firestore
    await campaignRef.update(updateData);

    // Update Cloud Storage for backward compatibility
    const updatedCampaign = { ...campaignDoc.data(), ...updateData };
    await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/campaign_analysis.json`, updatedCampaign, campaignRef);

    return new Campaign(updatedCampaign);
  }
}

export default Campaign;

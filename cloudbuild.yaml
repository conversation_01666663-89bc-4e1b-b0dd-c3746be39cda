steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/studious-booth-446123-h5/palas-influencer-intelligence:latest', '.']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/studious-booth-446123-h5/palas-influencer-intelligence:latest']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'palas-influencer-intelligence'
      - '--image=gcr.io/studious-booth-446123-h5/palas-influencer-intelligence:latest'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=2Gi'
      - '--cpu=2'
      - '--min-instances=1'
      - '--max-instances=10'
      - '--set-env-vars=NODE_ENV=production'
      - '--set-secrets=FIREBASE_SERVICE_ACCOUNT=firebase-service-account:latest,OPENAI_API_KEY=openai-api-key:latest,INFLUENCERS_API_KEY=influencers-api-key:latest'
      - '--set-env-vars=STORAGE_BUCKET=palas-run-cache'

images:
  - 'gcr.io/studious-booth-446123-h5/palas-influencer-intelligence:latest'

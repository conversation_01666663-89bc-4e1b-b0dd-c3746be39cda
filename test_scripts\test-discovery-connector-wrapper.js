/**
 * test-discovery-connector-wrapper.js
 * A wrapper for the Influencers Club Discovery connector to fix API issues
 */

import InfluencersClubDiscoveryConnector from './connectors/influencers-club-discovery-connector.js';
import axios from 'axios';

/**
 * A wrapper for the Influencers Club Discovery connector
 * that fixes issues with the API
 */
class DiscoveryConnectorWrapper extends InfluencersClubDiscoveryConnector {
  /**
   * Override the discoverInfluencers method to fix the sort parameter
   * @param {Object} params - The discovery parameters
   * @returns {Object} - The discovery results
   */
  async discoverInfluencers(params) {
    const url = "https://api-dashboard.influencers.club/public/v1/discovery/";

    // Prepare request payload with correct sort parameter
    const payload = {
      platform: params.main_platform || "instagram",
      paging: {
        limit: params.limit || 10, // Default to 10 for production, can be overridden
        page: params.page || 1
      },
      // Add sort parameter with correct values according to API documentation
      sort: {
        sort_by: "relevancy",
        sort_order: "asc"  // Must be lowercase 'asc' or 'desc' according to API validation
      },
      filters: {
        // Primary search parameter
        ai_search: params.influencer_description,

        // Secondary parameters
        number_of_followers: {
          min: params.min_follower_count || 10000,
          max: params.max_follower_count || null
        }
      }
    };

    // Add optional filters if provided
    if (params.min_likes) {
      payload.filters.average_likes = {
        min: params.min_likes,
        max: null
      };
    }

    if (params.min_comments) {
      payload.filters.average_comments = {
        min: params.min_comments,
        max: null
      };
    }

    if (params.min_views) {
      payload.filters.average_views = {
        min: params.min_views,
        max: null
      };
    }

    if (params.location) {
      payload.filters.location = params.location;
    }

    if (params.gender) {
      payload.filters.gender = params.gender;
    }

    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };

    // Make request
    try {
      console.log(`Discovering influencers with AI search: "${params.influencer_description.substring(0, 50)}..."`);
      console.log('Request payload:', JSON.stringify(payload, null, 2));
      const response = await axios.post(url, payload, { headers });
      return response.data;
    } catch (error) {
      // Handle errors
      if (error.response) {
        console.error(`Error ${error.response.status}: ${error.response.statusText}`);
        console.error('Error response data:', JSON.stringify(error.response.data, null, 2));

        // Detailed error inspection for validation errors
        if (error.response.data && Array.isArray(error.response.data)) {
          error.response.data.forEach((errorItem, index) => {
            console.error(`Error item ${index + 1}:`, JSON.stringify(errorItem, null, 2));

            // If there are nested objects, inspect them
            if (errorItem.error && Array.isArray(errorItem.error)) {
              errorItem.error.forEach((nestedError, nestedIndex) => {
                console.error(`  Nested error ${nestedIndex + 1}:`, JSON.stringify(nestedError, null, 2));
              });
            }
          });
        }
      } else if (error.request) {
        console.error('No response received from server:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }
      throw error;
    }
  }
}

export default DiscoveryConnectorWrapper;

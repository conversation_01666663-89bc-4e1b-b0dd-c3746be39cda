3. Discovery API
Header Parameters
Authorization
string
Required
Find your API key on the Profile Page after logging into your account.

Body ParametersExpand all
platform
string
Required
Enum values:
instagram
youtube
tiktok
twitch
twitter
onlyfans
paging
object
Show child attributes

sort
object
Show child attributes

filters
object
Show child attributes

ResponseExpand all
200
Object
Response Attributes
total
integer
Required
limit
integer
Required
credits_left
string (decimal)
Required
accounts
array
Show child attributes

Was this section helpful?
Yes
No
POST

/public/v1/discovery/

Python


import requests
import json

url = "https://api-dashboard.influencers.club/public/v1/discovery/"

payload = json.dumps({
  "platform": "instagram",
  "paging": {
    "limit": None,
    "page": None
  },
  "sort": {
    "sort_by": "relevancy",
    "sort_order": "ASC"
  },
  "filters": {
    "location": "",
    "type": "",
    "gender": "",
    "profile_language": "",
    "ai_search": "",
    "number_of_followers": {
      "min": None,
      "max": None
    },
    "posting_frequency": None,
    "follower_growth": {
      "growth_percentage": None,
      "time_range_months": None
    },
    "average_likes": {
      "min": None,
      "max": None
    },
    "average_comments": {
      "min": None,
      "max": None
    },
    "average_views": {
      "min": None,
      "max": None
    },
    "average_video_downloads": {
      "min": None,
      "max": None
    },
    "has_tik_tok_shop": False,
    "exclude_private_profile": False,
    "is_verified": False,
    "keywords_in_bio": [
      ""
    ],
    "keywords_in_tweets": [
      ""
    ],
    "number_of_tweets": {
      "min": None,
      "max": None
    },
    "last_post": "",
    "number_of_subscribers": {
      "min": None,
      "max": None
    },
    "topics": [
      ""
    ],
    "keywords_in_video_titles": [
      ""
    ],
    "keywords_in_description": [
      ""
    ],
    "keywords_in_video_description": [
      ""
    ],
    "subscriber_growth": {
      "growth_percentage": None,
      "time_range_months": None
    },
    "has_shorts": False,
    "shorts_percentage": {
      "min": None,
      "max": None
    },
    "has_community_posts": False,
    "streams_live": False,
    "has_merch": False,
    "average_views_on_long_videos": {
      "min": None,
      "max": None
    },
    "average_views_on_shorts": {
      "min": None,
      "max": None
    },
    "number_of_videos": {
      "min": None,
      "max": None
    },
    "is_monetizing": False,
    "number_of_posts": {
      "min": None,
      "max": None
    },
    "reels_percent": {
      "min": None,
      "max": None
    },
    "average_views_for_reels": {
      "min": None,
      "max": None
    },
    "subscription_price": {
      "min": None,
      "max": None
    },
    "number_of_photos": {
      "min": None,
      "max": None
    },
    "number_of_likes": {
      "min": None,
      "max": None
    },
    "followers": {
      "min": None,
      "max": None
    },
    "active_subscribers": {
      "min": None,
      "max": None
    },
    "streamed_hours_last_30_days": {
      "min": None,
      "max": None
    },
    "total_hours_streamed": {
      "min": None,
      "max": None
    },
    "maximum_views_count": {
      "min": None,
      "max": None
    },
    "avg_views_last_30_days": {
      "min": None,
      "max": None
    },
    "streams_count_last_30_days": {
      "min": None,
      "max": None
    },
    "games_played": [
      ""
    ],
    "is_twitch_partner": False
  }
})
headers = {
  'Authorization': 'Bearer YOUR API KEY',
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
Response

200
{
  "total": null,
  "limit": null,
  "credits_left": "",
  "accounts": [
    {
      "user_id": "",
      "profile": {
        "full_name": "",
        "username": "",
        "picture": "",
        "followers": null,
        "engagement_percent": null
      }
    }
  ]
}

Fetch Available Twitch Games
Header Parameters
Authorization
string
Required
Find your API key on the Profile Page after logging into your account.

ResponseExpand all
200
Object
Response Attributes
topic_details
string
Required
Min length
1
sub_topic_details
array
Required
Show child attributes

Was this section helpful?
Yes
No
GET

/public/v1/discovery/classifier/games/

Python


import requests
import json

url = "https://api-dashboard.influencers.club/public/v1/discovery/classifier/games/"

payload = {}
headers = {
  'Authorization': 'Bearer YOUR API KEY',
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
Response

200
[
  {
    "topic_details": "",
    "sub_topic_details": [
      ""
    ]
  }
]

Fetch Available Languages
Header Parameters
Authorization
string
Required
Find your API key on the Profile Page after logging into your account.

ResponseExpand all
200
Object
Response Attributes
language
string
Required
Min length
1
abbreviation
string
Required
Min length
1
Was this section helpful?
Yes
No
GET

/public/v1/discovery/classifier/languages/

Python


import requests
import json

url = "https://api-dashboard.influencers.club/public/v1/discovery/classifier/languages/"

payload = {}
headers = {
  'Authorization': 'Bearer YOUR API KEY',
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
Response

200
[
  {
    "language": "",
    "abbreviation": ""
  }
]

Fetch Available Locations for Platform
Header Parameters
Authorization
string
Required
Find your API key on the Profile Page after logging into your account.

Path Parameters
platform
string
Required
Enum values:
instagram
youtube
tiktok
twitch
twitter
onlyfans
Response
200
Object
A list of strings representing platform details
Response Attributes
undefined
array
Was this section helpful?
Yes
No
GET

/public/v1/discovery/classifier/locations/{platform}/

Python


import requests
import json

url = "https://api-dashboard.influencers.club/public/v1/discovery/classifier/locations/{platform}/"

payload = {}
headers = {
  'Authorization': 'Bearer YOUR API KEY',
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
Response

200
[
  ""
]

Fetch Available Youtube Topics
Header Parameters
Authorization
string
Required
Find your API key on the Profile Page after logging into your account.

ResponseExpand all
200
Object
Response Attributes
topic_details
string
Required
Min length
1
sub_topic_details
array
Required
Show child attributes

Was this section helpful?
Yes
No
GET

/public/v1/discovery/classifier/yt-topics/

Python


import requests
import json

url = "https://api-dashboard.influencers.club/public/v1/discovery/classifier/yt-topics/"

payload = {}
headers = {
  'Authorization': 'Bearer YOUR API KEY',
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
Response

200
[
  {
    "topic_details": "",
    "sub_topic_details": [
      ""
    ]
  }
]

Supported Platforms
Currently Integrated:
Instagram
TikTok
YouTube
OnlyFans
Twitter
Twitch
Planned Integrations:
LinkedIn
Reddit
Facebook
Pinterest
Discord
Douyin
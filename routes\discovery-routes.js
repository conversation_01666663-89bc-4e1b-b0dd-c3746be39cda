// discovery-routes.js
// Discovery-related routes

import express from 'express';
import { discoverInfluencers } from '../services/discovery-service.js';
import { generateCampaignBrief } from '../services/campaign-service.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';
import { getFirestore } from 'firebase-admin/firestore';

const router = express.Router();

// The seed influencers route has been removed as it's been replaced by the Discovery API

/**
 * Discover influencers for a campaign
 * POST /api/discovery/discover
 */
router.post('/discover', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID, campaignId, campaignData } = req.body;

    // Validate required parameters
    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    if (!campaignId) {
      return res.status(400).json({ error: 'Campaign ID is required' });
    }

    if (!campaignData || !campaignData.influencer_description) {
      return res.status(400).json({ error: 'Campaign data with influencer_description is required' });
    }

    const result = await discoverInfluencers(clientId, campaignId, campaignData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers', message: error.message });
  }
});

/**
 * Discover influencers for a campaign by ID
 * POST /api/discovery/campaigns/:campaignId/discover
 */
router.post('/campaigns/:campaignId/discover', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId = DEFAULT_CLIENT_ID } = req.body;

    // Validate client ID
    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    // Get the campaign data
    const db = getFirestore();
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
    const campaignDoc = await campaignRef.get();

    if (!campaignDoc.exists) {
      return res.status(404).json({ error: `Campaign not found: ${campaignId}` });
    }

    const campaignData = campaignDoc.data();

    // Validate campaign data
    if (!campaignData.influencer_description) {
      return res.status(400).json({ error: 'Campaign data is missing influencer_description' });
    }

    // Discover influencers
    const result = await discoverInfluencers(clientId, campaignId, campaignData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers', message: error.message });
  }
});

/**
 * Discover influencers from a campaign request
 * POST /api/discovery/discover-from-request
 *
 * This endpoint combines the campaign brief generation and discovery processes:
 * 1. Takes a campaign request as input
 * 2. Generates a campaign brief
 * 3. Uses the brief to discover influencers
 * 4. Returns the discovered influencers
 */
router.post('/discover-from-request', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID } = req.body;

    // Validate client ID
    if (!clientId) {
      return res.status(400).json({ error: 'Client ID is required' });
    }

    // Validate campaign data
    if (!req.body.campaign || !req.body.campaign.name) {
      return res.status(400).json({ error: 'Campaign data with name is required' });
    }

    console.log('Generating campaign brief from request...');

    // Step 1: Generate campaign brief
    // Ensure client_id is properly passed to generateCampaignBrief
    const briefInput = {
      ...req.body,
      client_id: clientId  // Add the extracted clientId as client_id for generateCampaignBrief
    };
    const briefResult = await generateCampaignBrief(briefInput);
    const campaignId = briefResult.campaignId;
    const campaignData = briefResult.campaignData;

    console.log(`Campaign brief generated with ID: ${campaignId}`);

    // Step 2: Discover influencers using the generated brief
    console.log('Discovering influencers based on generated brief...');
    const discoveryResult = await discoverInfluencers(clientId, campaignId, campaignData);

    // Step 3: Return the combined result
    const result = {
      campaign: {
        id: campaignId,
        ...campaignData
      },
      influencers: discoveryResult.similar_accounts || []
    };

    // Log the final combined result being returned to the client
    console.log(`Combined result from discover-from-request: ${JSON.stringify(result)}`);

    res.status(200).json(result);
  } catch (error) {
    console.error('Error in discover-from-request flow:', error);
    res.status(500).json({ error: 'Failed to process discovery request', message: error.message });
  }
});

export default router;

# PowerShell script to clean up old secret versions
# Project: Palas Influencer Intelligence Platform

# Set environment variables
$env:PROJECT_ID = "studious-booth-446123-h5"

Write-Host "Starting secret cleanup process for project: $env:PROJECT_ID" -ForegroundColor Green

# Function to clean up old versions of a secret
function Cleanup-SecretVersions {
    param (
        [string]$SecretName,
        [int]$KeepLatest = 1  # Number of latest versions to keep
    )
    
    Write-Host "Cleaning up old versions of secret: $SecretName" -ForegroundColor Cyan
    
    # List all versions of the secret
    $versions = $(gcloud secrets versions list $SecretName --project=$env:PROJECT_ID --format="json") | ConvertFrom-Json
    
    if (-not $versions -or $versions.Count -eq 0) {
        Write-Host "No versions found for secret: $SecretName" -ForegroundColor Yellow
        return
    }
    
    # Sort versions by creation time (newest first)
    $sortedVersions = $versions | Sort-Object -Property createTime -Descending
    
    # Keep track of versions to delete
    $versionsToDelete = @()
    
    # Skip the latest N versions and mark the rest for deletion
    for ($i = $KeepLatest; $i -lt $sortedVersions.Count; $i++) {
        $versionsToDelete += $sortedVersions[$i].name.Split('/')[-1]
    }
    
    # If there are versions to delete
    if ($versionsToDelete.Count -gt 0) {
        Write-Host "Found $($versionsToDelete.Count) old version(s) to delete for secret: $SecretName" -ForegroundColor Yellow
        
        # Delete each version
        foreach ($version in $versionsToDelete) {
            Write-Host "Deleting version $version of secret: $SecretName" -ForegroundColor Cyan
            gcloud secrets versions destroy $version --secret=$SecretName --project=$env:PROJECT_ID
        }
        
        Write-Host "Successfully cleaned up old versions of secret: $SecretName" -ForegroundColor Green
    } else {
        Write-Host "No old versions to clean up for secret: $SecretName" -ForegroundColor Green
    }
}

# Clean up each secret
try {
    # List of secrets to clean up
    $secrets = @(
        "firebase-service-account",
        "openai-api-key",
        "influencers-api-key"
    )
    
    foreach ($secret in $secrets) {
        Cleanup-SecretVersions -SecretName $secret -KeepLatest 1
    }
    
    Write-Host "Secret cleanup completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error during secret cleanup: $_" -ForegroundColor Red
}

// firebase.js
// Firebase initialization and configuration

import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';

/**
 * Initialize Firebase Admin SDK
 * @param {Object} serviceAccount - The service account credentials
 * @param {string} storageBucket - The storage bucket name
 * @returns {Object} - The initialized Firebase app and Firestore instance
 */
function initializeFirebase(serviceAccount, storageBucket) {
  try {
    // Check if Firebase is already initialized
    let app;
    try {
      app = initializeApp({
        credential: cert(serviceAccount),
        storageBucket: storageBucket
      });
    } catch (error) {
      if (error.code === 'app/duplicate-app') {
        console.log('Firebase already initialized');
        app = initializeApp();
      } else {
        throw error;
      }
    }

    // Initialize Firestore with settings
    const db = getFirestore();
    db.settings({
      ignoreUndefinedProperties: true
    });

    // Initialize Storage
    const storage = getStorage(app);

    console.log('Firebase initialized successfully');
    return { app, db, storage };
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error;
  }
}

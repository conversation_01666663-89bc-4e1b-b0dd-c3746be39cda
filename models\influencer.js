// influencer.js
// Influencer data model

import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { validateInfluencerData } from '../utils/validation-utils.js';

/**
 * Influencer model
 */
class Influencer {
  /**
   * Create a new Influencer instance
   * @param {Object} data - The influencer data
   */
  constructor(data = {}) {
    this.username = data.username || '';
    this.full_name = data.full_name || '';
    this.email = data.email || '';
    this.location = data.location || '';
    this.speaking_language = data.speaking_language || '';
    this.has_brand_deals = data.has_brand_deals || false;
    this.has_link_in_bio = data.has_link_in_bio || false;
    this.is_business = data.is_business || false;
    this.is_creator = data.is_creator || false;
    this.platforms = data.platforms || {};
    this.creator_has = data.creator_has || [];
    this.last_updated = data.last_updated || null;
    this.created_at = data.created_at || null;
  }

  /**
   * Find an influencer by username
   * @param {string} username - The username
   * @returns {Influencer} - The influencer
   */
  static async findByUsername(username) {
    const db = getFirestore();

    // Get the influencer document
    const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
    const influencerSnapshot = await influencerRef.get();

    if (influencerSnapshot.empty) {
      return null;
    }

    return new Influencer(influencerSnapshot.docs[0].data());
  }

  /**
   * Create a new influencer in Firestore
   * @param {Object} data - The influencer data
   * @returns {string} - The influencer ID
   */
  static async create(data) {
    const db = getFirestore();

    // Validate influencer data
    const validation = validateInfluencerData(data);
    if (!validation.isValid) {
      throw new Error(`Invalid influencer data: ${validation.errors.join(', ')}`);
    }

    // Check if influencer already exists
    const existingInfluencer = await Influencer.findByUsername(data.username);
    if (existingInfluencer) {
      throw new Error(`Influencer already exists: ${data.username}`);
    }

    // Create a new influencer document
    const influencerRef = db.collection('influencers').doc();
    const influencerId = influencerRef.id;

    // Prepare influencer data
    const influencerData = {
      username: data.username,
      full_name: data.full_name || '',
      email: data.email || '',
      location: data.location || '',
      speaking_language: data.speaking_language || '',
      has_brand_deals: data.has_brand_deals || false,
      has_link_in_bio: data.has_link_in_bio || false,
      is_business: data.is_business || false,
      is_creator: data.is_creator || false,
      platforms: data.platforms || {},
      creator_has: data.creator_has || [],
      created_at: Timestamp.now(),
      last_updated: Timestamp.now()
    };

    // Save to Firestore
    await influencerRef.set(influencerData);

    return influencerId;
  }

  /**
   * Update an influencer
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The influencer data
   * @returns {Influencer} - The updated influencer
   */
  static async update(influencerId, data) {
    const db = getFirestore();

    // Validate influencer data
    const validation = validateInfluencerData(data);
    if (!validation.isValid) {
      throw new Error(`Invalid influencer data: ${validation.errors.join(', ')}`);
    }

    // Get the influencer document
    const influencerRef = db.collection('influencers').doc(influencerId);
    const influencerDoc = await influencerRef.get();

    if (!influencerDoc.exists) {
      throw new Error(`Influencer not found: ${influencerId}`);
    }

    // Prepare update data
    const updateData = {
      username: data.username,
      full_name: data.full_name || '',
      email: data.email || '',
      location: data.location || '',
      speaking_language: data.speaking_language || '',
      has_brand_deals: data.has_brand_deals || false,
      has_link_in_bio: data.has_link_in_bio || false,
      is_business: data.is_business || false,
      is_creator: data.is_creator || false,
      platforms: data.platforms || {},
      creator_has: data.creator_has || [],
      last_updated: Timestamp.now()
    };

    // Update Firestore
    await influencerRef.update(updateData);

    return new Influencer({ ...influencerDoc.data(), ...updateData });
  }
}

export default Influencer;

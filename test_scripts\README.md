# Test Scripts for Palas Influencer Intelligence Platform

This directory contains test scripts for testing various components of the Palas Influencer Intelligence Platform.

## Available Test Scripts

### 1. Discovery Endpoint Test

Tests the Discovery API endpoint that takes a campaign description and returns potential influencers.

**Usage:**
```bash
node test-discovery-endpoint.js --file=sample_jsons/campaign_analysis.json
```

or

```bash
node test-discovery-endpoint.js --campaign_id=existing_campaign_id
```

**Options:**
- `--file=<path>`: Path to a JSON file containing campaign data
- `--campaign_id=<id>`: ID of an existing campaign to use
- `--client_id=<id>`: Client ID (default: 'default_client')

### 2. Enrichment Endpoint Test

Tests the Influencer Enrichment API endpoint that takes an influencer username and enriches it with data from Influencers Club and AI analysis.

**Usage:**
```bash
node test-enrichment-endpoint.js --username=influencer_username --campaign_id=campaign_id
```

**Options:**
- `--username=<username>`: Instagram username of the influencer to enrich
- `--campaign_id=<id>`: ID of the campaign to associate with the influencer
- `--client_id=<id>`: Client ID (default: 'default_client')
- `--platform=<platform>`: Platform (default: 'instagram')
- `--force_update=<bool>`: Force update even if data exists (default: false)
- `--full_analysis=<bool>`: Also test the full analysis endpoint (default: false)

### 3. Analysis Flow Test

Tests the complete analysis flow for an influencer.

**Usage:**
```bash
node test-analysis-flow.js --username=influencer_username
```

**Options:**
- `--username=<username>`: Instagram username of the influencer to analyze
- `--client_id=<id>`: Client ID (default: 'default_client')
- `--campaign_id=<id>`: Campaign ID (default: generated)
- `--force_update=<bool>`: Force update even if data exists (default: false)

### 4. Discovery Connector Test

Tests the Influencers Club Discovery connector directly.

**Usage:**
```bash
node test-discovery-connector.js --ai_search="Fitness influencers who focus on strength training"
```

**Options:**
- `--ai_search=<text>`: The AI search description (required)
- `--platform=<platform>`: Platform to search (default: instagram)
- `--limit=<number>`: Number of results to return (default: 10)
- `--min_followers=<number>`: Minimum follower count (default: 10000)
- `--max_followers=<number>`: Maximum follower count (optional)
- `--store_in_firestore=<bool>`: Whether to store results in Firestore (default: false)

## Test Flow

The test scripts follow the two main flows of the platform:

### Discovery Flow

1. User submits a campaign description
2. The description is sent to be made into a campaign brief
3. This campaign brief is sent to the Discovery API to get potential influencers
4. The JSON is sent to the interface for selection

### Enrichment Flow

1. Each selected influencer is sent separately to the system
2. The system checks for Influencers Club enrichment data (no older than a month)
3. If there is data, it pulls that from Firestore, otherwise it pulls from the Influencers Club API
4. A deep dive web search is done for the influencer
5. A deep dive visual analysis of the profile picture and images is done
6. A detailed ROI analysis is done
7. All metrics are standardized and stored in Firestore

## Output

All test scripts save their output to the `test_outputs` directory with timestamp-based filenames for easy reference and comparison.

## Prerequisites

- Node.js 18 or higher
- Firebase credentials configured
- API keys for Influencers Club and OpenAI
- Deployed server running at https://palas-influencer-intelligence-9815718377.us-central1.run.app

The test scripts are configured to use the deployed Cloud Run service. No local server is required.

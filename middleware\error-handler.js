// error-handler.js
// Error handling middleware

/**
 * Error handler middleware
 * @param {Error} err - The error
 * @param {Object} req - The request
 * @param {Object} res - The response
 * @param {Function} next - The next middleware
 */
function errorHandler(err, req, res, next) {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
}

export { errorHandler };

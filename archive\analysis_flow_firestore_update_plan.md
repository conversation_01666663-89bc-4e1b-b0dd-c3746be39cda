# Analysis Flow Firestore Update Plan

This document outlines the necessary changes to update the `analysisFlow.js` file to use the new Firestore database structure instead of JSON files. The changes are organized into logical chunks to facilitate a methodical implementation.

## Overview of Current Flow

The current `analysisFlow.js` follows this general flow:

1. **Campaign Analysis**: Process campaign data and create a campaign brief
2. **Influencer Discovery**: Find and rank potential influencers
3. **Influencer Enrichment**: Fetch detailed data from Influencers Club API
4. **Web Analysis**: Perform web search to gather reputation insights
5. **Aesthetic Analysis**: Analyze visual content and style
6. **ROI Analysis**: Evaluate potential ROI and strategic fit

Currently, all data is stored in JSON files in Google Cloud Storage. We need to transition to using Firestore while maintaining the same functionality.

## Required Changes

### 1. Firebase/Firestore Initialization

**Current Implementation:**
```javascript
const storage = new Storage(); // Automatically uses GOOGLE_APPLICATION_CREDENTIALS
const bucketName = 'palas-run-cache'; // Replace with your bucket name
```

**Firestore Implementation:**
```javascript
// Add Firebase initialization
import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Firebase
const app = initializeApp({
  credential: cert(serviceAccount),
  storageBucket: bucketName
});

// Initialize Firestore
const db = getFirestore();
db.settings({
  ignoreUndefinedProperties: true
});
```

### 2. Campaign Analysis Storage

**Current Implementation:**
```javascript
await writeJsonToBucket(`/runs/${report_id}_campaign_analysis.json`, campaignJSON);
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function storeCampaignAnalysis(clientId, campaignId, campaignData) {
  // Create a new campaign document in Firestore
  await db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId).set({
    name: campaignData.name,
    report_id: campaignData.report_id,
    product_description: campaignData.product_description,
    influencer_gender: campaignData.influencer_gender,
    influencer_niche: campaignData.influencer_niche,
    influencer_age: campaignData.influencer_age,
    influencer_personality: campaignData.influencer_personality,
    influencer_aesthetic: campaignData.influencer_aesthetic,
    min_follower_count: campaignData.min_follower_count,
    max_follower_count: campaignData.max_follower_count,
    min_engagement_rate: campaignData.min_engagement_rate,
    created_at: db.Timestamp.now(),
    updated_at: db.Timestamp.now(),
    status: "active"
  });
  
  // Also store the raw JSON in Cloud Storage for backward compatibility
  await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/campaign_analysis.json`, campaignData);
  
  return campaignId;
}
```

### 3. Influencer Discovery Storage

**Current Implementation:**
```javascript
await writeJsonToBucket(`/runs/${report_id}_discovery_results.json`, discoveryJSON);
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function storeDiscoveredInfluencers(clientId, campaignId, discoveryData) {
  // Create a new discovery document in Firestore
  const discoveryRef = await db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('discovered_influencers').doc();
    
  await discoveryRef.set({
    similar_accounts: discoveryData.similar_accounts,
    created_at: db.Timestamp.now()
  });
  
  // Also store the raw JSON in Cloud Storage for backward compatibility
  await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/discovery_results.json`, discoveryData);
  
  return discoveryRef.id;
}
```

### 4. Influencer Enrichment

**Current Implementation:**
```javascript
const filePath1 = `influencers/${username}_raw_data.json`;
enrichmentJSON = await getCachedJson(bucketName, filePath1);
if (enrichmentJSON === false) {
  console.log("No cached data found. Triggering API call...");
  enrichmentJSON = await updateInfluencerFullReport(username);
  await writeJsonToBucket(filePath1, enrichmentJSON);
}
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function enrichInfluencer(username, platform = "instagram") {
  // Check if influencer exists in Firestore
  const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
  const influencerSnapshot = await influencerRef.get();
  
  let influencerId;
  if (influencerSnapshot.empty) {
    // Create a new influencer document
    const newInfluencerRef = db.collection('influencers').doc();
    influencerId = newInfluencerRef.id;
  } else {
    // Use existing influencer document
    influencerId = influencerSnapshot.docs[0].id;
  }
  
  // Use the Influencers Club connector to fetch and process data
  const connector = new InfluencersClubConnector(INFLUENCERS_API_KEY, bucketName);
  const { processedData } = await connector.enrichInfluencer(username, platform);
  
  return { influencerId, processedData };
}
```

### 5. Web Analysis Storage

**Current Implementation:**
```javascript
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_web_analysis.json`, deepDiveResultsJSON);
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function storeWebAnalysis(influencerId, webAnalysisData) {
  // Create a new web analysis document in Firestore
  const webAnalysisRef = await db.collection('influencers').doc(influencerId)
    .collection('web_analysis').doc();
    
  await webAnalysisRef.set({
    name: webAnalysisData.name,
    sentiment_score: webAnalysisData.sentiment_score,
    risk_level: webAnalysisData.risk_level,
    deep_dive_report: webAnalysisData.deep_dive_report,
    created_at: db.Timestamp.now(),
    updated_at: db.Timestamp.now()
  });
  
  // Also store the raw JSON in Cloud Storage for backward compatibility
  await writeJsonToBucket(`/influencers/${influencerId}/web_analysis/${webAnalysisRef.id}.json`, webAnalysisData);
  
  return webAnalysisRef.id;
}
```

### 6. Aesthetic Analysis Storage

**Current Implementation:**
```javascript
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_aesthetic_analysis.json`, aestheticAnalysisJSON);
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function storeAestheticAnalysis(clientId, campaignId, influencerId, aestheticAnalysisData) {
  // Create a new aesthetic analysis document in Firestore
  const aestheticAnalysisRef = await db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('campaign_influencers').doc(influencerId)
    .collection('aesthetic_analysis').doc();
    
  await aestheticAnalysisRef.set({
    name: aestheticAnalysisData.name,
    brand_fit_score: aestheticAnalysisData.brand_fit_score,
    content_analysis: aestheticAnalysisData.content_analysis,
    created_at: db.Timestamp.now()
  });
  
  // Also store the raw JSON in Cloud Storage for backward compatibility
  await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/aesthetic_analysis.json`, aestheticAnalysisData);
  
  return aestheticAnalysisRef.id;
}
```

### 7. ROI Analysis Storage

**Current Implementation:**
```javascript
// ROI analysis is currently stored in a similar way to the other analyses
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_roi_analysis.json`, roiAnalysisJSON);
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function storeROIAnalysis(clientId, campaignId, influencerId, roiAnalysisData) {
  // Create a new ROI analysis document in Firestore
  const roiAnalysisRef = await db.collection('clients').doc(clientId)
    .collection('campaigns').doc(campaignId)
    .collection('campaign_influencers').doc(influencerId)
    .collection('roi_analysis').doc();
    
  await roiAnalysisRef.set({
    brand_fit_score: roiAnalysisData.brand_fit_score,
    brand_fit_description: roiAnalysisData.brand_fit_description,
    risk_level: roiAnalysisData.risk_level,
    risk_description: roiAnalysisData.risk_description,
    influencer_analysis: roiAnalysisData.influencer_analysis,
    created_at: db.Timestamp.now()
  });
  
  // Also store the raw JSON in Cloud Storage for backward compatibility
  await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/roi_analysis.json`, roiAnalysisData);
  
  return roiAnalysisRef.id;
}
```

### 8. Helper Functions for Combined and Merged Analyses

**Current Implementation:**
The current implementation combines JSON files to create combined_analyses.json and merged_analyses.json.

**Firestore Implementation:**
```javascript
// Pseudocode
async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  // Get the campaign influencer document
  const campaignInfluencerRef = db.collection('clients')
    .doc(clientId)
    .collection('campaigns')
    .doc(campaignId)
    .collection('campaign_influencers')
    .doc(influencerId);
  
  // Get the aesthetic analysis
  const aestheticAnalysisSnapshot = await campaignInfluencerRef
    .collection('aesthetic_analysis')
    .orderBy('created_at', 'desc')
    .limit(1)
    .get();
  
  // Get the ROI analysis
  const roiAnalysisSnapshot = await campaignInfluencerRef
    .collection('roi_analysis')
    .orderBy('created_at', 'desc')
    .limit(1)
    .get();
  
  // Get the web analysis
  const webAnalysisSnapshot = await db.collection('influencers')
    .doc(influencerId)
    .collection('web_analysis')
    .orderBy('created_at', 'desc')
    .limit(1)
    .get();
  
  // Combine all data into a structured object
  const combinedAnalysis = {
    profileInfo: {
      metrics: {},
      aestheticAnalysis: {}
    },
    roiProjection: {},
    webAnalysis: {}
  };
  
  // Add aesthetic analysis data
  if (!aestheticAnalysisSnapshot.empty) {
    const aestheticAnalysis = aestheticAnalysisSnapshot.docs[0].data();
    // Populate combinedAnalysis.profileInfo.metrics and combinedAnalysis.profileInfo.aestheticAnalysis
  }
  
  // Add ROI analysis data
  if (!roiAnalysisSnapshot.empty) {
    const roiAnalysis = roiAnalysisSnapshot.docs[0].data();
    // Populate combinedAnalysis.roiProjection
  }
  
  // Add web analysis data
  if (!webAnalysisSnapshot.empty) {
    const webAnalysis = webAnalysisSnapshot.docs[0].data();
    // Populate combinedAnalysis.webAnalysis
  }
  
  return combinedAnalysis;
}

async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  // Get the combined analysis
  const combinedAnalysis = await generateCombinedAnalysis(clientId, campaignId, influencerId);
  
  // Get the processed influencer data
  const influencerSnapshot = await db.collection('influencers').doc(influencerId).get();
  const influencerData = influencerSnapshot.data();
  
  // Get the latest processed data reference
  const rawDataSnapshot = await db.collection('influencers')
    .doc(influencerId)
    .collection('raw_data')
    .orderBy('processed_at', 'desc')
    .limit(1)
    .get();
  
  if (rawDataSnapshot.empty) {
    throw new Error(`No processed data found for influencer: ${influencerId}`);
  }
  
  const rawDataDoc = rawDataSnapshot.docs[0].data();
  const processedStoragePath = rawDataDoc.processed_storage_path;
  
  // Get the processed data from Cloud Storage
  const bucket = storage.bucket(bucketName);
  const file = bucket.file(processedStoragePath);
  
  const [contents] = await file.download();
  const processedData = JSON.parse(contents.toString('utf8'));
  
  // Merge the combined analysis with the processed data
  return deepMerge(combinedAnalysis, processedData);
}
```

### 9. Update Main Analysis Flow Function

**Current Implementation:**
```javascript
async function performInfluencerAnalysis(input) {
  // Phase 1: Campaign Brief & Audience Analysis
  // Phase 2: Broad Influencer Discovery
  // Phase 3: Deep-Dive Web Search & Historical Analysis
  // Phase 4: Influencer Enrichment
  // Phase 5: Visual & Content Analysis
  // Phase 6: ROI & Strategic Fit Evaluation
}
```

**Firestore Implementation:**
```javascript
// Pseudocode
async function performInfluencerAnalysis(input) {
  const clientId = input.client_id;
  let campaignId, influencerId;
  
  // Phase 1: Campaign Brief & Audience Analysis
  if (input.report_id) {
    // Use existing campaign
    campaignId = input.report_id;
  } else {
    // Create new campaign
    const campaignJSON = await processAgent('asst_8z7v7LffCsuh2Ez9tBtStYbg', phase1Prompt);
    campaignId = await storeCampaignAnalysis(clientId, null, campaignJSON);
  }
  
  // Phase 2: Broad Influencer Discovery
  if (input.selected_account) {
    // Use selected influencer
    const username = input.selected_account;
    // Check if influencer exists in Firestore
    const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
    const influencerSnapshot = await influencerRef.get();
    
    if (influencerSnapshot.empty) {
      // Create new influencer
      const { influencerId: newInfluencerId } = await enrichInfluencer(username);
      influencerId = newInfluencerId;
    } else {
      // Use existing influencer
      influencerId = influencerSnapshot.docs[0].id;
    }
    
    // Add influencer to campaign
    await db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .set({
        influencer_id: influencerId,
        username: username,
        status: "selected",
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now()
      });
  } else {
    // Discover influencers
    const discoveryJSON = await processAgent('asst_we8mAAj5oluautOVautmiJqn', discoveryPrompt);
    await storeDiscoveredInfluencers(clientId, campaignId, discoveryJSON);
    return discoveryJSON;
  }
  
  // Phase 3: Influencer Enrichment
  const username = input.selected_account;
  const { processedData } = await enrichInfluencer(username);
  
  // Phase 4: Deep-Dive Web Search & Historical Analysis
  const deepDiveResultsJSON = await performWebAnalysis(username);
  await storeWebAnalysis(influencerId, deepDiveResultsJSON);
  
  // Phase 5: Visual & Content Analysis
  const influencerImagesBase64 = await processInstagramImagePosts(processedData);
  const aestheticAnalysisJSON = await performAestheticAnalysis(username, influencerImagesBase64);
  await storeAestheticAnalysis(clientId, campaignId, influencerId, aestheticAnalysisJSON);
  
  // Phase 6: ROI & Strategic Fit Evaluation
  const roiAnalysisJSON = await performROIAnalysis(username, processedData, deepDiveResultsJSON, aestheticAnalysisJSON);
  await storeROIAnalysis(clientId, campaignId, influencerId, roiAnalysisJSON);
  
  // Generate merged analysis
  const mergedAnalysis = await generateMergedAnalysis(clientId, campaignId, influencerId);
  
  return mergedAnalysis;
}
```

## API Endpoints Update

The API endpoints need to be updated to use Firestore:

```javascript
// Pseudocode
app.post('/api/campaigns', async (req, res) => {
  try {
    const { campaignData, clientId } = req.body;
    const campaignId = await storeCampaignAnalysis(clientId, null, campaignData);
    res.status(200).json({ campaignId });
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({ error: 'Failed to create campaign' });
  }
});

app.post('/api/campaigns/:campaignId/discover', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId } = req.body;
    const discoveryData = await discoverInfluencers(campaignId, clientId);
    res.status(200).json(discoveryData);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers' });
  }
});

app.post('/api/campaigns/:campaignId/enrich', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { influencerUsername, clientId } = req.body;
    const result = await enrichInfluencer(influencerUsername, campaignId, clientId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error enriching influencer:', error);
    res.status(500).json({ error: 'Failed to enrich influencer' });
  }
});

app.get('/api/campaigns/:campaignId/influencers/:influencerId', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId } = req.query;
    const result = await generateMergedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting influencer analysis:', error);
    res.status(500).json({ error: 'Failed to get influencer analysis' });
  }
});
```

## Implementation Strategy

The implementation should follow these steps:

1. **Setup Firebase/Firestore**: Initialize Firebase and Firestore in the application
2. **Create Helper Functions**: Implement the helper functions for storing and retrieving data from Firestore
3. **Update Main Flow**: Modify the main analysis flow to use Firestore instead of JSON files
4. **Update API Endpoints**: Update the API endpoints to use the new Firestore-based functions
5. **Backward Compatibility**: Maintain backward compatibility by continuing to store JSON files in Cloud Storage
6. **Testing**: Test each component individually and then the entire flow end-to-end

## Considerations

1. **Data Migration**: Existing JSON data should be migrated to Firestore to ensure continuity
2. **Error Handling**: Robust error handling should be implemented to handle Firestore-specific errors
3. **Security Rules**: Firestore security rules should be implemented to protect the data
4. **Performance**: Firestore queries should be optimized for performance
5. **Cost**: Firestore usage should be monitored to control costs

## Next Steps

After implementing the changes outlined in this plan, the following steps should be taken:

1. **Testing**: Thoroughly test the new implementation to ensure it works as expected
2. **Documentation**: Update documentation to reflect the new Firestore-based implementation
3. **Monitoring**: Set up monitoring to track Firestore usage and performance
4. **Optimization**: Optimize Firestore queries and indexes for better performance
5. **Feature Enhancements**: Consider adding new features that leverage Firestore's capabilities

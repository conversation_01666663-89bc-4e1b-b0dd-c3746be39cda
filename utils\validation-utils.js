// validation-utils.js
// Validation utilities

/**
 * Validate campaign data
 * @param {Object} data - The campaign data to validate
 * @returns {Object} - The validation result
 */
function validateCampaignData(data) {
  const errors = [];
  
  // Check required fields
  if (!data.name) {
    errors.push('Campaign name is required');
  }
  
  if (!data.product_description) {
    errors.push('Product description is required');
  }
  
  // Validate follower count range
  if (data.min_follower_count && data.max_follower_count) {
    if (parseInt(data.min_follower_count) > parseInt(data.max_follower_count)) {
      errors.push('Minimum follower count cannot be greater than maximum follower count');
    }
  }
  
  // Validate engagement rate
  if (data.min_engagement_rate) {
    const rate = parseFloat(data.min_engagement_rate);
    if (isNaN(rate) || rate < 0 || rate > 100) {
      errors.push('Minimum engagement rate must be a number between 0 and 100');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate influencer data
 * @param {Object} data - The influencer data to validate
 * @returns {Object} - The validation result
 */
function validateInfluencerData(data) {
  const errors = [];
  
  // Check required fields
  if (!data.username) {
    errors.push('Username is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export {
  validateCampaignData,
  validateInfluencerData
};

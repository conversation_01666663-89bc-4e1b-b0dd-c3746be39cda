#!/usr/bin/env node
/**
 * test-discovery-connector.js
 * Test script for the Influencers Club Discovery connector
 *
 * This script tests the Influencers Club Discovery connector by:
 * - Taking an ai_search parameter from the command line
 * - Calling the discovery API with that parameter
 * - Saving the results to the test_outputs directory
 *
 * Usage:
 * node test-discovery-connector.js --ai_search="Your search description here"
 */

// Core imports
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { performance } from 'perf_hooks';

// Firebase imports
import { initializeFirebase, getFirestoreDb } from './config/firebase-config.js';

// Connector import
import DiscoveryConnectorWrapper from './test-discovery-connector-wrapper.js';

// Utility imports
import { DEFAULT_CLIENT_ID } from './config/constants.js';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Constants
const TEST_OUTPUTS_DIR = path.join(__dirname, 'test_outputs');

/**
 * Simple logger utility
 */
const logger = {
  info: (message) => console.log(`[INFO] ${message}`),
  success: (message) => console.log(`[SUCCESS] ${message}`),
  error: (message, error) => {
    console.error(`[ERROR] ${message}`);
    if (error) {
      console.error(error.message);

      // Enhanced error logging
      if (error.response && error.response.data) {
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }

      if (error.stack) console.error(error.stack);
    }
  },
  debug: (message, data) => {
    console.log(`[DEBUG] ${message}`);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  },
  section: (title) => {
    console.log('\n' + '='.repeat(80));
    console.log(`${title}`);
    console.log('='.repeat(80));
  }
};

/**
 * Initialize the test environment
 */
async function initializeTestEnvironment() {
  try {
    logger.section('INITIALIZING TEST ENVIRONMENT');

    // Create test_outputs directory if it doesn't exist
    if (!fs.existsSync(TEST_OUTPUTS_DIR)) {
      logger.info(`Creating test_outputs directory at ${TEST_OUTPUTS_DIR}`);
      fs.mkdirSync(TEST_OUTPUTS_DIR, { recursive: true });
    }

    // Initialize Firebase using the centralized configuration
    try {
      logger.info('Initializing Firebase');
      initializeFirebase(true); // Pass true for verbose logging
      logger.success('Firebase initialized successfully');
    } catch (firebaseError) {
      logger.error('Failed to initialize Firebase', firebaseError);
      throw new Error(`Firebase initialization failed: ${firebaseError.message}`);
    }

    logger.success('Test environment initialized');
    return true;
  } catch (error) {
    logger.error('Failed to initialize test environment', error);
    throw new Error(`Test environment initialization failed: ${error.message}`);
  }
}

/**
 * Test the Influencers Club Discovery connector
 * @param {string} aiSearch - The AI search parameter
 * @param {Object} options - Additional options
 */
async function testDiscoveryConnector(aiSearch, options = {}) {
  try {
    // Initialize the test environment
    await initializeTestEnvironment();

    logger.section('TESTING INFLUENCERS CLUB DISCOVERY CONNECTOR');
    logger.info(`AI Search: "${aiSearch}"`);

    // Create the connector wrapper
    const connector = new DiscoveryConnectorWrapper();

    // Prepare parameters
    const params = {
      influencer_description: aiSearch,
      main_platform: options.platform || 'instagram',
      limit: options.limit || 4, // Default to 4 influencers to minimize costs
      page: options.page || 1,
      min_follower_count: options.min_followers || 10000,
      max_follower_count: options.max_followers || null
    };

    // Add optional parameters if provided
    if (options.min_likes) params.min_likes = options.min_likes;
    if (options.min_comments) params.min_comments = options.min_comments;
    if (options.min_views) params.min_views = options.min_views;
    if (options.location) params.location = options.location;
    if (options.gender) params.gender = options.gender;

    // Log the parameters with enhanced debugging
    logger.info('Discovery parameters:');
    logger.debug('Parameters object:', params);

    // Start the timer
    const startTime = performance.now();

    // Call the discovery API
    logger.info('Calling Influencers Club Discovery API...');
    logger.info('This may take a few moments...');
    let results;

    try {
      results = await connector.discoverInfluencers(params);

      // End the timer
      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000; // Convert to seconds

      logger.success(`Discovery API call completed in ${duration.toFixed(2)} seconds`);

      // Log the raw response for debugging
      logger.debug('Raw API response:', results);

      // Check if we have results - the API returns 'accounts' not 'similar_accounts'
      if (!results || !results.accounts || !Array.isArray(results.accounts)) {
        logger.error('Invalid response structure from Influencers Club Discovery API', {
          responseKeys: results ? Object.keys(results) : 'null response'
        });
        throw new Error('Invalid response from Influencers Club Discovery API');
      }

      // Map the accounts to the expected format for backward compatibility
      results.similar_accounts = results.accounts.map(account => {
        return {
          username: account.profile.username,
          full_name: account.profile.full_name,
          platform: params.main_platform || 'instagram',
          follower_count: account.profile.followers,
          engagement_percent: account.profile.engagement_percent,
          profile_picture: account.profile.picture, // Store the picture URL
          user_id: account.user_id,
          // Add any additional fields we want to store
          niches: [], // Will be populated later if available
          ai_search_used: params.influencer_description,
          discovery_params: {
            platform: params.main_platform || 'instagram',
            min_follower_count: params.min_follower_count || 10000,
            max_follower_count: params.max_follower_count || null,
            // Add other params that were used
            limit: params.limit || 4,
            page: params.page || 1
          }
        };
      });

      logger.info(`Found ${results.accounts.length} accounts`);

      // Save the results to a file
      const timestamp = Date.now();
      const outputPath = path.join(TEST_OUTPUTS_DIR, `discovery_results_${timestamp}.json`);
      fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
      logger.info(`Results saved to ${outputPath}`);

      // Store in Firestore if requested
      if (options.store_in_firestore) {
        logger.info('Storing results in Firestore...');
        const clientId = options.client_id || DEFAULT_CLIENT_ID;
        const campaignId = options.campaign_id || `test_campaign_${timestamp}`;

        const docId = await connector.storeDiscoveryResults(
          clientId,
          campaignId,
          results,
          aiSearch
        );

        logger.success(`Results stored in Firestore with document ID: ${docId}`);
      }

      // Display a summary of the results
      displayResultsSummary(results);
    } catch (error) {
      logger.error('API call failed', error);
      throw error;
    }

    return results;
  } catch (error) {
    logger.error('Discovery connector test failed', error);
    throw error;
  }
}

/**
 * Display a summary of the discovery results
 * @param {Object} results - The discovery results
 */
function displayResultsSummary(results) {
  logger.section('DISCOVERY RESULTS SUMMARY');

  console.log(`Total accounts found: ${results.accounts.length}`);
  console.log(`Credits left: ${results.credits_left}`);
  console.log('\nTop accounts:');

  // Display all accounts (since we're limiting to 4 anyway)
  results.similar_accounts.forEach((account, index) => {
    console.log(`\n${index + 1}. ${account.username} (${account.full_name || 'No name'})`);
    console.log(`   Platform: ${account.platform}`);
    console.log(`   Followers: ${account.follower_count.toLocaleString()}`);
    console.log(`   Engagement: ${account.engagement_percent.toFixed(2)}%`);
    console.log(`   Profile Picture: ${account.profile_picture || 'Not available'}`);
    if (account.niches && account.niches.length > 0) {
      console.log(`   Niches: ${account.niches.join(', ')}`);
    }
  });

  console.log('\n' + '='.repeat(80));
}

/**
 * Parse command line arguments
 * @returns {Object} - The parsed arguments
 */
function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  const options = {
    ai_search: '',
    platform: 'instagram',
    limit: 10,  // Default to 10 for production, can be overridden
    store_in_firestore: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg.startsWith('--ai_search=')) {
      options.ai_search = arg.substring('--ai_search='.length);
    } else if (arg.startsWith('--platform=')) {
      options.platform = arg.substring('--platform='.length);
    } else if (arg.startsWith('--limit=')) {
      options.limit = parseInt(arg.substring('--limit='.length), 10);
    } else if (arg.startsWith('--min_followers=')) {
      options.min_followers = parseInt(arg.substring('--min_followers='.length), 10);
    } else if (arg.startsWith('--max_followers=')) {
      options.max_followers = parseInt(arg.substring('--max_followers='.length), 10);
    } else if (arg.startsWith('--store_in_firestore=')) {
      options.store_in_firestore = arg.substring('--store_in_firestore='.length) === 'true';
    } else if (arg.startsWith('--client_id=')) {
      options.client_id = arg.substring('--client_id='.length);
    } else if (arg.startsWith('--campaign_id=')) {
      options.campaign_id = arg.substring('--campaign_id='.length);
    } else if (arg === '--help' || arg === '-h') {
      displayHelp();
      process.exit(0);
    }
  }

  return options;
}

/**
 * Display help information
 */
function displayHelp() {
  console.log(`
Usage: node test-discovery-connector.js [options]

Options:
  --ai_search=<text>           The AI search description (required)
  --platform=<platform>        Platform to search (default: instagram)
  --limit=<number>             Number of results to return (default: 10, can be overridden)
  --min_followers=<number>     Minimum follower count (default: 10000)
  --max_followers=<number>     Maximum follower count (optional)
  --store_in_firestore=<bool>  Whether to store results in Firestore (default: false)
  --client_id=<id>             Client ID for Firestore storage (default: 'default_client')
  --campaign_id=<id>           Campaign ID for Firestore storage (default: generated)
  --help, -h                   Display this help message

Example:
  node test-discovery-connector.js --ai_search="Fitness influencers who focus on strength training"
  `);
}

// Run the test if this file is executed directly
if (process.argv[1].endsWith('test-discovery-connector.js')) {
  const options = parseCommandLineArgs();

  if (!options.ai_search) {
    console.log('Error: --ai_search parameter is required');
    displayHelp();
    process.exit(1);
  }

  testDiscoveryConnector(options.ai_search, options)
    .then(() => {
      process.exit(0);
    })
    .catch(error => {
      console.error(`Test failed: ${error.message}`);
      process.exit(1);
    });
}

// Export the test function for external use
export default testDiscoveryConnector;

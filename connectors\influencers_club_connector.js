// influencers_club_connector.js
// Connector class for Influencers Club API integration

import axios from 'axios';
import { Storage } from '@google-cloud/storage';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';

/**
 * Connector class for Influencers Club API
 * Handles data fetching, processing, and storage
 */
class InfluencersClubConnector {
  /**
   * Create a new InfluencersClubConnector
   * @param {string} apiKey - The Influencers Club API key
   * @param {string} bucketName - The Cloud Storage bucket name
   */
  constructor(apiKey, bucketName) {
    this.apiKey = apiKey;
    this.bucketName = bucketName;
    this.storage = new Storage();
    this.bucket = this.storage.bucket(bucketName);
    this.db = getFirestore();
  }

  /**
   * Fetch influencer data from Influencers Club API
   * @param {string} username - The influencer's username
   * @param {string} platform - The platform (default: "instagram")
   * @param {string} emailRequired - Email requirement (default: "must_have")
   * @param {boolean} postDataRequired - Whether post data is required (default: true)
   * @param {string} filterKey - The type of filter to use (default: "username")
   * @returns {Object} - The raw API response
   */
  async fetchInfluencerData(username, platform = "instagram", emailRequired = "must_have", postDataRequired = true, filterKey = "username") {
    const url = "https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/";

    // Validate the platform is supported
    const supportedPlatforms = [
      "instagram", "youtube", "tiktok", "onlyfans",
      "twitter", "twitch", "discord", "facebook",
      "snapchat", "pinterest"
    ];

    if (!supportedPlatforms.includes(platform)) {
      console.warn(`Platform ${platform} may not be supported. Supported platforms: ${supportedPlatforms.join(', ')}`);
    }

    // Validate the filter key
    const validFilterKeys = ["url", "email", "username"];
    if (!validFilterKeys.includes(filterKey)) {
      throw new Error(`Invalid filter_key: ${filterKey}. Must be one of: ${validFilterKeys.join(', ')}`);
    }

    // Validate email_required parameter
    const validEmailOptions = ["not_needed", "preferred", "must_have", true, false];
    if (!validEmailOptions.includes(emailRequired)) {
      console.warn(`Invalid email_required value: ${emailRequired}. Using default "must_have"`);
      emailRequired = "must_have";
    }

    const payload = {
      filter_value: username,
      filter_key: filterKey,
      platform,
      email_required: emailRequired,
      post_data_required: postDataRequired
    };

    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };

    try {
      console.log(`Fetching data for ${username} on ${platform} platform...`);
      const response = await axios.post(url, payload, { headers });
      return response.data;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error(`Error ${error.response.status}: ${error.response.statusText}`);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error setting up request:', error.message);
      }
      throw error;
    }
  }

  /**
   * Process raw influencer data into standardized format
   * @param {Object} rawData - The raw data from Influencers Club API
   * @returns {Object} - The processed data in standardized format
   */
  async processRawData(rawData) {
    // Extract platform data (Instagram, TikTok, YouTube, Twitter, etc.)
    const processed = {
      profileInfo: {
        username: '',
        fullName: '',
        profileImageUrl: '',
        category: '',
        bio: '',
        email: rawData.email || '',
        location: rawData.location || '',
        speakingLanguage: rawData.speaking_language || '',
        isBusinessAccount: rawData.is_business || false,
        isCreator: rawData.is_creator || false,
        hasBrandDeals: rawData.has_brand_deals || false,
        hasLinkInBio: rawData.has_link_in_bio || false
      },
      influencerStats: {
        platforms: [],
        engagementRate: {
          value: '',
          description: ''
        },
        contentTypes: [],
        postingFrequency: 0,
        followerGrowth: {
          threeMonths: 0,
          sixMonths: 0,
          nineMonths: 0,
          twelveMonths: 0
        }
      },
      audienceInsights: {
        demographics: {
          age: [],
          gender: {}
        },
        locations: [],
        interests: []
      },
      recentPosts: [],
      platformPresence: []
    };

    // Add platform presence information
    if (rawData.creator_has && Array.isArray(rawData.creator_has)) {
      processed.platformPresence = rawData.creator_has.map(platform => ({
        platform: platform.platform || '',
        url: platform.url || ''
      }));
    }

    // Process Instagram data if available
    if (rawData.instagram) {
      const instagram = rawData.instagram;

      // Set profile info
      processed.profileInfo.username = instagram.username || '';
      processed.profileInfo.fullName = instagram.full_name || '';

      // Download and cache profile image if available
      if (instagram.profile_pic_url) {
        const imageUrl = await this.cacheImage(instagram.profile_pic_url, `${instagram.username}_profile.jpg`);
        processed.profileInfo.profileImageUrl = imageUrl;
      }

      // Set category based on niches
      if (instagram.niches) {
        const primaryNiche = instagram.niches.primary || '';
        const secondaryNiches = instagram.niches.secondary || [];
        processed.profileInfo.category = [primaryNiche, ...secondaryNiches].filter(Boolean).join(' - ');
      } else if (instagram.niche_class) {
        // Fallback to niche_class if niches is not available
        processed.profileInfo.category = instagram.niche_class.join(' - ');
      }

      processed.profileInfo.bio = instagram.biography || '';

      // Set influencer stats
      processed.influencerStats.platforms.push({
        name: 'Instagram',
        followers: this.formatNumber(instagram.follower_count),
        engagement: instagram.engagement_percent ? `${instagram.engagement_percent}%` : 'N/A'
      });

      processed.influencerStats.engagementRate = {
        value: instagram.engagement_percent ? `${instagram.engagement_percent}%` : 'N/A',
        description: 'Instagram engagement rate.'
      };

      // Set posting frequency
      processed.influencerStats.postingFrequency = instagram.posting_frequency_recent_months || 0;

      // Set follower growth
      if (instagram.follower_growth) {
        processed.influencerStats.followerGrowth = {
          threeMonths: instagram.follower_growth.three_months_ago || 0,
          sixMonths: instagram.follower_growth.six_months_ago || 0,
          nineMonths: instagram.follower_growth.nine_months_ago || 0,
          twelveMonths: instagram.follower_growth.twelve_months_ago || 0
        };
      } else if (instagram.creator_follower_growth) {
        // Fallback to creator_follower_growth if follower_growth is not available
        processed.influencerStats.followerGrowth = {
          threeMonths: instagram.creator_follower_growth.three_months_ago || 0,
          sixMonths: instagram.creator_follower_growth.six_months_ago || 0,
          nineMonths: instagram.creator_follower_growth.nine_months_ago || 0,
          twelveMonths: instagram.creator_follower_growth.twelve_months_ago || 0
        };
      }

      // Determine content types
      const contentTypes = new Set();
      if (instagram.posts && instagram.posts.length > 0) {
        instagram.posts.forEach(post => {
          if (post.is_video) {
            contentTypes.add('video');
          } else {
            contentTypes.add('image');
          }
        });
      }
      processed.influencerStats.contentTypes = Array.from(contentTypes);

      // Process audience insights
      if (instagram.audience_demographics) {
        // Age demographics
        if (instagram.audience_demographics.age_gender) {
          const ageData = [];
          for (const [ageRange, percentage] of Object.entries(instagram.audience_demographics.age_gender.age)) {
            ageData.push({
              range: ageRange,
              percentage: percentage
            });
          }
          processed.audienceInsights.demographics.age = ageData;

          // Gender demographics
          processed.audienceInsights.demographics.gender = instagram.audience_demographics.age_gender.gender || {};
        }

        // Locations
        if (instagram.audience_demographics.top_countries) {
          const locationData = [];
          for (const [country, percentage] of Object.entries(instagram.audience_demographics.top_countries)) {
            locationData.push({
              location: country,
              percentage: percentage
            });
          }
          processed.audienceInsights.locations = locationData;
        }

        // Interests
        if (instagram.audience_demographics.interests) {
          processed.audienceInsights.interests = instagram.audience_demographics.interests.map(interest => ({
            category: interest.category,
            percentage: interest.percentage
          }));
        }
      }

      // Process recent posts
      if (instagram.posts && instagram.posts.length > 0) {
        processed.recentPosts = await Promise.all(instagram.posts.slice(0, 10).map(async post => {
          let imageUrl = '';
          if (post.display_url) {
            imageUrl = await this.cacheImage(post.display_url, `${instagram.username}_post_${post.id}.jpg`);
          }

          return {
            id: post.id,
            imageUrl: imageUrl,
            caption: post.caption || '',
            likes: this.formatNumber(post.like_count),
            comments: this.formatNumber(post.comment_count),
            views: post.view_count ? this.formatNumber(post.view_count) : '',
            date: post.taken_at ? new Date(post.taken_at * 1000).toISOString().split('T')[0] : '',
            location: post.location ? {
              name: post.location.name || '',
              latitude: post.location.lat,
              longitude: post.location.lng
            } : null,
            taggedUsers: post.tagged_users || []
          };
        }));
      } else if (instagram.latest_post) {
        // Fallback to latest_post if posts array is not available
        const post = instagram.latest_post;
        let imageUrl = '';
        if (post.media && post.media.length > 0 && post.media[0].url) {
          imageUrl = await this.cacheImage(post.media[0].url, `${instagram.username}_latest_post.jpg`);
        }

        processed.recentPosts.push({
          id: post.post_id || '',
          imageUrl: imageUrl,
          caption: post.caption || '',
          likes: post.engagement && post.engagement.likes ? this.formatNumber(post.engagement.likes) : '0',
          comments: post.engagement && post.engagement.comments ? this.formatNumber(post.engagement.comments) : '0',
          views: post.engagement && post.engagement.views ? this.formatNumber(post.engagement.views) : '',
          date: post.created_at || '',
          location: post.location ? {
            name: post.location.name || '',
            latitude: post.location.latitude,
            longitude: post.location.longitude
          } : null,
          taggedUsers: post.tagged_users || []
        });
      }

      // Add hashtags
      if (instagram.hashtags && Array.isArray(instagram.hashtags)) {
        processed.hashtags = instagram.hashtags;
      }
    }

    // Process TikTok data if available
    if (rawData.tiktok) {
      const tiktok = rawData.tiktok;

      // Add TikTok to platforms
      processed.influencerStats.platforms.push({
        name: 'TikTok',
        followers: this.formatNumber(tiktok.follower_count),
        engagement: tiktok.engagement_percent ? `${tiktok.engagement_percent}%` : 'N/A'
      });

      // Add video content type if not already added
      if (!processed.influencerStats.contentTypes.includes('video')) {
        processed.influencerStats.contentTypes.push('video');
      }

      // Add TikTok-specific data
      if (!processed.tiktok) {
        processed.tiktok = {
          username: tiktok.username || '',
          biography: tiktok.biography || '',
          category: tiktok.category || '',
          averageLikes: tiktok.average_likes || 0,
          hasTikTokShop: tiktok.has_tiktok_shop || false
        };
      }

      // Add follower growth if available
      if (tiktok.follower_growth) {
        processed.tiktok.followerGrowth = {
          threeMonths: tiktok.follower_growth.three_months_ago || 0,
          sixMonths: tiktok.follower_growth.six_months_ago || 0,
          nineMonths: tiktok.follower_growth.nine_months_ago || 0,
          twelveMonths: tiktok.follower_growth.twelve_months_ago || 0
        };
      }

      // Add niches if available
      if (tiktok.niches) {
        processed.tiktok.niches = {
          primary: tiktok.niches.primary || '',
          secondary: tiktok.niches.secondary || []
        };
      }

      // Add hashtags if available
      if (tiktok.hashtags && Array.isArray(tiktok.hashtags)) {
        processed.tiktok.hashtags = tiktok.hashtags;
      }

      // Add latest post if available
      if (tiktok.latest_post) {
        const post = tiktok.latest_post;
        let mediaUrl = '';
        if (post.media && post.media.url) {
          mediaUrl = await this.cacheImage(post.media.url, `${tiktok.username}_tiktok_latest.jpg`);
        }

        processed.tiktok.latestPost = {
          id: post.post_id || '',
          caption: post.caption || '',
          url: post.post_url || '',
          mediaUrl: mediaUrl,
          mediaType: post.media ? post.media.type : '',
          mediaDuration: post.media ? post.media.duration : null,
          likes: post.engagement && post.engagement.likes ? this.formatNumber(post.engagement.likes) : '0',
          comments: post.engagement && post.engagement.comments ? this.formatNumber(post.engagement.comments) : '0',
          views: post.engagement && post.engagement.views ? this.formatNumber(post.engagement.views) : '0',
          shares: post.engagement && post.engagement.shares ? this.formatNumber(post.engagement.shares) : '0',
          date: post.created_at || '',
          sound: post.sound ? {
            name: post.sound.name || '',
            url: post.sound.url || ''
          } : null
        };
      }
    }

    // Process YouTube data if available
    if (rawData.youtube) {
      const youtube = rawData.youtube;

      // Add YouTube to platforms
      processed.influencerStats.platforms.push({
        name: 'YouTube',
        followers: this.formatNumber(youtube.subscriber_count),
        engagement: youtube.engagement_percent ? `${youtube.engagement_percent}%` : 'N/A'
      });

      // Add video content type if not already added
      if (!processed.influencerStats.contentTypes.includes('video')) {
        processed.influencerStats.contentTypes.push('video');
      }

      // Add YouTube-specific data
      if (!processed.youtube) {
        processed.youtube = {
          title: youtube.title || '',
          description: youtube.description || '',
          subscriberCount: youtube.subscriber_count || 0,
          videoCount: youtube.video_count || 0,
          averageViews: youtube.average_views || 0,
          isMonetized: youtube.is_monetized || false,
          hasCommunityPosts: youtube.has_community_posts || false
        };
      }

      // Add follower growth if available
      if (youtube.follower_growth) {
        processed.youtube.followerGrowth = {
          threeMonths: youtube.follower_growth.three_months_ago || 0,
          sixMonths: youtube.follower_growth.six_months_ago || 0,
          nineMonths: youtube.follower_growth.nine_months_ago || 0,
          twelveMonths: youtube.follower_growth.twelve_months_ago || 0
        };
      }

      // Add niches if available
      if (youtube.niches) {
        processed.youtube.niches = {
          primary: youtube.niches.primary || '',
          secondary: youtube.niches.secondary || []
        };
      }

      // Add hashtags if available
      if (youtube.hashtags && Array.isArray(youtube.hashtags)) {
        processed.youtube.hashtags = youtube.hashtags;
      }

      // Add latest video if available
      if (youtube.latest_video) {
        const video = youtube.latest_video;
        processed.youtube.latestVideo = {
          id: video.video_id || '',
          title: video.title || '',
          publishedAt: video.published_at || '',
          views: video.views ? this.formatNumber(video.views) : '0',
          likes: video.likes ? this.formatNumber(video.likes) : '0',
          comments: video.comments ? this.formatNumber(video.comments) : '0',
          duration: video.duration || '',
          language: video.language || '',
          topicCategories: video.topic_categories || []
        };
      }
    }

    // Process Twitter data if available
    if (rawData.twitter) {
      const twitter = rawData.twitter;

      // Add Twitter to platforms
      processed.influencerStats.platforms.push({
        name: 'Twitter',
        followers: this.formatNumber(twitter.follower_count),
        engagement: twitter.engagement_percent ? `${twitter.engagement_percent}%` : 'N/A'
      });

      // Add Twitter-specific data
      if (!processed.twitter) {
        processed.twitter = {
          username: twitter.username || '',
          description: twitter.description || '',
          followerCount: twitter.follower_count || 0,
          tweetCount: twitter.tweet_count || 0
        };
      }

      // Add follower growth if available
      if (twitter.follower_growth) {
        processed.twitter.followerGrowth = {
          threeMonths: twitter.follower_growth.three_months_ago || 0,
          sixMonths: twitter.follower_growth.six_months_ago || 0,
          nineMonths: twitter.follower_growth.nine_months_ago || 0,
          twelveMonths: twitter.follower_growth.twelve_months_ago || 0
        };
      }
    }

    // Process OnlyFans data if available
    if (rawData.onlyfans) {
      const onlyfans = rawData.onlyfans;

      // Add OnlyFans to platforms
      processed.influencerStats.platforms.push({
        name: 'OnlyFans',
        followers: this.formatNumber(onlyfans.follower_count || 0),
        engagement: onlyfans.engagement_percent ? `${onlyfans.engagement_percent}%` : 'N/A'
      });

      // Add OnlyFans-specific data
      if (!processed.onlyfans) {
        processed.onlyfans = {
          username: onlyfans.username || '',
          biography: onlyfans.biography || '',
          subscriptionPrice: onlyfans.subscription_price || 0,
          postCount: onlyfans.post_count || 0,
          photoCount: onlyfans.photo_count || 0,
          videoCount: onlyfans.video_count || 0
        };
      }
    }

    // Process Twitch data if available
    if (rawData.twitch) {
      const twitch = rawData.twitch;

      // Add Twitch to platforms
      processed.influencerStats.platforms.push({
        name: 'Twitch',
        followers: this.formatNumber(twitch.follower_count || 0),
        engagement: twitch.engagement_percent ? `${twitch.engagement_percent}%` : 'N/A'
      });

      // Add Twitch-specific data
      if (!processed.twitch) {
        processed.twitch = {
          username: twitch.username || '',
          description: twitch.description || '',
          followerCount: twitch.follower_count || 0,
          isTwitchPartner: twitch.is_twitch_partner || false,
          totalHoursStreamed: twitch.total_hours_streamed || 0,
          streamedHoursLast30Days: twitch.streamed_hours_last_30_days || 0,
          maxViewsCount: twitch.maximum_views_count || 0,
          avgViewsLast30Days: twitch.avg_views_last_30_days || 0,
          streamsCountLast30Days: twitch.streams_count_last_30_days || 0,
          gamesPlayed: twitch.games_played || []
        };
      }
    }

    // Add brand mentions and partnerships
    processed.brandMentions = this.extractBrandMentions(rawData);
    processed.partnerships = this.extractPartnerships(rawData);

    return processed;
  }

  /**
   * Extract brand mentions from raw data
   * @param {Object} rawData - The raw data from Influencers Club API
   * @returns {Array} - Array of brand mentions
   */
  extractBrandMentions(rawData) {
    const brandMentions = [];

    // Extract from Instagram posts
    if (rawData.instagram && rawData.instagram.posts) {
      rawData.instagram.posts.forEach(post => {
        if (post.caption) {
          // Simple regex to find brand mentions (@ followed by word characters)
          const mentions = post.caption.match(/@(\w+)/g);
          if (mentions) {
            mentions.forEach(mention => {
              const brandName = mention.substring(1); // Remove @ symbol

              // Check if this brand is already in the list
              const existingMention = brandMentions.find(b => b.name === brandName);
              if (existingMention) {
                existingMention.count += 1;
              } else {
                brandMentions.push({
                  name: brandName,
                  count: 1,
                  platform: 'Instagram'
                });
              }
            });
          }

          // Look for hashtags that might indicate brand partnerships
          const hashtags = post.caption.match(/#(\w+)/g);
          if (hashtags) {
            const partnershipHashtags = ['ad', 'sponsored', 'partner', 'collab', 'collaboration', 'ambassador'];
            hashtags.forEach(hashtag => {
              const tag = hashtag.substring(1).toLowerCase(); // Remove # symbol
              if (partnershipHashtags.includes(tag)) {
                // This post might be a brand partnership
                // Extract other hashtags that might be brands
                hashtags.forEach(otherTag => {
                  const otherTagName = otherTag.substring(1);
                  if (!partnershipHashtags.includes(otherTagName.toLowerCase())) {
                    // Check if this brand is already in the list
                    const existingMention = brandMentions.find(b => b.name === otherTagName);
                    if (existingMention) {
                      existingMention.count += 1;
                      existingMention.isPotentialPartner = true;
                    } else {
                      brandMentions.push({
                        name: otherTagName,
                        count: 1,
                        platform: 'Instagram',
                        isPotentialPartner: true
                      });
                    }
                  }
                });
              }
            });
          }
        }
      });
    }

    // Process TikTok posts for brand mentions
    if (rawData.tiktok && rawData.tiktok.latest_post) {
      const post = rawData.tiktok.latest_post;
      if (post.caption) {
        // Simple regex to find brand mentions (@ followed by word characters)
        const mentions = post.caption.match(/@(\w+)/g);
        if (mentions) {
          mentions.forEach(mention => {
            const brandName = mention.substring(1); // Remove @ symbol

            // Check if this brand is already in the list
            const existingMention = brandMentions.find(b => b.name === brandName);
            if (existingMention) {
              existingMention.count += 1;
            } else {
              brandMentions.push({
                name: brandName,
                count: 1,
                platform: 'TikTok'
              });
            }
          });
        }

        // Look for hashtags that might indicate brand partnerships
        const hashtags = post.caption.match(/#(\w+)/g);
        if (hashtags) {
          const partnershipHashtags = ['ad', 'sponsored', 'partner', 'collab', 'collaboration', 'ambassador'];
          hashtags.forEach(hashtag => {
            const tag = hashtag.substring(1).toLowerCase(); // Remove # symbol
            if (partnershipHashtags.includes(tag)) {
              // This post might be a brand partnership
              // Extract other hashtags that might be brands
              hashtags.forEach(otherTag => {
                const otherTagName = otherTag.substring(1);
                if (!partnershipHashtags.includes(otherTagName.toLowerCase())) {
                  // Check if this brand is already in the list
                  const existingMention = brandMentions.find(b => b.name === otherTagName);
                  if (existingMention) {
                    existingMention.count += 1;
                    existingMention.isPotentialPartner = true;
                  } else {
                    brandMentions.push({
                      name: otherTagName,
                      count: 1,
                      platform: 'TikTok',
                      isPotentialPartner: true
                    });
                  }
                }
              });
            }
          });
        }
      }
    }

    // Process YouTube videos for brand mentions
    if (rawData.youtube && rawData.youtube.latest_video) {
      const video = rawData.youtube.latest_video;
      if (video.title) {
        // Look for brand names in the title
        // This is a simplified approach - in a real implementation, you might use NLP
        // to more accurately identify brand names
        const words = video.title.split(/\s+/);
        words.forEach(word => {
          // Check if the word starts with a capital letter (potential brand name)
          if (word.length > 1 && word[0] === word[0].toUpperCase() && word[1] === word[1].toLowerCase()) {
            // Remove any punctuation
            const cleanWord = word.replace(/[^\w\s]/gi, '');
            if (cleanWord.length > 1) {
              // Check if this brand is already in the list
              const existingMention = brandMentions.find(b => b.name === cleanWord);
              if (existingMention) {
                existingMention.count += 1;
              } else {
                brandMentions.push({
                  name: cleanWord,
                  count: 1,
                  platform: 'YouTube'
                });
              }
            }
          }
        });
      }
    }

    return brandMentions;
  }

  /**
   * Extract partnerships from raw data
   * @param {Object} rawData - The raw data from Influencers Club API
   * @returns {Array} - Array of partnerships
   */
  extractPartnerships(rawData) {
    const partnerships = [];

    // Look for explicit partnership indicators in posts
    if (rawData.instagram && rawData.instagram.posts) {
      const partnershipKeywords = ['partner', 'sponsored', 'ad ', '#ad', 'ambassador', 'collab', 'collaboration'];

      rawData.instagram.posts.forEach(post => {
        if (post.caption) {
          const caption = post.caption.toLowerCase();

          // Check if any partnership keywords are in the caption
          const hasPartnershipKeyword = partnershipKeywords.some(keyword => caption.includes(keyword));

          if (hasPartnershipKeyword) {
            // Extract potential brand names from caption
            // This is a simplified approach - in a real implementation, you might use NLP
            // to more accurately identify brand names

            // Look for @ mentions
            const mentions = post.caption.match(/@(\w+)/g);
            if (mentions) {
              mentions.forEach(mention => {
                const brandName = mention.substring(1); // Remove @ symbol

                // Skip if it's the influencer's own username
                if (rawData.instagram.username && brandName.toLowerCase() === rawData.instagram.username.toLowerCase()) {
                  return;
                }

                // Check if this partnership is already in the list
                const existingPartnership = partnerships.find(p => p.brand === brandName);
                if (!existingPartnership) {
                  partnerships.push({
                    brand: brandName,
                    evidence: 'Mentioned in sponsored content',
                    platform: 'Instagram',
                    date: post.taken_at ? new Date(post.taken_at * 1000).toISOString().split('T')[0] : 'Unknown'
                  });
                }
              });
            }
          }
        }
      });
    }

    // Look for partnerships in TikTok posts
    if (rawData.tiktok && rawData.tiktok.latest_post) {
      const post = rawData.tiktok.latest_post;
      if (post.caption) {
        const caption = post.caption.toLowerCase();

        // Check for partnership keywords
        const partnershipKeywords = ['partner', 'sponsored', 'ad ', '#ad', 'ambassador', 'collab', 'collaboration'];
        const hasPartnershipKeyword = partnershipKeywords.some(keyword => caption.includes(keyword));

        if (hasPartnershipKeyword) {
          // Extract potential brand names from caption
          const mentions = post.caption.match(/@(\w+)/g);
          if (mentions) {
            mentions.forEach(mention => {
              const brandName = mention.substring(1); // Remove @ symbol

              // Skip if it's the influencer's own username
              if (rawData.tiktok.username && brandName.toLowerCase() === rawData.tiktok.username.toLowerCase()) {
                return;
              }

              // Check if this partnership is already in the list
              const existingPartnership = partnerships.find(p => p.brand === brandName);
              if (!existingPartnership) {
                partnerships.push({
                  brand: brandName,
                  evidence: 'Mentioned in sponsored TikTok content',
                  platform: 'TikTok',
                  date: post.created_at || 'Unknown'
                });
              }
            });
          }
        }
      }
    }

    // Look for partnerships in YouTube videos
    if (rawData.youtube && rawData.youtube.latest_video) {
      const video = rawData.youtube.latest_video;
      if (video.title) {
        const title = video.title.toLowerCase();

        // Check for partnership keywords
        const partnershipKeywords = ['partner', 'sponsored', 'ad ', '#ad', 'ambassador', 'collab', 'collaboration', 'review', 'sponsored by'];
        const hasPartnershipKeyword = partnershipKeywords.some(keyword => title.includes(keyword));

        if (hasPartnershipKeyword) {
          // Extract potential brand names from title
          // This is a simplified approach - in a real implementation, you might use NLP
          // to more accurately identify brand names
          const words = video.title.split(/\s+/);
          words.forEach(word => {
            // Check if the word starts with a capital letter (potential brand name)
            if (word.length > 1 && word[0] === word[0].toUpperCase() && word[1] === word[1].toLowerCase()) {
              // Remove any punctuation
              const cleanWord = word.replace(/[^\w\s]/gi, '');
              if (cleanWord.length > 1) {
                // Check if this partnership is already in the list
                const existingPartnership = partnerships.find(p => p.brand === cleanWord);
                if (!existingPartnership) {
                  partnerships.push({
                    brand: cleanWord,
                    evidence: 'Mentioned in sponsored YouTube content',
                    platform: 'YouTube',
                    date: video.published_at || 'Unknown'
                  });
                }
              }
            }
          });
        }
      }
    }

    return partnerships;
  }

  /**
   * Cache an image in Cloud Storage and return the public URL
   * @param {string} imageUrl - The original image URL
   * @param {string} filename - The filename to use in storage
   * @returns {string} - The public URL of the cached image
   */
  async cacheImage(imageUrl, filename) {
    try {
      // Download the image
      const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      const buffer = Buffer.from(response.data, 'binary');

      // Upload to Cloud Storage
      const file = this.bucket.file(`palas-image-cache/${filename}`);
      await file.save(buffer, {
        metadata: {
          contentType: response.headers['content-type']
        }
      });

      // Make the file publicly accessible
      await file.makePublic();

      // Return the public URL
      return `https://storage.googleapis.com/${this.bucketName}/palas-image-cache/${filename}`;
    } catch (error) {
      console.error('Error caching image:', error);
      return imageUrl; // Return original URL if caching fails
    }
  }

  /**
   * Format a number with commas for thousands
   * @param {number} num - The number to format
   * @returns {string} - The formatted number
   */
  formatNumber(num) {
    if (num === undefined || num === null) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  /**
   * Fetch, process, and store influencer data
   * @param {string} filterValue - The value to search for (username, email, or URL)
   * @param {string} filterKey - The type of filter (username, email, or url)
   * @param {string} platform - The platform to fetch data from
   * @param {Object} options - Additional options
   * @returns {Object} - The processed data and influencer ID
   */
  async enrichInfluencer(filterValue, filterKey = "username", platform = "instagram", options = {}) {
    try {
      // Set default options
      const defaultOptions = {
        emailRequired: "must_have",
        postDataRequired: true,
        updateFirestore: true,
        forceRefresh: false // Add this option
      };

      const finalOptions = { ...defaultOptions, ...options };

      let influencerRef, influencerSnapshot, influencerId;

      if (finalOptions.updateFirestore) {
        influencerRef = this.db.collection('influencers').where('username', '==', filterValue).limit(1);
        influencerSnapshot = await influencerRef.get();

        if (influencerSnapshot.empty) {
          const newInfluencerRef = this.db.collection('influencers').doc();
          influencerId = newInfluencerRef.id;
        } else {
          influencerId = influencerSnapshot.docs[0].id;
          // Check if processed_data exists and force refresh is false
          if (!finalOptions.forceRefresh &&
              influencerSnapshot.docs[0].data().processed_data) {
            return {
              influencerId,
              processedData: influencerSnapshot.docs[0].data().processed_data
            };
          }
        }
      } else {
        influencerId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
      }

      // Fetch raw data from Influencers Club
      const rawData = await this.fetchInfluencerData(
        filterValue,
        platform,
        finalOptions.emailRequired,
        finalOptions.postDataRequired,
        filterKey
      );

      // Store raw data in Cloud Storage if updating Firestore
      const timestamp = Date.now();
      let rawStoragePath = '';

      if (finalOptions.updateFirestore) {
        rawStoragePath = `influencers/${influencerId}/raw_data/influencers_club_${timestamp}.json`;
        await this.bucket.file(rawStoragePath).save(JSON.stringify(rawData), {
          metadata: { contentType: 'application/json' }
        });
      }

      // Process the raw data
      const processedData = await this.processRawData(rawData);

      // Store processed data in Cloud Storage if updating Firestore
      let processedStoragePath = '';

      if (finalOptions.updateFirestore) {
        processedStoragePath = `influencers/${influencerId}/processed_data/influencers_club_${timestamp}.json`;
        await this.bucket.file(processedStoragePath).save(JSON.stringify(processedData), {
          metadata: { contentType: 'application/json' }
        });
      }

      // Update Firestore with the latest data if requested
      if (finalOptions.updateFirestore) {
        // Prepare the base influencer data
        const influencerData = {
          username: processedData.profileInfo.username || filterValue,
          full_name: processedData.profileInfo.fullName || '',
          email: rawData.email || '',
          location: rawData.location || '',
          speaking_language: rawData.speaking_language || '',
          has_brand_deals: rawData.has_brand_deals || false,
          has_link_in_bio: rawData.has_link_in_bio || false,
          is_business: rawData.is_business || false,
          is_creator: rawData.is_creator || false,
          platforms: {},
          creator_has: rawData.creator_has || [],
          last_updated: Timestamp.now()
        };

        // Add platform-specific data
        if (rawData.instagram) {
          influencerData.platforms.instagram = {
            username: rawData.instagram.username,
            follower_count: rawData.instagram.follower_count,
            engagement_percent: rawData.instagram.engagement_percent,
            biography: rawData.instagram.biography,
            profile_image_url: processedData.profileInfo.profileImageUrl,
            niches: rawData.instagram.niches || {
              primary: '',
              secondary: []
            },
            hashtags: rawData.instagram.hashtags || [],
            posting_frequency_recent_months: rawData.instagram.posting_frequency_recent_months,
            follower_growth: rawData.instagram.follower_growth || rawData.instagram.creator_follower_growth || {
              three_months_ago: 0,
              six_months_ago: 0,
              nine_months_ago: 0,
              twelve_months_ago: 0
            }
          };
        }

        if (rawData.tiktok) {
          influencerData.platforms.tiktok = {
            username: rawData.tiktok.username,
            follower_count: rawData.tiktok.follower_count,
            engagement_percent: rawData.tiktok.engagement_percent,
            biography: rawData.tiktok.biography,
            category: rawData.tiktok.category,
            average_likes: rawData.tiktok.average_likes,
            niches: rawData.tiktok.niches || {
              primary: '',
              secondary: []
            },
            hashtags: rawData.tiktok.hashtags || [],
            follower_growth: rawData.tiktok.follower_growth || {
              three_months_ago: 0,
              six_months_ago: 0,
              nine_months_ago: 0,
              twelve_months_ago: 0
            },
            has_tiktok_shop: rawData.tiktok.has_tiktok_shop || false
          };
        }

        if (rawData.youtube) {
          influencerData.platforms.youtube = {
            title: rawData.youtube.title,
            description: rawData.youtube.description,
            subscriber_count: rawData.youtube.subscriber_count,
            video_count: rawData.youtube.video_count,
            average_views: rawData.youtube.average_views,
            engagement_percent: rawData.youtube.engagement_percent,
            niches: rawData.youtube.niches || {
              primary: '',
              secondary: []
            },
            hashtags: rawData.youtube.hashtags || [],
            is_monetized: rawData.youtube.is_monetized || false,
            has_community_posts: rawData.youtube.has_community_posts || false,
            follower_growth: rawData.youtube.follower_growth || {
              three_months_ago: 0,
              six_months_ago: 0,
              nine_months_ago: 0,
              twelve_months_ago: 0
            }
          };
        }

        if (rawData.twitter) {
          influencerData.platforms.twitter = {
            username: rawData.twitter.username,
            follower_count: rawData.twitter.follower_count,
            engagement_percent: rawData.twitter.engagement_percent,
            description: rawData.twitter.description,
            tweet_count: rawData.twitter.tweet_count,
            follower_growth: rawData.twitter.follower_growth || {
              three_months_ago: 0,
              six_months_ago: 0,
              nine_months_ago: 0,
              twelve_months_ago: 0
            }
          };
        }

        if (rawData.onlyfans) {
          influencerData.platforms.onlyfans = {
            username: rawData.onlyfans.username,
            follower_count: rawData.onlyfans.follower_count,
            engagement_percent: rawData.onlyfans.engagement_percent,
            biography: rawData.onlyfans.biography,
            subscription_price: rawData.onlyfans.subscription_price,
            post_count: rawData.onlyfans.post_count,
            photo_count: rawData.onlyfans.photo_count,
            video_count: rawData.onlyfans.video_count
          };
        }

        if (rawData.twitch) {
          influencerData.platforms.twitch = {
            username: rawData.twitch.username,
            follower_count: rawData.twitch.follower_count,
            engagement_percent: rawData.twitch.engagement_percent,
            description: rawData.twitch.description,
            is_twitch_partner: rawData.twitch.is_twitch_partner,
            total_hours_streamed: rawData.twitch.total_hours_streamed,
            streamed_hours_last_30_days: rawData.twitch.streamed_hours_last_30_days,
            maximum_views_count: rawData.twitch.maximum_views_count,
            avg_views_last_30_days: rawData.twitch.avg_views_last_30_days,
            streams_count_last_30_days: rawData.twitch.streams_count_last_30_days,
            games_played: rawData.twitch.games_played
          };
        }

        // Add or update the influencer document
        if (influencerSnapshot.empty) {
          influencerData.created_at = Timestamp.now();
          await this.db.collection('influencers').doc(influencerId).set(influencerData);
        } else {
          await this.db.collection('influencers').doc(influencerId).update(influencerData);
        }

        // Add a record of this data pull
        await this.db.collection('influencers').doc(influencerId).collection('raw_data').add({
          source: 'influencers_club',
          storage_path: rawStoragePath,
          processed_storage_path: processedStoragePath,
          pulled_at: Timestamp.now(),
          processed_at: Timestamp.now()
        });
      }

      return {
        influencerId,
        processedData
      };
    } catch (error) {
      console.error('Error enriching influencer:', error);
      throw error;
    }
  }
}

export default InfluencersClubConnector;


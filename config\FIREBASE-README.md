# Firebase Configuration

This document explains how the Firebase configuration is set up for both the test script and the production application.

## Overview

The Firebase configuration is centralized in the `config/firebase-config.js` file. This file provides functions to initialize Firebase, get a Firestore instance, and get a Storage instance.

## Credentials

The application uses the Firebase service account credentials located at:
```
credentials/palas-influencer-intelligence-firebase-adminsdk-fbsvc-7f452c5b1f.json
```

## Usage

### Initializing Firebase

```javascript
import { initializeFirebase } from './config/firebase-config.js';

// Initialize Firebase
initializeFirebase();
```

### Getting a Firestore Instance

```javascript
import { getFirestoreDb } from './config/firebase-config.js';

// Get a Firestore instance
const db = getFirestoreDb();

// Use the Firestore instance
const docRef = db.collection('collection').doc('document');
```

### Getting a Storage Instance

```javascript
import { getStorageInstance } from './config/firebase-config.js';

// Get a Storage instance
const storage = getStorageInstance();

// Use the Storage instance
const bucket = storage.bucket();
```

## Fallback Mechanism

The configuration includes a fallback mechanism:

1. First, it tries to use the credentials file specified above.
2. If that fails, it checks for the `GOOGLE_APPLICATION_CREDENTIALS` environment variable.
3. If that also fails, it throws an error.

## Verbose Mode

You can enable verbose logging by passing `true` to the `initializeFirebase` function:

```javascript
initializeFirebase(true);
```

This will log information about the initialization process, which can be helpful for debugging.

## Test Script Integration

The test script (`test-analysis-flow.js`) uses this centralized configuration to ensure that it's using the same Firebase setup as the production application.

## Production Integration

The production application (`server.js`) also uses this centralized configuration, ensuring consistency between testing and production environments.

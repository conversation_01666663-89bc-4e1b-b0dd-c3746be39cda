#!/usr/bin/env node
/**
 * test-enrichment-endpoint.js
 * Test script for the Influencer Enrichment API endpoint
 *
 * This script tests the Influencer Enrichment API endpoint by:
 * - Taking an influencer username as input
 * - Sending it to the /api/influencers/enrich endpoint
 * - Displaying the results
 *
 * Usage:
 * node test-enrichment-endpoint.js --username=influencer_username --campaign_id=campaign_id
 */

// Core imports
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import { performance } from 'perf_hooks';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Constants
// Use the deployed Cloud Run URL
const API_BASE_URL = 'https://palas-influencer-intelligence-9815718377.us-central1.run.app/api';
const TEST_OUTPUTS_DIR = path.join(__dirname, '..', 'test_outputs');

// Ensure test_outputs directory exists
if (!fs.existsSync(TEST_OUTPUTS_DIR)) {
  fs.mkdirSync(TEST_OUTPUTS_DIR, { recursive: true });
}

// Enhanced logger utility
const logger = {
  section: (title) => {
    console.log('\n' + '='.repeat(80));
    console.log(`${title}`);
    console.log('='.repeat(80));
  },
  info: (message) => console.log(`ℹ️ ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message, error) => {
    console.error(`❌ ${message}`);
    if (error) {
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
      } else if (error.request) {
        console.error('No response received');
        console.error('Request details:', error.request._header);
      } else {
        console.error('Error message:', error.message);
      }
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }
  },
  debug: (message, data) => {
    if (process.env.DEBUG) {
      console.log(`🔍 ${message}`);
      if (data) {
        if (typeof data === 'object') {
          console.log(JSON.stringify(data, null, 2));
        } else {
          console.log(data);
        }
      }
    }
  },
  // Add a new method for data structure validation
  validate: (label, condition) => {
    if (condition) {
      console.log(`✅ ${label}: Valid`);
    } else {
      console.log(`❌ ${label}: Invalid`);
    }
    return condition;
  }
};

/**
 * Parse command line arguments
 * @returns {Object} - The parsed arguments
 */
function parseCommandLineArgs() {
  const args = {};
  process.argv.slice(2).forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      args[key] = value || true;
    }
  });
  return args;
}

/**
 * Display help message
 */
function displayHelp() {
  console.log(`
Usage: node test-enrichment-endpoint.js [options]

Options:
  --username=<username>   Instagram username of the influencer to enrich
  --campaign_id=<id>      ID of the campaign to associate with the influencer
  --client_id=<id>        Client ID (default: 'default_client')
  --platform=<platform>   Platform (default: 'instagram')
  --force_update=<bool>   Force update even if data exists (default: false)
  --debug=<bool>          Enable detailed debug logging (default: false)
  --full_analysis=<bool>  Test the full analysis endpoint (default: false)
  --help, -h              Display this help message

Example:
  node test-enrichment-endpoint.js --username=fitness_influencer --campaign_id=summer_campaign_2025
  `);
}

/**
 * Log detailed verification of the complete flow
 * @param {Object} data - The response data
 * @param {string} username - The influencer username
 * @param {string} campaignId - The campaign ID
 * @param {string} clientId - The client ID
 */
function logFlowVerification(data, username, campaignId, clientId) {
  // 1. Log basic influencer info
  console.log(`\nInfluencer: ${data.username || username}`);
  console.log(`Name: ${data.name || 'N/A'}`);
  console.log(`Followers: ${data.followers_count || data.followers || 'N/A'}`);
  console.log(`Engagement Rate: ${data.engagement_rate ? (data.engagement_rate * 100).toFixed(2) + '%' : 'N/A'}`);

  // 2. Verify and log enrichment data
  logger.section('ENRICHMENT DATA VERIFICATION');
  if (data.profileInfo || data.profile_info) {
    console.log('✅ Influencer enrichment data present');
    const profileData = data.profileInfo || data.profile_info;
    console.log(`Profile Image: ${profileData.profileImageUrl ? 'Cached' : 'Not cached'}`);
    console.log(`Recent Posts: ${profileData.recentPosts ? profileData.recentPosts.length : 0} posts found`);

    // Check if images are cached
    if (profileData.recentPosts && profileData.recentPosts.length > 0) {
      const cachedImages = profileData.recentPosts.filter(post => post.imageUrl && post.imageUrl.includes('storage.googleapis.com')).length;
      console.log(`Cached Images: ${cachedImages}/${profileData.recentPosts.length} posts have cached images`);
    }
  } else {
    console.log('❌ Influencer enrichment data not found');
  }

  // 3. Verify and log web analysis
  logger.section('WEB ANALYSIS VERIFICATION');
  if (data.webAnalysis || data.web_analysis) {
    console.log('✅ Web analysis data present');
    const webData = data.webAnalysis || data.web_analysis;
    console.log(`Sentiment Score: ${webData.sentimentScore || webData.sentiment_score || 'N/A'}`);
    console.log(`Risk Level: ${webData.riskLevel || webData.risk_level || 'N/A'}`);

    // Check for deep dive report elements
    const deepDive = webData.deep_dive_report || {};
    console.log(`Timeline Events: ${deepDive.timeline_events ? deepDive.timeline_events.length : 0} events found`);
    console.log(`Press Mentions: ${deepDive.press_mentions ? deepDive.press_mentions.length : 0} mentions found`);
    console.log(`Controversies: ${deepDive.controversies ? deepDive.controversies.length : 0} controversies found`);
  } else {
    console.log('❌ Web analysis data not found');
  }

  // 4. Verify and log aesthetic analysis
  logger.section('AESTHETIC ANALYSIS VERIFICATION');
  if (data.aestheticAnalysis || data.aesthetic_analysis) {
    console.log('✅ Aesthetic analysis data present');
    const aestheticData = data.aestheticAnalysis || data.aesthetic_analysis;
    console.log(`Brand Fit Score: ${aestheticData.brandFitScore || aestheticData.brand_fit_score || 'N/A'}`);

    // Check for content analysis elements
    const contentAnalysis = aestheticData.content_analysis || {};
    console.log(`Visual Fit: ${contentAnalysis.visual_fit || 'N/A'}`);
    console.log(`Image Analyses: ${contentAnalysis.image_analyses ? contentAnalysis.image_analyses.length : 0} images analyzed`);
    console.log(`Notable Strengths: ${contentAnalysis.notable_strengths ? contentAnalysis.notable_strengths.length : 0} strengths identified`);
  } else {
    console.log('❌ Aesthetic analysis data not found');
  }

  // 5. Verify and log ROI analysis
  logger.section('ROI ANALYSIS VERIFICATION');
  if (data.roiAnalysis || data.roi_analysis) {
    console.log('✅ ROI analysis data present');
    const roiData = data.roiAnalysis || data.roi_analysis;
    console.log(`Brand Fit Score: ${roiData.brandFitScore || roiData.brand_fit_score || 'N/A'}`);
    console.log(`Risk Level: ${roiData.riskLevel || roiData.risk_level || 'N/A'}`);

    // Check for ROI projection elements
    const roiProjection = roiData.roi_projection || roiData.roiProjection || {};
    console.log(`Expected Impressions: ${roiProjection.expected_impressions || roiProjection.expectedImpressions || 'N/A'}`);
    console.log(`Expected Engagement Rate: ${roiProjection.expected_engagement_rate || roiProjection.expectedEngagementRate || 'N/A'}`);
    console.log(`ROI Rating: ${roiProjection.roi_rating || roiProjection.roiRating || 'N/A'}`);
  } else {
    console.log('❌ ROI analysis data not found');
  }

  // 6. Verify and log partnership analysis
  logger.section('PARTNERSHIP ANALYSIS VERIFICATION');
  if (data.partnershipAnalysis || data.partnership_analysis) {
    console.log('✅ Partnership analysis data present');
    const partnershipData = data.partnershipAnalysis || data.partnership_analysis;
    console.log(`Partnership Type: ${partnershipData.partnership_status?.partnership_type || 'N/A'}`);
    console.log(`Contractual Readiness: ${partnershipData.contractual_readiness?.fluency_score || 'N/A'}`);
    console.log(`Recommendation: ${partnershipData.recommendation?.fit_status || 'N/A'}`);
  } else {
    console.log('❌ Partnership analysis data not found');
  }

  // 7. Verify and log merged analysis
  logger.section('MERGED ANALYSIS VERIFICATION');
  if (data.profileInfo && data.roiProjection) {
    console.log('✅ Merged analysis data present');
    console.log(`Profile Info: Present`);
    console.log(`ROI Projection: Present`);
    console.log(`Web Analysis: ${data.webAnalysis ? 'Present' : 'Missing'}`);
    console.log(`Brand Fit: ${data.brandFit ? 'Present' : 'Missing'}`);
  } else {
    console.log('❌ Merged analysis data structure not found');
  }
}

/**
 * Test the enrichment endpoint for an influencer
 * @param {string} username - The influencer username
 * @param {string} campaignId - The campaign ID
 * @param {Object} options - Additional options
 * @returns {Object} - The enrichment results
 */
async function testEnrichmentEndpoint(username, campaignId, options = {}) {
  try {
    logger.section('TESTING INFLUENCER ENRICHMENT ENDPOINT');
    logger.info(`Enriching influencer: ${username}`);
    logger.info(`Campaign ID: ${campaignId}`);
    logger.info(`Client ID: ${options.clientId || 'default_client'}`);

    // Prepare the request payload
    const payload = {
      username,
      campaignId,
      clientId: options.clientId || 'default_client',
      platform: options.platform || 'instagram',
      forceUpdate: options.forceUpdate || false
    };

    logger.debug('Request payload:', payload);

    // Start timing
    const startTime = performance.now();

    // Make the API request
    logger.info('Sending request to /api/influencers/enrich endpoint...');
    const response = await axios.post(`${API_BASE_URL}/influencers/enrich`, payload);

    // End timing
    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // Convert to seconds

    logger.success(`Enrichment API call completed in ${duration.toFixed(2)} seconds`);

    // Check the response
    if (!response.data) {
      throw new Error('Empty response from Enrichment API');
    }

    // Save the results to a file
    const timestamp = Date.now();
    const outputPath = path.join(TEST_OUTPUTS_DIR, `${username}_enrichment_results_${timestamp}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
    logger.info(`Results saved to ${outputPath}`);

    // Display summary of results
    logger.section('ENRICHMENT RESULTS SUMMARY');

    const influencerData = response.data;
    console.log(`Username: ${influencerData.username || username}`);
    console.log(`Name: ${influencerData.name || 'N/A'}`);
    console.log(`Followers: ${influencerData.followers_count || 'N/A'}`);
    console.log(`Engagement Rate: ${influencerData.engagement_rate ? (influencerData.engagement_rate * 100).toFixed(2) + '%' : 'N/A'}`);

    if (influencerData.analyses) {
      console.log('\nAnalyses:');
      Object.keys(influencerData.analyses).forEach(analysisType => {
        console.log(`  - ${analysisType}: ${influencerData.analyses[analysisType] ? 'Completed' : 'Not available'}`);
      });
    }

    return response.data;
  } catch (error) {
    logger.error('Enrichment endpoint test failed', error);
    throw error;
  }
}

/**
 * Test the full analysis endpoint for an influencer
 * @param {string} username - The influencer username
 * @param {string} campaignId - The campaign ID
 * @param {Object} options - Additional options
 * @returns {Object} - The analysis results
 */
async function testFullAnalysisEndpoint(username, campaignId, options = {}) {
  try {
    logger.section('TESTING FULL ANALYSIS ENDPOINT');
    logger.info(`Analyzing influencer: ${username}`);
    logger.info(`Campaign ID: ${campaignId}`);
    logger.info(`Client ID: ${options.clientId || 'default_client'}`);

    // Prepare the request payload - use the correct parameter names expected by the API
    const payload = {
      client_id: options.clientId || 'default_client',
      report_id: campaignId,
      selected_account: username,
      force_refresh: options.forceUpdate || false
    };

    logger.debug('Request payload:', payload);

    // Start timing
    const startTime = performance.now();

    // Make the API request
    logger.info('Sending request to /api/analyze endpoint...');
    const response = await axios.post(`${API_BASE_URL}/analyze`, payload);

    // End timing
    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // Convert to seconds

    logger.success(`Full analysis API call completed in ${duration.toFixed(2)} seconds`);

    // Check the response
    if (!response.data) {
      throw new Error('Empty response from Analysis API');
    }

    // Save the results to a file
    const timestamp = Date.now();
    const outputPath = path.join(TEST_OUTPUTS_DIR, `${username}_full_analysis_results_${timestamp}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
    logger.info(`Results saved to ${outputPath}`);

    // Display summary of results
    logger.section('FULL ANALYSIS RESULTS SUMMARY');

    const analysisData = response.data;
    console.log(`Username: ${analysisData.influencer_username || username}`);

    // Log detailed verification of the complete flow
    logFlowVerification(analysisData, username, campaignId, options.clientId || 'default_client');

    return response.data;
  } catch (error) {
    logger.error('Full analysis endpoint test failed', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    const options = parseCommandLineArgs();

    if (options.help || options.h) {
      displayHelp();
      return;
    }

    if (!options.username) {
      logger.error('No username specified');
      displayHelp();
      return;
    }

    if (!options.campaign_id) {
      logger.error('No campaign_id specified');
      displayHelp();
      return;
    }

    // Enable debug mode if requested
    if (options.debug === 'true') {
      process.env.DEBUG = 'true';
      logger.info('Debug mode enabled');
    }

    // Test the enrichment endpoint
    await testEnrichmentEndpoint(options.username, options.campaign_id, {
      clientId: options.client_id,
      platform: options.platform,
      forceUpdate: options.force_update === 'true'
    });

    // Optionally test the full analysis endpoint
    if (options.full_analysis === 'true') {
      logger.info('Testing full analysis endpoint as requested...');
      await testFullAnalysisEndpoint(options.username, options.campaign_id, {
        clientId: options.client_id,
        platform: options.platform,
        forceUpdate: options.force_update === 'true'
      });
    } else {
      logger.info('Skipping full analysis endpoint test. Use --full_analysis=true to test it.');
    }

    logger.success('Test completed successfully');
  } catch (error) {
    logger.error('Test failed', error);
    process.exit(1);
  }
}

// Run the script if it's called directly
if (process.argv[1].endsWith('test-enrichment-endpoint.js')) {
  main();
}

export {
  testEnrichmentEndpoint,
  testFullAnalysisEndpoint
};

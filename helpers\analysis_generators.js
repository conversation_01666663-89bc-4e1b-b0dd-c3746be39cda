// analysis_generators.js
// Helper functions for generating combined and merged analyses

import { getFirestore } from 'firebase-admin/firestore';
import { Storage } from '@google-cloud/storage';

/**
 * Deep merge two objects
 * @param {Object} target - The target object
 * @param {Object} source - The source object
 * @returns {Object} - The merged object
 */
function deepMerge(target, source) {
  const output = { ...target };
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  
  return output;
}

/**
 * Check if a value is an object
 * @param {*} item - The value to check
 * @returns {boolean} - Whether the value is an object
 */
function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

/**
 * Generate a combined analysis for an influencer in a specific campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The combined analysis
 */
async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  const db = getFirestore();
  
  try {
    // Get the campaign influencer document
    const campaignInfluencerRef = db.collection('clients')
      .doc(clientId)
      .collection('campaigns')
      .doc(campaignId)
      .collection('campaign_influencers')
      .doc(influencerId);
    
    const campaignInfluencerDoc = await campaignInfluencerRef.get();
    
    if (!campaignInfluencerDoc.exists) {
      throw new Error(`Campaign influencer not found: ${influencerId}`);
    }
    
    // Get the aesthetic analysis
    const aestheticAnalysisSnapshot = await campaignInfluencerRef
      .collection('aesthetic_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();
    
    // Get the ROI analysis
    const roiAnalysisSnapshot = await campaignInfluencerRef
      .collection('roi_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();
    
    // Get the web analysis from the global influencer document
    const influencerRef = db.collection('influencers').doc(influencerId);
    const webAnalysisSnapshot = await influencerRef
      .collection('web_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();
    
    // Get the global influencer data
    const influencerDoc = await influencerRef.get();
    
    if (!influencerDoc.exists) {
      throw new Error(`Influencer not found: ${influencerId}`);
    }
    
    // Initialize the combined analysis object
    const combinedAnalysis = {
      profileInfo: {
        metrics: {},
        aestheticAnalysis: {}
      },
      roiProjection: {},
      webAnalysis: {},
      brandFit: {}
    };
    
    // Add aesthetic analysis data if available
    if (!aestheticAnalysisSnapshot.empty) {
      const aestheticAnalysis = aestheticAnalysisSnapshot.docs[0].data();
      
      combinedAnalysis.profileInfo.metrics = {
        brandAesthetic: `${aestheticAnalysis.content_analysis?.visual_fit || 0}/100`,
        styleAnalysis: ((aestheticAnalysis.brand_fit_score || 0) / 10).toFixed(1),
        vibeAlignment: ((aestheticAnalysis.brand_fit_score || 0) / 10).toFixed(1),
        visualAlignment: ((aestheticAnalysis.content_analysis?.visual_fit || 0) / 10).toFixed(1)
      };
      
      combinedAnalysis.profileInfo.aestheticAnalysis = {
        keyObservations: aestheticAnalysis.content_analysis?.content_themes || [],
        verdict: "Cohesive, brand-aligned aesthetic",
        verdictDescription: aestheticAnalysis.content_analysis?.tone_fit || ""
      };
    }
    
    // Add ROI analysis data if available
    if (!roiAnalysisSnapshot.empty) {
      const roiAnalysis = roiAnalysisSnapshot.docs[0].data();
      
      combinedAnalysis.profileInfo.metrics.roiPotential = roiAnalysis.influencer_analysis?.roi_projection?.roi_rating || "Medium";
      combinedAnalysis.profileInfo.metrics.brandFitScore = ((roiAnalysis.brand_fit_score || 0) / 10).toFixed(1);
      
      combinedAnalysis.roiProjection = {
        expectedImpressions: roiAnalysis.influencer_analysis?.roi_projection?.expected_impressions?.toLocaleString() || "0",
        expectedEngagementRate: `${roiAnalysis.influencer_analysis?.roi_projection?.expected_engagement_rate || 0}%`,
        expectedEngagements: roiAnalysis.influencer_analysis?.roi_projection?.expected_engagements?.toLocaleString() || "0",
        expectedEngagementDescription: roiAnalysis.influencer_analysis?.roi_projection?.roi_rationale || "",
        roiRating: roiAnalysis.influencer_analysis?.roi_projection?.roi_rating || "Medium",
        roiPotentialScore: "5",
        riskAssessment: roiAnalysis.risk_level || "Medium",
        riskAssessmentDescription: roiAnalysis.risk_description || "",
        audienceQuality: "High",
        audienceQualityDescription: "Engaged audience with strong interest in relevant topics."
      };
      
      combinedAnalysis.brandFit = {
        score: roiAnalysis.brand_fit_score || 0,
        description: roiAnalysis.brand_fit_description || "",
        strengths: roiAnalysis.influencer_analysis?.roi_projection?.strengths || [],
        weaknesses: roiAnalysis.influencer_analysis?.roi_projection?.weaknesses || []
      };
    }
    
    // Add web analysis data if available
    if (!webAnalysisSnapshot.empty) {
      const webAnalysis = webAnalysisSnapshot.docs[0].data();
      
      combinedAnalysis.webAnalysis = {
        sentimentScore: webAnalysis.sentiment_score || 0,
        riskLevel: webAnalysis.risk_level || "Medium",
        timeline: webAnalysis.deep_dive_report?.timeline_events || [],
        brandMentions: webAnalysis.deep_dive_report?.brand_mentions || [],
        partnerships: webAnalysis.deep_dive_report?.partnerships || [],
        controversies: webAnalysis.deep_dive_report?.controversies || []
      };
    }
    
    return combinedAnalysis;
  } catch (error) {
    console.error('Error generating combined analysis:', error);
    throw error;
  }
}

/**
 * Get the processed influencer data from Cloud Storage
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The processed influencer data
 */
async function getProcessedInfluencerData(influencerId) {
  const db = getFirestore();
  const storage = new Storage();
  
  try {
    // Get the latest processed data reference
    const rawDataSnapshot = await db.collection('influencers')
      .doc(influencerId)
      .collection('raw_data')
      .orderBy('processed_at', 'desc')
      .limit(1)
      .get();
    
    if (rawDataSnapshot.empty) {
      throw new Error(`No processed data found for influencer: ${influencerId}`);
    }
    
    const rawDataDoc = rawDataSnapshot.docs[0].data();
    const processedStoragePath = rawDataDoc.processed_storage_path;
    
    // Get the processed data from Cloud Storage
    const bucket = storage.bucket(processedStoragePath.split('/')[0]);
    const file = bucket.file(processedStoragePath.split('/').slice(1).join('/'));
    
    const [contents] = await file.download();
    const processedData = JSON.parse(contents.toString('utf8'));
    
    return processedData;
  } catch (error) {
    console.error('Error getting processed influencer data:', error);
    throw error;
  }
}

/**
 * Generate a merged analysis by combining the processed influencer data
 * with the combined analysis
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The merged analysis
 */
async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  try {
    // Get the combined analysis
    const combinedAnalysis = await generateCombinedAnalysis(clientId, campaignId, influencerId);
    
    // Get the processed influencer data
    const processedData = await getProcessedInfluencerData(influencerId);
    
    // Merge the two objects
    return deepMerge(combinedAnalysis, processedData);
  } catch (error) {
    console.error('Error generating merged analysis:', error);
    throw error;
  }
}

export {
  generateCombinedAnalysis,
  generateMergedAnalysis,
  deepMerge
};

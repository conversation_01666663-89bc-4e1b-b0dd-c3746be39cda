# Deployment Guide for Google Cloud Run

This guide outlines the steps to deploy the Palas Influencer Intelligence platform to Google Cloud Run.

## Prerequisites

1. [Google Cloud SDK](https://cloud.google.com/sdk/docs/install) installed and configured
2. [Docker](https://docs.docker.com/get-docker/) installed
3. A Google Cloud project with billing enabled
4. Firebase project with Firestore enabled
5. Google Cloud Storage bucket created

## Setup Steps

### 1. Set Environment Variables

```bash
# Set your Google Cloud project ID
export PROJECT_ID="palas-influencer-intelligence"
export REGION="us-central1"
```

### 2. Enable Required APIs

```bash
# Enable required Google Cloud APIs
gcloud services enable cloudbuild.googleapis.com \
  run.googleapis.com \
  secretmanager.googleapis.com \
  artifactregistry.googleapis.com \
  storage.googleapis.com \
  --project=$PROJECT_ID
```

### 3. Set Up Secret Manager

Make the Firebase service account key, OpenAI API key, and Influencers Club API key available as secrets:

```bash
# Make the setup-secrets.sh script executable
chmod +x setup-secrets.sh

# Run the script to set up secrets
./setup-secrets.sh
```

### 4. Build and Deploy with Cloud Build

```bash
# Submit the build to Cloud Build
gcloud builds submit --config=cloudbuild.yaml --project=$PROJECT_ID
```

### 5. Verify Deployment

```bash
# Get the deployed service URL
gcloud run services describe palas-influencer-intelligence \
  --platform=managed \
  --region=$REGION \
  --project=$PROJECT_ID \
  --format="value(status.url)"
```

## Manual Deployment (Alternative)

If you prefer to deploy manually instead of using Cloud Build:

### 1. Build the Docker Image

```bash
# Build the Docker image
docker build -t gcr.io/$PROJECT_ID/palas-influencer-intelligence:latest .
```

### 2. Push the Image to Container Registry

```bash
# Configure Docker to use gcloud as a credential helper
gcloud auth configure-docker

# Push the image to Container Registry
docker push gcr.io/$PROJECT_ID/palas-influencer-intelligence:latest
```

### 3. Deploy to Cloud Run

```bash
# Deploy to Cloud Run
gcloud run deploy palas-influencer-intelligence \
  --image=gcr.io/$PROJECT_ID/palas-influencer-intelligence:latest \
  --platform=managed \
  --region=$REGION \
  --allow-unauthenticated \
  --memory=2Gi \
  --cpu=2 \
  --min-instances=1 \
  --max-instances=10 \
  --set-env-vars=NODE_ENV=production \
  --set-secrets=FIREBASE_SERVICE_ACCOUNT=firebase-service-account:latest,OPENAI_API_KEY=openai-api-key:latest,INFLUENCERS_API_KEY=influencers-api-key:latest \
  --set-env-vars=STORAGE_BUCKET=palas-run-cache \
  --project=$PROJECT_ID
```

## Continuous Deployment

For continuous deployment, you can connect your GitHub repository to Cloud Build:

1. Go to the Cloud Build Triggers page in the Google Cloud Console
2. Click "Create Trigger"
3. Connect your GitHub repository
4. Configure the trigger to use the cloudbuild.yaml file
5. Set the trigger to run on pushes to your main branch

## Monitoring and Logging

- View logs in the Cloud Run console or using the gcloud command:
  ```bash
  gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=palas-influencer-intelligence" --project=$PROJECT_ID
  ```

- Set up monitoring alerts in Cloud Monitoring

## Troubleshooting

- **Container fails to start**: Check the logs for errors
- **Firebase authentication issues**: Verify the service account has the correct permissions
- **API rate limiting**: Adjust the min/max instances to handle load appropriately
- **Memory issues**: Increase the memory allocation in the deployment configuration

import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp } from 'firebase-admin/app';
import { getStorage } from 'firebase-admin/storage';
import OpenAIConnector from '../connectors/openai-connector.js';
import InfluencersClubConnector from '../connectors/influencers-club-connector.js';
import { performWebAnalysis, performAestheticAnalysis, performROIAnalysis } from '../services/analysis-service.js';
import { generateCampaignBrief } from '../services/campaign-service.js';
import { getSeedInfluencers, discoverInfluencers } from '../services/discovery-service.js';
import { generateCombinedAnalysis } from '../helpers/analysis-generators.js';
import { DEFAULT_CLIENT_ID, DEFAULT_BUCKET_NAME } from '../config/constants.js';
import { extractJSON } from '../utils/string-utils.js';
import { writeJsonToBucket, getCached<PERSON>son } from '../helpers/storage-helpers.js';
import { v4 as uuidv4 } from 'uuid';

// Initialize Firebase
initializeApp();
const db = getFirestore();
const storage = getStorage();
const bucketName = DEFAULT_BUCKET_NAME;

/**
 * Test function to analyze an influencer
 * @param {string} username - The influencer's username
 * @param {string} platform - The platform (default: 'instagram')
 * @param {boolean} useCache - Whether to use cached data (default: true)
 * @param {boolean} skipSteps - Whether to skip steps that have already been completed (default: true)
 */
async function testInfluencerAnalysis(username, platform = 'instagram', useCache = true, skipSteps = true) {
  try {
    console.log('='.repeat(80));
    console.log(`🧪 STARTING ANALYSIS FOR INFLUENCER: ${username} on ${platform}`);
    console.log('='.repeat(80));
    
    // Generate a unique report ID for this analysis
    const reportId = `test_${Date.now()}_${uuidv4().substring(0, 8)}`;
    console.log(`📝 Report ID: ${reportId}`);
    
    // Step 1: Create a test campaign if it doesn't exist
    console.log('\n📋 STEP 1: Creating test campaign...');
    const campaignId = await createTestCampaign(reportId);
    console.log(`✅ Test campaign created with ID: ${campaignId}`);
    
    // Step 2: Fetch influencer data from Influencers Club
    console.log('\n🔍 STEP 2: Fetching influencer data from Influencers Club...');
    const influencerData = await fetchInfluencerData(username, platform, useCache);
    console.log(`✅ Influencer data fetched for ${username}`);
    
    // Step 3: Create influencer in Firestore
    console.log('\n📊 STEP 3: Creating influencer in Firestore...');
    const influencerId = await createInfluencer(username, influencerData);
    console.log(`✅ Influencer created with ID: ${influencerId}`);
    
    // Step 4: Associate influencer with campaign
    console.log('\n🔗 STEP 4: Associating influencer with campaign...');
    await associateInfluencerWithCampaign(campaignId, influencerId, username);
    console.log(`✅ Influencer associated with campaign`);
    
    // Step 5: Perform web analysis
    console.log('\n🌐 STEP 5: Performing web analysis...');
    let webAnalysisData;
    const webAnalysisPath = `/runs/${campaignId}_${username}_web_analysis.json`;
    
    if (useCache && skipSteps) {
      webAnalysisData = await getCachedJson(bucketName, webAnalysisPath);
    }
    
    if (!webAnalysisData) {
      console.log('   🔄 No cached web analysis found or cache disabled. Performing web analysis...');
      const influencerName = influencerData.instagram?.full_name || username;
      webAnalysisData = await performWebAnalysis(DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, username);
      console.log('   ✅ Web analysis completed');
    } else {
      console.log('   ✅ Using cached web analysis');
    }
    
    // Step 6: Perform aesthetic analysis
    console.log('\n🎨 STEP 6: Performing aesthetic analysis...');
    let aestheticAnalysisData;
    const aestheticAnalysisPath = `/runs/${campaignId}_${username}_aesthetic_analysis.json`;
    
    if (useCache && skipSteps) {
      aestheticAnalysisData = await getCachedJson(bucketName, aestheticAnalysisPath);
    }
    
    if (!aestheticAnalysisData) {
      console.log('   🔄 No cached aesthetic analysis found or cache disabled. Performing aesthetic analysis...');
      const influencerName = influencerData.instagram?.full_name || username;
      
      // Get campaign data
      const campaignDoc = await db.collection('clients').doc(DEFAULT_CLIENT_ID)
        .collection('campaigns').doc(campaignId).get();
      const campaignData = campaignDoc.data();
      
      // Process the influencer data for aesthetic analysis
      const processedInfluencerData = {
        profileInfo: {
          username: username,
          name: influencerName,
          bio: influencerData.instagram?.biography || '',
          profilePicture: influencerData.instagram?.profile_picture_hd || ''
        }
      };
      
      aestheticAnalysisData = await performAestheticAnalysis(
        DEFAULT_CLIENT_ID, 
        campaignId, 
        influencerId, 
        influencerName, 
        influencerData, 
        webAnalysisData
      );
      console.log('   ✅ Aesthetic analysis completed');
    } else {
      console.log('   ✅ Using cached aesthetic analysis');
    }
    
    // Step 7: Perform ROI analysis
    console.log('\n💰 STEP 7: Performing ROI analysis...');
    let roiAnalysisData;
    const roiAnalysisPath = `/runs/${campaignId}_${username}_roi_analysis.json`;
    
    if (useCache && skipSteps) {
      roiAnalysisData = await getCachedJson(bucketName, roiAnalysisPath);
    }
    
    if (!roiAnalysisData) {
      console.log('   🔄 No cached ROI analysis found or cache disabled. Performing ROI analysis...');
      const influencerName = influencerData.instagram?.full_name || username;
      
      // Get campaign data
      const campaignDoc = await db.collection('clients').doc(DEFAULT_CLIENT_ID)
        .collection('campaigns').doc(campaignId).get();
      const campaignData = campaignDoc.data();
      
      // Process the influencer data for ROI analysis
      const processedInfluencerData = {
        profileInfo: {
          username: username,
          name: influencerName,
          bio: influencerData.instagram?.biography || '',
          profilePicture: influencerData.instagram?.profile_picture_hd || '',
          followers: influencerData.instagram?.follower_count || 0,
          engagement: influencerData.instagram?.engagement_rate || 0
        }
      };
      
      roiAnalysisData = await performROIAnalysis(
        DEFAULT_CLIENT_ID, 
        campaignId, 
        influencerId, 
        influencerName, 
        campaignData, 
        processedInfluencerData, 
        webAnalysisData, 
        aestheticAnalysisData
      );
      console.log('   ✅ ROI analysis completed');
    } else {
      console.log('   ✅ Using cached ROI analysis');
    }
    
    // Step 8: Generate combined analysis
    console.log('\n🔄 STEP 8: Generating combined analysis...');
    const combinedAnalysis = await generateCombinedAnalysis(DEFAULT_CLIENT_ID, campaignId, influencerId);
    console.log('   ✅ Combined analysis generated');
    
    // Save the combined analysis to Cloud Storage
    await writeJsonToBucket(`/runs/${campaignId}_${username}_combined_analyses.json`, combinedAnalysis);
    
    console.log('\n='.repeat(80));
    console.log('🎉 ANALYSIS COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(80));
    console.log(`📊 Results saved with report ID: ${reportId}`);
    console.log(`📁 Campaign ID: ${campaignId}`);
    console.log(`👤 Influencer ID: ${influencerId}`);
    
    return {
      reportId,
      campaignId,
      influencerId,
      webAnalysis: webAnalysisData,
      aestheticAnalysis: aestheticAnalysisData,
      roiAnalysis: roiAnalysisData,
      combinedAnalysis
    };
  } catch (error) {
    console.error('❌ ERROR IN ANALYSIS PROCESS:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

/**
 * Create a test campaign
 * @param {string} reportId - The report ID
 * @returns {string} - The campaign ID
 */
async function createTestCampaign(reportId) {
  try {
    console.log('   🔄 Creating test campaign in Firestore...');
    
    // Create a test campaign object
    const testCampaign = {
      name: `Test Campaign ${reportId}`,
      description: 'A test campaign for influencer analysis',
      objectives: ['brand_awareness', 'engagement'],
      target_audience: {
        age_range: '18-34',
        gender: 'all',
        interests: ['fashion', 'lifestyle', 'travel']
      },
      content_themes: ['authentic', 'lifestyle', 'product_showcase'],
      brand_values: ['quality', 'authenticity', 'innovation'],
      visual_style: 'modern, clean, vibrant',
      tone_of_voice: 'friendly, conversational, professional',
      min_follower_count: 10000,
      max_follower_count: 500000,
      min_engagement_rate: 2,
      budget: {
        total: 10000,
        per_influencer: 1000
      },
      timeline: {
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      status: 'active',
      report_id: reportId
    };
    
    // Save the campaign to Firestore
    const campaignRef = await db.collection('clients').doc(DEFAULT_CLIENT_ID)
      .collection('campaigns').add(testCampaign);
    
    console.log(`   ✅ Test campaign created with ID: ${campaignRef.id}`);
    return campaignRef.id;
  } catch (error) {
    console.error('   ❌ Error creating test campaign:', error);
    throw error;
  }
}

/**
 * Fetch influencer data from Influencers Club
 * @param {string} username - The influencer's username
 * @param {string} platform - The platform
 * @param {boolean} useCache - Whether to use cached data
 * @returns {Object} - The influencer data
 */
async function fetchInfluencerData(username, platform, useCache) {
  try {
    console.log(`   🔄 Fetching data for ${username} on ${platform}...`);
    
    // Check if data is cached
    const filePath = `influencers/${username}_raw_data.json`;
    let influencerData = null;
    
    if (useCache) {
      console.log('   🔍 Checking for cached data...');
      influencerData = await getCachedJson(bucketName, filePath);
      
      if (influencerData) {
        console.log('   ✅ Found cached data');
        return influencerData;
      }
      
      console.log('   ⚠️ No cached data found');
    }
    
    // Fetch data from Influencers Club
    console.log('   🔄 Fetching data from Influencers Club API...');
    const connector = new InfluencersClubConnector();
    influencerData = await connector.fetchInfluencerData(username, 'username', platform);
    
    // Process the raw data
    console.log('   🔄 Processing raw data...');
    const processedData = await connector.processRawData(influencerData);
    
    // Save the raw data to Cloud Storage
    console.log('   💾 Saving raw data to Cloud Storage...');
    await writeJsonToBucket(filePath, influencerData);
    
    // Save the processed data to Cloud Storage
    console.log('   💾 Saving processed data to Cloud Storage...');
    await writeJsonToBucket(`influencers/${username}_processed_data.json`, processedData);
    
    console.log('   ✅ Data fetched and processed successfully');
    return influencerData;
  } catch (error) {
    console.error('   ❌ Error fetching influencer data:', error);
    throw error;
  }
}

/**
 * Create an influencer in Firestore
 * @param {string} username - The influencer's username
 * @param {Object} influencerData - The influencer data
 * @returns {string} - The influencer ID
 */
async function createInfluencer(username, influencerData) {
  try {
    console.log(`   🔄 Creating influencer ${username} in Firestore...`);
    
    // Extract relevant data
    const instagram = influencerData.instagram || {};
    
    // Create influencer object
    const influencer = {
      username: username,
      name: instagram.full_name || username,
      platform: 'instagram',
      follower_count: instagram.follower_count || 0,
      engagement_rate: instagram.engagement_rate || 0,
      bio: instagram.biography || '',
      profile_picture: instagram.profile_picture_hd || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Check if influencer already exists
    const influencersSnapshot = await db.collection('influencers')
      .where('username', '==', username)
      .limit(1)
      .get();
    
    let influencerId;
    
    if (!influencersSnapshot.empty) {
      // Update existing influencer
      influencerId = influencersSnapshot.docs[0].id;
      await db.collection('influencers').doc(influencerId).update({
        ...influencer,
        updated_at: new Date().toISOString()
      });
      console.log(`   ✅ Updated existing influencer with ID: ${influencerId}`);
    } else {
      // Create new influencer
      const influencerRef = await db.collection('influencers').add(influencer);
      influencerId = influencerRef.id;
      console.log(`   ✅ Created new influencer with ID: ${influencerId}`);
    }
    
    return influencerId;
  } catch (error) {
    console.error('   ❌ Error creating influencer:', error);
    throw error;
  }
}

/**
 * Associate an influencer with a campaign
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @param {string} username - The influencer's username
 */
async function associateInfluencerWithCampaign(campaignId, influencerId, username) {
  try {
    console.log(`   🔄 Associating influencer ${username} with campaign ${campaignId}...`);
    
    // Check if association already exists
    const associationSnapshot = await db.collection('clients').doc(DEFAULT_CLIENT_ID)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers')
      .where('influencer_id', '==', influencerId)
      .limit(1)
      .get();
    
    if (!associationSnapshot.empty) {
      console.log('   ✅ Influencer already associated with campaign');
      return;
    }
    
    // Create association
    const association = {
      influencer_id: influencerId,
      username: username,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    await db.collection('clients').doc(DEFAULT_CLIENT_ID)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .set(association);
    
    console.log('   ✅ Influencer associated with campaign');
  } catch (error) {
    console.error('   ❌ Error associating influencer with campaign:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (process.argv[1].endsWith('test-influencer-analysis.js')) {
  const username = process.argv[2] || 'cristiano'; // Default to 'cristiano' if no username provided
  const platform = process.argv[3] || 'instagram'; // Default to 'instagram' if no platform provided
  const useCache = process.argv[4] !== 'false'; // Default to true unless explicitly set to 'false'
  const skipSteps = process.argv[5] !== 'false'; // Default to true unless explicitly set to 'false'
  
  console.log(`Running test for ${username} on ${platform} (useCache: ${useCache}, skipSteps: ${skipSteps})`);
  
  testInfluencerAnalysis(username, platform, useCache, skipSteps)
    .then(results => {
      console.log('\n📊 ANALYSIS RESULTS SUMMARY:');
      console.log(`Report ID: ${results.reportId}`);
      console.log(`Campaign ID: ${results.campaignId}`);
      console.log(`Influencer ID: ${results.influencerId}`);
      
      // Print a summary of each analysis
      if (results.webAnalysis) {
        console.log('\n🌐 Web Analysis Summary:');
        console.log(`Risk Level: ${results.webAnalysis.risk_level || 'N/A'}`);
        console.log(`Sentiment Score: ${results.webAnalysis.sentiment_score || 'N/A'}`);
        
        if (results.webAnalysis.deep_dive_report) {
          const report = results.webAnalysis.deep_dive_report;
          console.log(`Aliases: ${report.aliases?.length || 0}`);
          console.log(`Timeline Events: ${report.timeline_events?.length || 0}`);
          console.log(`Press Mentions: ${report.press_mentions?.length || 0}`);
          console.log(`Controversies: ${report.controversies?.length || 0}`);
        }
      }
      
      if (results.aestheticAnalysis) {
        console.log('\n🎨 Aesthetic Analysis Summary:');
        console.log(`Brand Fit Score: ${results.aestheticAnalysis.brand_fit_score || 'N/A'}`);
        
        if (results.aestheticAnalysis.content_analysis) {
          const analysis = results.aestheticAnalysis.content_analysis;
          console.log(`Visual Fit: ${analysis.visual_fit || 'N/A'}`);
          console.log(`Content Themes: ${analysis.content_themes?.length || 0}`);
          console.log(`Red Flags: ${analysis.red_flags?.length || 0}`);
        }
      }
      
      if (results.roiAnalysis) {
        console.log('\n💰 ROI Analysis Summary:');
        
        if (results.roiAnalysis.finalists && results.roiAnalysis.finalists.length > 0) {
          const finalist = results.roiAnalysis.finalists[0];
          console.log(`Brand Fit Score: ${finalist.brand_fit_score || 'N/A'}`);
          console.log(`Risk Level: ${finalist.risk_level || 'N/A'}`);
          
          if (finalist.influencer_analysis?.roi_projection) {
            const roi = finalist.influencer_analysis.roi_projection;
            console.log(`Expected Impressions: ${roi.expected_impressions || 'N/A'}`);
            console.log(`Expected Engagement Rate: ${roi.expected_engagement_rate || 'N/A'}`);
            console.log(`ROI Rating: ${roi.roi_rating || 'N/A'}`);
          }
        }
      }
      
      if (results.combinedAnalysis) {
        console.log('\n🔄 Combined Analysis Summary:');
        console.log(`Name: ${results.combinedAnalysis.profileInfo?.name || 'N/A'}`);
        console.log(`Username: ${results.combinedAnalysis.profileInfo?.username || 'N/A'}`);
        console.log(`Followers: ${results.combinedAnalysis.profileInfo?.followers || 'N/A'}`);
        console.log(`Engagement: ${results.combinedAnalysis.profileInfo?.engagement || 'N/A'}`);
        
        if (results.combinedAnalysis.roiProjection) {
          const roi = results.combinedAnalysis.roiProjection;
          console.log(`ROI Rating: ${roi.roiRating || 'N/A'}`);
          console.log(`Risk Assessment: ${roi.riskAssessment || 'N/A'}`);
        }
      }
      
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export default testInfluencerAnalysis;

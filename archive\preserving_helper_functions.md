# Preserving Helper Functions

This document outlines how to preserve the helper functions while transitioning to Firestore.

## processInstagramImagePosts Function

### Current Implementation
```javascript
async function processInstagramImagePosts(data) {
  // Validate input structure
  if (!data.instagram || !Array.isArray(data.instagram.post_data)) {
    throw new Error('Invalid input: Expected data.instagram.post_data to be an array');
  }

  const imageUrls = [];

  // Append the HD profile picture as the first image, if available
  if (data.instagram.profile_picture_hd) {
    imageUrls.push(data.instagram.profile_picture_hd);
    console.log(`Profile Picture Added: ${data.instagram.profile_picture_hd}`);
  }

  // Process each post: get the first image from each post's media array
  data.instagram.post_data.forEach(post => {
    if (Array.isArray(post.media)) {
      const firstImageItem = post.media.find(mediaItem => mediaItem.type === 'image' && mediaItem.url);
      if (firstImageItem) {
        imageUrls.push(firstImageItem.url);
        console.log(`Image Found: ${firstImageItem.url}`);
      }
    }
  });

  // If no images are found, return an empty string.
  if (imageUrls.length === 0) {
    console.log("No image posts found. Returning an empty string.");
    return '';
  }

  // Download all images concurrently
  const imageBuffers = await Promise.all(imageUrls.map(url => downloadImage(url)));

  // Merge the images vertically using join-images
  const mergedImage = await joinImages(imageBuffers, { direction: 'vertical' });
  
  // Convert the merged image to JPEG format and get the buffer
  const mergedBuffer = await mergedImage.toFormat('jpeg').toBuffer();
  const base64Image = mergedBuffer.toString('base64');
  
  return base64Image;
}
```

### Firestore Implementation
This function should remain unchanged as it doesn't interact with storage directly. It processes Instagram images and returns a base64-encoded string, which is then used for aesthetic analysis.

## extractInfluencerProfileDirect Function

### Current Implementation
```javascript
async function extractInfluencerProfileDirect(data) {
  // Helper function to format numbers with commas
  const formatNumber = num => (num != null && !isNaN(num)) ? Number(num).toLocaleString() : "";

  // Helper function to compute engagement rate as a percentage string with two decimals
  const computeEngagementRate = (totalEngagement, followerCount) => {
    if (followerCount > 0) {
      return ((totalEngagement / followerCount) * 100).toFixed(2) + "%";
    }
    return "";
  };

  // -- Basic Fields (from root) --
  const email = data.email || "";
  const location = data.location || "";
  const speakingLanguage = data.speaking_language || "";
  const firstName = data.first_name || "";
  const profilePicture = await cacheImage(data.instagram.profile_picture_hd) || "";
  const hasBrandDeals = data.has_brand_deals || false;
  const hasLinkInBio = data.has_link_in_bio || false;
  const isBusiness = data.is_business || false;
  const isCreator = data.is_creator || false;
  const postData = data.post_data || {};
  const influencerCategories = data.instagram.niche_class.join(' - ') || "";

  // -- Instagram Data Extraction --
  const insta = data.instagram || {};
  const instaUsername = insta.username || "";
  const instaFollowerCount = insta.follower_count != null ? insta.follower_count : 0;
  const instaBio = insta.biography || "";
  const instaEngagementPercent = insta.engagement_percent != null ? insta.engagement_percent : 0;
  const instaLatestPost = insta.latest_post || {};
  const instaPostEngagement = instaLatestPost.engagement || {};
  const instaPostMedia = Array.isArray(instaLatestPost.media) ? instaLatestPost.media : [];

  // -- YouTube Data Extraction --
  const yt = data.youtube || {};
  const ytSubscriberCount = yt.subscriber_count != null ? yt.subscriber_count : 0;
  const ytLatestVideo = yt.latest_video || {};
  const ytVideoViews = ytLatestVideo.views != null ? ytLatestVideo.views : 0;
  const ytVideoLikes = ytLatestVideo.likes != null ? ytLatestVideo.likes : 0;
  const ytVideoComments = ytLatestVideo.comments != null ? ytLatestVideo.comments : 0;

  // -- TikTok Data Extraction --
  const tt = data.tiktok || {};
  const ttUsername = tt.username || "";
  const ttFollowerCount = tt.follower_count != null ? tt.follower_count : 0;
  const ttLatestPost = tt.latest_post || {};
  const ttPostMedia = ttLatestPost.media || {};
  const ttPostEngagement = ttLatestPost.engagement || {};

  // -- Creator Has --
  const creatorHas = Array.isArray(data.creator_has) ? data.creator_has : [];

  // -- Assemble profileInfo --
  const profileInfo = {
    username: instaUsername,
    profileImageUrl: profilePicture,
    category: influencerCategories,
    bio: instaBio
  };

  // -- Assemble Influencer Stats --
  const platforms = [];
  
  // Instagram platform: use provided engagement percent
  platforms.push({
    name: "Instagram",
    followers: formatNumber(instaFollowerCount),
    engagement: instaEngagementPercent != null ? `${instaEngagementPercent}%` : ""
  });

  // YouTube platform: compute engagement rate if video exists
  if (ytLatestVideo.video_id) {
    const ytTotalEngagement = (ytVideoLikes || 0) + (ytVideoComments || 0);
    platforms.push({
      name: "YouTube",
      followers: formatNumber(ytSubscriberCount),
      engagement: ytSubscriberCount > 0 ? computeEngagementRate(ytTotalEngagement, ytSubscriberCount) : ""
    });
  }

  // TikTok platform: compute engagement rate using likes, comments, and shares
  if (ttLatestPost.post_id) {
    const ttTotalEngagement = (ttPostEngagement.likes || 0) + (ttPostEngagement.comments || 0) + (ttPostEngagement.shares || 0);
    platforms.push({
      name: "TikTok",
      followers: formatNumber(ttFollowerCount),
      engagement: ttFollowerCount > 0 ? computeEngagementRate(ttTotalEngagement, ttFollowerCount) : ""
    });
  }

  // Engagement Rate object (using Instagram metric)
  const engagementRate = {
    value: instaEngagementPercent != null ? `${instaEngagementPercent}%` : "",
    description: "Instagram engagement rate."
  };

  // Content Types: Extract unique media types from all Instagram posts.
  const contentTypesSet = new Set();
  if (Array.isArray(insta.post_data)) {
    insta.post_data.forEach(post => {
      if (Array.isArray(post.media)) {
        post.media.forEach(mediaItem => {
          if (mediaItem.type) {
            contentTypesSet.add(mediaItem.type);
          }
        });
      }
    });
  }
  const contentTypes = Array.from(contentTypesSet);

  const influencerStats = {
    platforms,
    engagementRate,
    contentTypes
  };

  // -- Recent Posts --
  const recentPosts = Array.isArray(insta.post_data)
    ? await Promise.all(insta.post_data.map(async post => {
        let mediaUrl = "";
        if (Array.isArray(post.media) && post.media.length > 0) {
          const firstMedia = post.media[0];
          if (firstMedia.type === "image") {
            mediaUrl = firstMedia.url;
          }
        }
        return {
          id: parseInt(post.post_id, 10) || 0,
          imageUrl: mediaUrl ? await cacheImage(mediaUrl) : "",
          likes: post.engagement && post.engagement.likes != null ? formatNumber(post.engagement.likes) : "0",
          comments: post.engagement && post.engagement.comments != null ? formatNumber(post.engagement.comments) : "0"
        };
      }))
    : [];

  // -- Return the assembled influencer profile --
  return {
    profileInfo,
    influencerStats,
    recentPosts
  };
}
```

### Firestore Implementation
This function should remain unchanged as it processes raw influencer data into a standardized format. It doesn't interact with storage directly.

## extractJSON Function

### Current Implementation
```javascript
function extractJSON(str) {
  // Find the first occurrence of '{'
  const firstIndex = str.indexOf('{');
  if (firstIndex === -1) return null;

  // Use a stack approach to find the matching closing '}'
  let stack = 0;
  let endIndex = -1;
  for (let i = firstIndex; i < str.length; i++) {
    if (str[i] === '{') {
      stack++;
    } else if (str[i] === '}') {
      stack--;
      // When the stack is empty, we found the matching closing brace
      if (stack === 0) {
        endIndex = i;
        break;
      }
    }
  }

  // If no complete JSON object is found, return null
  if (endIndex === -1) return null;

  // Extract the potential JSON substring
  const jsonString = str.substring(firstIndex, endIndex + 1);
  try {
    // Parse and return the JSON object
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Invalid JSON format:", error);
    console.log(`Erroneous JSON: ${str}`)
    return null;
  }
}
```

### Firestore Implementation
This function should remain unchanged as it's a utility function for extracting JSON from text responses.

## deepMerge Function

### Current Implementation
```javascript
function deepMerge(target, source) {
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      // If both values are objects (and not arrays), merge them recursively
      if (
        target[key] &&
        typeof target[key] === "object" &&
        !Array.isArray(target[key]) &&
        typeof source[key] === "object" &&
        !Array.isArray(source[key])
      ) {
        target[key] = deepMerge({ ...target[key] }, source[key]);
      } else {
        // Otherwise, assign the source value to the target
        target[key] = source[key];
      }
    }
  }
  return target;
}
```

### Firestore Implementation
This function should remain unchanged as it's a utility function for merging objects.

## cacheImage Function

### Current Implementation
```javascript
async function cacheImage(imageUrl) {
  if (!imageUrl) return "";
  
  try {
    // Generate a unique filename based on the URL
    const urlHash = crypto.createHash('md5').update(imageUrl).digest('hex');
    const filename = `${urlHash}.jpg`;
    const cachePath = `image-cache/${filename}`;
    
    // Check if the image is already cached
    try {
      await storage.bucket(bucketName).file(cachePath).getMetadata();
      console.log(`Image already cached: ${imageUrl}`);
      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (error) {
      // Image not cached, proceed with download
    }
    
    // Download the image
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const buffer = Buffer.from(response.data, 'binary');
    
    // Upload to Cloud Storage
    await storage.bucket(bucketName).file(cachePath).save(buffer, {
      metadata: {
        contentType: response.headers['content-type'] || 'image/jpeg'
      }
    });
    
    // Make the file publicly accessible
    await storage.bucket(bucketName).file(cachePath).makePublic();
    
    console.log(`Image cached: ${imageUrl}`);
    return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
  } catch (error) {
    console.error(`Error caching image: ${error.message}`);
    return imageUrl; // Return the original URL if caching fails
  }
}
```

### Firestore Implementation
This function should remain largely unchanged, but we should update the path to include the influencer ID for better organization:

```javascript
async function cacheImage(imageUrl, influencerId = null) {
  if (!imageUrl) return "";
  
  try {
    // Generate a unique filename based on the URL
    const urlHash = crypto.createHash('md5').update(imageUrl).digest('hex');
    const filename = `${urlHash}.jpg`;
    
    // If influencerId is provided, include it in the path
    const cachePath = influencerId 
      ? `influencers/${influencerId}/images/${filename}`
      : `image-cache/${filename}`;
    
    // Check if the image is already cached
    try {
      await storage.bucket(bucketName).file(cachePath).getMetadata();
      console.log(`Image already cached: ${imageUrl}`);
      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (error) {
      // Image not cached, proceed with download
    }
    
    // Download the image
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const buffer = Buffer.from(response.data, 'binary');
    
    // Upload to Cloud Storage
    await storage.bucket(bucketName).file(cachePath).save(buffer, {
      metadata: {
        contentType: response.headers['content-type'] || 'image/jpeg'
      }
    });
    
    // Make the file publicly accessible
    await storage.bucket(bucketName).file(cachePath).makePublic();
    
    console.log(`Image cached: ${imageUrl}`);
    return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
  } catch (error) {
    console.error(`Error caching image: ${error.message}`);
    return imageUrl; // Return the original URL if caching fails
  }
}
```

## downloadImage Function

### Current Implementation
```javascript
async function downloadImage(url) {
  try {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data, 'binary');
  } catch (error) {
    console.error(`Error downloading image from ${url}: ${error.message}`);
    throw error;
  }
}
```

### Firestore Implementation
This function should remain unchanged as it's a utility function for downloading images.

## getCachedJson Function

### Current Implementation
```javascript
async function getCachedJson(bucketName, filePath) {
  try {
    const file = storage.bucket(bucketName).file(filePath);
    const [exists] = await file.exists();
    
    if (!exists) {
      console.log(`File not found: ${filePath}`);
      return false;
    }
    
    const [content] = await file.download();
    return JSON.parse(content.toString());
  } catch (error) {
    console.error(`Error getting cached JSON: ${error.message}`);
    return false;
  }
}
```

### Firestore Implementation
This function should be updated to check Firestore first, then fall back to Cloud Storage:

```javascript
async function getCachedJson(bucketName, filePath, firestoreRef = null) {
  try {
    // If a Firestore reference is provided, try to get the data from Firestore first
    if (firestoreRef) {
      const doc = await firestoreRef.get();
      if (doc.exists) {
        console.log(`Data found in Firestore: ${firestoreRef.path}`);
        return doc.data();
      }
    }
    
    // Fall back to Cloud Storage
    const file = storage.bucket(bucketName).file(filePath);
    const [exists] = await file.exists();
    
    if (!exists) {
      console.log(`File not found: ${filePath}`);
      return false;
    }
    
    const [content] = await file.download();
    return JSON.parse(content.toString());
  } catch (error) {
    console.error(`Error getting cached JSON: ${error.message}`);
    return false;
  }
}
```

## writeJsonToBucket Function

### Current Implementation
```javascript
async function writeJsonToBucket(filePath, jsonData) {
  try {
    const file = storage.bucket(bucketName).file(filePath);
    await file.save(JSON.stringify(jsonData, null, 2), {
      metadata: { contentType: 'application/json' }
    });
    console.log(`JSON written to ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error writing JSON to bucket: ${error.message}`);
    throw error;
  }
}
```

### Firestore Implementation
This function should be updated to write to both Firestore and Cloud Storage:

```javascript
async function writeJsonToBucket(filePath, jsonData, firestoreRef = null) {
  try {
    // Write to Cloud Storage
    const file = storage.bucket(bucketName).file(filePath);
    await file.save(JSON.stringify(jsonData, null, 2), {
      metadata: { contentType: 'application/json' }
    });
    console.log(`JSON written to ${filePath}`);
    
    // If a Firestore reference is provided, also write to Firestore
    if (firestoreRef) {
      await firestoreRef.set(jsonData);
      console.log(`JSON written to Firestore: ${firestoreRef.path}`);
    }
    
    return true;
  } catch (error) {
    console.error(`Error writing JSON: ${error.message}`);
    throw error;
  }
}
```

## Conclusion

Most helper functions should remain unchanged during the transition to Firestore. Only the functions that directly interact with storage (`cacheImage`, `getCachedJson`, and `writeJsonToBucket`) need to be updated to work with both Firestore and Cloud Storage. By preserving these helper functions, we ensure that the system continues to function as expected while benefiting from the improved data storage and retrieval capabilities of Firestore.

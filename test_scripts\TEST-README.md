# Influencer Analysis Test Script

A comprehensive test script for the influencer analysis flow that uses real data and production components.

## Overview

This test script allows you to run a full analysis on an influencer by providing their username. The script:

1. Uses sample campaign data from `sample_jsons/campaign_analysis.json`
2. Uses sample influencer discovery data from `sample_jsons/influencer_discovery.json`
3. Fetches real influencer data from the Influencers Club API (with 14-day caching)
4. Uses the production AI analysis components
5. Writes results to the `test_outputs` directory with timestamp-based filenames

## Prerequisites

- Node.js 16+
- Firebase project with Firestore enabled
- Firebase credentials properly configured
- OpenAI API key with access to GPT-4.1
- Influencers Club API credentials

## Usage

### Basic Usage

```bash
node test-analysis-flow.js <username>
```

Example:
```bash
node test-analysis-flow.js cyborggainz
```

### List Available Influencers

To see a list of available influencers from the sample data:

```bash
node test-analysis-flow.js
```

### Advanced Options

```bash
node test-analysis-flow.js <username> --verbose=true|false --force_refresh=true|false --log_level=debug|info|warn|error --skip_steps=web,aesthetic,roi
```

Options:
- `--verbose`: Whether to display detailed logs (default: true)
- `--force_refresh`: Whether to force a refresh of influencer data even if recent (default: false)
- `--log_level`: Set the logging detail level (default: info)
- `--skip_steps`: Comma-separated list of steps to skip for targeted testing

Examples:
```bash
# Run with minimal logging
node test-analysis-flow.js cyborggainz --verbose=false

# Force refresh of influencer data
node test-analysis-flow.js cyborggainz --force_refresh=true

# Run with debug-level logging
node test-analysis-flow.js cyborggainz --log_level=debug

# Skip the aesthetic analysis step
node test-analysis-flow.js cyborggainz --skip_steps=aesthetic
```

## Debugging

The script includes detailed logging to help identify and fix issues:

- Each step is clearly marked with a header
- Substeps are indented and prefixed with status indicators
- Errors are clearly marked and include stack traces
- The script will attempt to continue even if some steps fail
- Performance metrics are displayed for each step

## Output

The script generates several output files in the `test_outputs` directory:

- `<username>_raw_data_<timestamp>.json`: Raw influencer data from Influencers Club
- `<username>_web_analysis_<timestamp>.json`: Web analysis results
- `<username>_aesthetic_analysis_<timestamp>.json`: Aesthetic analysis results
- `<username>_roi_analysis_<timestamp>.json`: ROI analysis results
- `analysis_<timestamp>_<username>.json`: Combined analysis results

The script also outputs:
- A summary of the analysis results
- The campaign ID and influencer ID
- Performance metrics for each step
- The location of the saved analysis files

## Troubleshooting

### Firestore Connectivity Issues

If you encounter Firestore connectivity issues:
1. Check that your Firebase credentials are correctly set up
2. Verify that your Firebase project has Firestore enabled
3. Check your network connectivity to Google Cloud
4. Ensure your service account has the necessary permissions

### API Issues

If you encounter API issues:
1. Check your API credentials and rate limits
2. Verify that the APIs are configured correctly
3. Check if the required features (web search, image processing) are enabled
4. The influencer might not have sufficient data for analysis

### Performance Issues

If the script is running slowly:
1. Use the `--skip_steps` option to skip specific analysis steps
2. Use the `--verbose=false` option to reduce logging
3. Check the performance metrics in the results summary

## Notes

- The script uses the Responses API for web analysis and aesthetic analysis
- The script uses the Assistants API for ROI analysis
- The script saves all data to both Firestore and the test_outputs directory
- The script uses a unique timestamp for each output file
- The script only updates influencer data if it's older than 14 days (unless forced)
- The script validates Firestore connectivity before running the analysis

// cors.js
// CORS middleware

import cors from 'cors'

/**
 * CORS options
 */
const corsOptions = {
  origin: (origin, callback) => {
    // allow requests that send no Origin (curl / mobile apps)
    if (!origin) return callback(null, true)

    // hosts we trust
    const allowedHosts = [
      'lovableproject.com',
      'lovable.app',
      'realizeanalytics.com',
      'palas-find-influencer-22.lovable.app',
      'palas.realizeanalytics.com',
      'preview--palas-find-influencer-22.lovable.app'
    ]

    let host
    try {
      host = new URL(origin).hostname           // strips protocol + path
    } catch {
      return callback(new Error('Bad Origin header'))
    }

    // exact host match
    if (allowedHosts.includes(host)) return callback(null, true)

    // subdomain of a base domain we trust
    const ok = allowedHosts.some(d => host.endsWith(`.${d}`))
    return ok ? callback(null, true) : callback(new Error('Not allowed by CORS'))
  },

  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}

export { cors, corsOptions }
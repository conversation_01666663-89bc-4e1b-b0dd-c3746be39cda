# Optimal File Structure for Firestore Implementation

This document outlines the recommended file structure for productionalizing the Firestore update. The structure is designed to be modular, maintainable, and scalable.

## Overview

Instead of keeping all code in a single file like the original implementation, we'll organize the codebase into logical modules with clear responsibilities. This approach will make the code easier to maintain, test, and extend.

## Root Directory Structure

```
/palas_firestore_update/
├── config/                  # Configuration files
├── connectors/              # API connectors
├── helpers/                 # Helper functions
├── models/                  # Data models
├── services/                # Business logic services
├── routes/                  # API routes
├── middleware/              # Express middleware
├── utils/                   # Utility functions
├── scripts/                 # Migration and utility scripts
├── tests/                   # Test files
├── old_implementation/      # Original implementation (unchanged)
├── .env                     # Environment variables
├── .gitignore               # Git ignore file
├── package.json             # Project dependencies
├── README.md                # Project documentation
└── server.js                # Main application entry point
```

## Detailed Structure

### 1. Config Directory

```
/config/
├── firebase.js              # Firebase initialization
├── storage.js               # Cloud Storage configuration
└── constants.js             # Application constants
```

**firebase.js**
```javascript
import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';

function initializeFirebase(serviceAccount, storageBucket) {
  // Firebase initialization code
}

export { initializeFirebase };
```

**storage.js**
```javascript
import { Storage } from '@google-cloud/storage';

const storage = new Storage();
const bucketName = process.env.STORAGE_BUCKET || 'palas-run-cache';

export { storage, bucketName };
```

**constants.js**
```javascript
// OpenAI API keys and assistant IDs
export const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
export const CAMPAIGN_ANALYSIS_AGENT = process.env.CAMPAIGN_ANALYSIS_AGENT;
export const DISCOVERY_AGENT = process.env.DISCOVERY_AGENT;
export const SEED_INFLUENCER_AGENT = process.env.SEED_INFLUENCER_AGENT;
export const WEB_ANALYSIS_AGENT = process.env.WEB_ANALYSIS_AGENT;
export const ROI_ANALYSIS_AGENT = process.env.ROI_ANALYSIS_AGENT;

// Influencers Club API key
export const INFLUENCERS_API_KEY = process.env.INFLUENCERS_API_KEY;

// Other constants
export const DEFAULT_CLIENT_ID = 'default_client';
```

### 2. Connectors Directory

```
/connectors/
├── influencers-club-connector.js    # Influencers Club API connector
└── openai-connector.js              # OpenAI API connector
```

**influencers-club-connector.js**
```javascript
import axios from 'axios';
import { storage, bucketName } from '../config/storage.js';
import { getFirestore } from 'firebase-admin/firestore';
import { INFLUENCERS_API_KEY } from '../config/constants.js';

class InfluencersClubConnector {
  constructor(apiKey = INFLUENCERS_API_KEY, bucketName = bucketName) {
    // Constructor code
  }

  async fetchInfluencerData(filterValue, filterKey = "username", platform = "instagram", emailRequired = "must_have", postDataRequired = true) {
    // Method implementation
  }

  async processRawData(rawData) {
    // Method implementation
  }

  async cacheImage(imageUrl, filename) {
    // Method implementation
  }

  async enrichInfluencer(filterValue, filterKey = "username", platform = "instagram", options = {}) {
    // Method implementation
  }

  // Helper methods
  extractBrandMentions(rawData) {
    // Method implementation
  }

  extractPartnerships(rawData) {
    // Method implementation
  }
}

export default InfluencersClubConnector;
```

**openai-connector.js**
```javascript
import OpenAI from 'openai';
import { OPENAI_API_KEY } from '../config/constants.js';

class OpenAIConnector {
  constructor(apiKey = OPENAI_API_KEY) {
    this.client = new OpenAI({ apiKey });
  }

  async processAgent(agentID, prompt) {
    // Method implementation
  }

  async parseCompletion(model, messages, maxCompletionTokens = 16000) {
    // Method implementation
  }
}

export default OpenAIConnector;
```

### 3. Helpers Directory

```
/helpers/
├── analysis-generators.js    # Combined and merged analysis generators
├── image-processors.js       # Image processing functions
├── storage-helpers.js        # Storage helper functions
└── json-helpers.js           # JSON processing helpers
```

**analysis-generators.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';
import { storage, bucketName } from '../config/storage.js';
import { deepMerge } from '../utils/object-utils.js';

async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  // Function implementation
}

async function getProcessedInfluencerData(influencerId) {
  // Function implementation
}

async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  // Function implementation
}

export {
  generateCombinedAnalysis,
  getProcessedInfluencerData,
  generateMergedAnalysis
};
```

**image-processors.js**
```javascript
import axios from 'axios';
import joinImages from 'join-images';
import { storage, bucketName } from '../config/storage.js';
import crypto from 'crypto';

async function processInstagramImagePosts(data) {
  // Function implementation
}

async function downloadImage(url) {
  // Function implementation
}

async function cacheImage(imageUrl, influencerId = null) {
  // Function implementation
}

export {
  processInstagramImagePosts,
  downloadImage,
  cacheImage
};
```

**storage-helpers.js**
```javascript
import { storage, bucketName } from '../config/storage.js';
import { getFirestore } from 'firebase-admin/firestore';

async function getCachedJson(bucketName, filePath, firestoreRef = null) {
  // Function implementation
}

async function writeJsonToBucket(filePath, jsonData, firestoreRef = null) {
  // Function implementation
}

export {
  getCachedJson,
  writeJsonToBucket
};
```

**json-helpers.js**
```javascript
function extractJSON(str) {
  // Function implementation
}

function extractInfluencerProfileDirect(data) {
  // Function implementation
}

export {
  extractJSON,
  extractInfluencerProfileDirect
};
```

### 4. Models Directory

```
/models/
├── campaign.js               # Campaign data model
├── influencer.js             # Influencer data model
└── analysis.js               # Analysis data models
```

**campaign.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';

class Campaign {
  constructor(data = {}) {
    this.name = data.name || '';
    this.report_id = data.report_id || '';
    this.product_description = data.product_description || '';
    this.influencer_gender = data.influencer_gender || '';
    this.influencer_niche = data.influencer_niche || '';
    this.influencer_age = data.influencer_age || '';
    this.influencer_personality = data.influencer_personality || '';
    this.influencer_aesthetic = data.influencer_aesthetic || '';
    this.min_follower_count = data.min_follower_count || 0;
    this.max_follower_count = data.max_follower_count || 0;
    this.min_engagement_rate = data.min_engagement_rate || 0;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
    this.status = data.status || 'active';
  }

  static async create(clientId, data) {
    // Method implementation
  }

  static async findById(clientId, campaignId) {
    // Method implementation
  }

  static async update(clientId, campaignId, data) {
    // Method implementation
  }
}

export default Campaign;
```

**influencer.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';

class Influencer {
  constructor(data = {}) {
    this.username = data.username || '';
    this.full_name = data.full_name || '';
    this.email = data.email || '';
    this.location = data.location || '';
    this.speaking_language = data.speaking_language || '';
    this.has_brand_deals = data.has_brand_deals || false;
    this.has_link_in_bio = data.has_link_in_bio || false;
    this.is_business = data.is_business || false;
    this.is_creator = data.is_creator || false;
    this.platforms = data.platforms || {};
    this.creator_has = data.creator_has || [];
    this.last_updated = data.last_updated || null;
    this.created_at = data.created_at || null;
  }

  static async findByUsername(username) {
    // Method implementation
  }

  static async create(data) {
    // Method implementation
  }

  static async update(influencerId, data) {
    // Method implementation
  }
}

export default Influencer;
```

**analysis.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';

class WebAnalysis {
  constructor(data = {}) {
    this.name = data.name || '';
    this.sentiment_score = data.sentiment_score || 0;
    this.risk_level = data.risk_level || 'Medium';
    this.deep_dive_report = data.deep_dive_report || {};
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  static async create(influencerId, data) {
    // Method implementation
  }
}

class AestheticAnalysis {
  constructor(data = {}) {
    this.name = data.name || '';
    this.brand_fit_score = data.brand_fit_score || 0;
    this.content_analysis = data.content_analysis || {};
    this.created_at = data.created_at || null;
  }

  static async create(clientId, campaignId, influencerId, data) {
    // Method implementation
  }
}

class ROIAnalysis {
  constructor(data = {}) {
    this.brand_fit_score = data.brand_fit_score || 0;
    this.brand_fit_description = data.brand_fit_description || '';
    this.risk_level = data.risk_level || 'Medium';
    this.risk_description = data.risk_description || '';
    this.influencer_analysis = data.influencer_analysis || {};
    this.created_at = data.created_at || null;
  }

  static async create(clientId, campaignId, influencerId, data) {
    // Method implementation
  }
}

export {
  WebAnalysis,
  AestheticAnalysis,
  ROIAnalysis
};
```

### 5. Services Directory

```
/services/
├── campaign-service.js        # Campaign-related business logic
├── influencer-service.js      # Influencer-related business logic
├── analysis-service.js        # Analysis-related business logic
└── discovery-service.js       # Discovery-related business logic
```

**campaign-service.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';
import Campaign from '../models/campaign.js';
import OpenAIConnector from '../connectors/openai-connector.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { CAMPAIGN_ANALYSIS_AGENT } from '../config/constants.js';

async function createCampaign(clientId, campaignData) {
  // Function implementation
}

async function getCampaign(clientId, campaignId) {
  // Function implementation
}

async function updateCampaign(clientId, campaignId, campaignData) {
  // Function implementation
}

export {
  createCampaign,
  getCampaign,
  updateCampaign
};
```

**influencer-service.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';
import Influencer from '../models/influencer.js';
import InfluencersClubConnector from '../connectors/influencers-club-connector.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';

async function enrichInfluencer(username, platform = "instagram") {
  // Function implementation
}

async function getInfluencer(influencerId) {
  // Function implementation
}

async function addInfluencerToCampaign(clientId, campaignId, influencerId, username) {
  // Function implementation
}

export {
  enrichInfluencer,
  getInfluencer,
  addInfluencerToCampaign
};
```

**analysis-service.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';
import { WebAnalysis, AestheticAnalysis, ROIAnalysis } from '../models/analysis.js';
import OpenAIConnector from '../connectors/openai-connector.js';
import { processInstagramImagePosts } from '../helpers/image-processors.js';
import { extractJSON } from '../helpers/json-helpers.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { generateCombinedAnalysis, generateMergedAnalysis } from '../helpers/analysis-generators.js';
import { WEB_ANALYSIS_AGENT, ROI_ANALYSIS_AGENT } from '../config/constants.js';

async function performWebAnalysis(clientId, campaignId, influencerId, influencerName, influencerUsername) {
  // Function implementation
}

async function performAestheticAnalysis(clientId, campaignId, influencerId, influencerName, influencerData, webAnalysisData) {
  // Function implementation
}

async function performROIAnalysis(clientId, campaignId, influencerId, influencerName, campaignData, influencerData, webAnalysisData, aestheticAnalysisData) {
  // Function implementation
}

async function getCombinedAnalysis(clientId, campaignId, influencerId) {
  // Function implementation
}

async function getMergedAnalysis(clientId, campaignId, influencerId) {
  // Function implementation
}

export {
  performWebAnalysis,
  performAestheticAnalysis,
  performROIAnalysis,
  getCombinedAnalysis,
  getMergedAnalysis
};
```

**discovery-service.js**
```javascript
import { getFirestore } from 'firebase-admin/firestore';
import OpenAIConnector from '../connectors/openai-connector.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { DISCOVERY_AGENT, SEED_INFLUENCER_AGENT } from '../config/constants.js';

async function getSeedInfluencers(clientId, campaignId, campaignData) {
  // Function implementation
}

async function discoverInfluencers(clientId, campaignId, campaignData) {
  // Function implementation
}

export {
  getSeedInfluencers,
  discoverInfluencers
};
```

### 6. Routes Directory

```
/routes/
├── campaign-routes.js         # Campaign-related routes
├── influencer-routes.js       # Influencer-related routes
├── analysis-routes.js         # Analysis-related routes
└── discovery-routes.js        # Discovery-related routes
```

**campaign-routes.js**
```javascript
import express from 'express';
import { createCampaign, getCampaign, updateCampaign } from '../services/campaign-service.js';

const router = express.Router();

router.post('/', async (req, res) => {
  try {
    const { campaignData, clientId } = req.body;
    const campaignId = await createCampaign(clientId, campaignData);
    res.status(200).json({ campaignId });
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({ error: 'Failed to create campaign' });
  }
});

router.get('/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId } = req.query;
    const campaign = await getCampaign(clientId, campaignId);
    res.status(200).json(campaign);
  } catch (error) {
    console.error('Error getting campaign:', error);
    res.status(500).json({ error: 'Failed to get campaign' });
  }
});

router.put('/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { campaignData, clientId } = req.body;
    await updateCampaign(clientId, campaignId, campaignData);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error updating campaign:', error);
    res.status(500).json({ error: 'Failed to update campaign' });
  }
});

export default router;
```

**influencer-routes.js**
```javascript
import express from 'express';
import { enrichInfluencer, getInfluencer, addInfluencerToCampaign } from '../services/influencer-service.js';

const router = express.Router();

router.post('/enrich', async (req, res) => {
  try {
    const { username, platform } = req.body;
    const result = await enrichInfluencer(username, platform);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error enriching influencer:', error);
    res.status(500).json({ error: 'Failed to enrich influencer' });
  }
});

router.get('/:influencerId', async (req, res) => {
  try {
    const { influencerId } = req.params;
    const influencer = await getInfluencer(influencerId);
    res.status(200).json(influencer);
  } catch (error) {
    console.error('Error getting influencer:', error);
    res.status(500).json({ error: 'Failed to get influencer' });
  }
});

router.post('/campaigns/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { influencerId, username, clientId } = req.body;
    await addInfluencerToCampaign(clientId, campaignId, influencerId, username);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error adding influencer to campaign:', error);
    res.status(500).json({ error: 'Failed to add influencer to campaign' });
  }
});

export default router;
```

**analysis-routes.js**
```javascript
import express from 'express';
import { 
  performWebAnalysis, 
  performAestheticAnalysis, 
  performROIAnalysis,
  getCombinedAnalysis,
  getMergedAnalysis
} from '../services/analysis-service.js';

const router = express.Router();

router.post('/web', async (req, res) => {
  try {
    const { clientId, campaignId, influencerId, influencerName, influencerUsername } = req.body;
    const result = await performWebAnalysis(clientId, campaignId, influencerId, influencerName, influencerUsername);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing web analysis:', error);
    res.status(500).json({ error: 'Failed to perform web analysis' });
  }
});

router.post('/aesthetic', async (req, res) => {
  try {
    const { clientId, campaignId, influencerId, influencerName, influencerData, webAnalysisData } = req.body;
    const result = await performAestheticAnalysis(clientId, campaignId, influencerId, influencerName, influencerData, webAnalysisData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing aesthetic analysis:', error);
    res.status(500).json({ error: 'Failed to perform aesthetic analysis' });
  }
});

router.post('/roi', async (req, res) => {
  try {
    const { clientId, campaignId, influencerId, influencerName, campaignData, influencerData, webAnalysisData, aestheticAnalysisData } = req.body;
    const result = await performROIAnalysis(clientId, campaignId, influencerId, influencerName, campaignData, influencerData, webAnalysisData, aestheticAnalysisData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing ROI analysis:', error);
    res.status(500).json({ error: 'Failed to perform ROI analysis' });
  }
});

router.get('/combined/:campaignId/:influencerId', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId } = req.query;
    const result = await getCombinedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting combined analysis:', error);
    res.status(500).json({ error: 'Failed to get combined analysis' });
  }
});

router.get('/merged/:campaignId/:influencerId', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId } = req.query;
    const result = await getMergedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting merged analysis:', error);
    res.status(500).json({ error: 'Failed to get merged analysis' });
  }
});

export default router;
```

**discovery-routes.js**
```javascript
import express from 'express';
import { getSeedInfluencers, discoverInfluencers } from '../services/discovery-service.js';

const router = express.Router();

router.post('/seed', async (req, res) => {
  try {
    const { clientId, campaignId, campaignData } = req.body;
    const result = await getSeedInfluencers(clientId, campaignId, campaignData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting seed influencers:', error);
    res.status(500).json({ error: 'Failed to get seed influencers' });
  }
});

router.post('/discover', async (req, res) => {
  try {
    const { clientId, campaignId, campaignData } = req.body;
    const result = await discoverInfluencers(clientId, campaignId, campaignData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers' });
  }
});

export default router;
```

### 7. Middleware Directory

```
/middleware/
├── auth.js                   # Authentication middleware
├── error-handler.js          # Error handling middleware
└── cors.js                   # CORS middleware
```

**auth.js**
```javascript
function authenticate(req, res, next) {
  // Authentication logic
  next();
}

export { authenticate };
```

**error-handler.js**
```javascript
function errorHandler(err, req, res, next) {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
}

export { errorHandler };
```

**cors.js**
```javascript
import cors from 'cors';

const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl)
    if (!origin) return callback(null, true);
    if (origin.endsWith("lovableproject.com") || origin.endsWith("lovable.app")) {
      return callback(null, true);
    } else {
      return callback(new Error("Not allowed by CORS"));
    }
  },
};

export { cors, corsOptions };
```

### 8. Utils Directory

```
/utils/
├── object-utils.js           # Object manipulation utilities
├── string-utils.js           # String manipulation utilities
└── validation-utils.js       # Validation utilities
```

**object-utils.js**
```javascript
function deepMerge(target, source) {
  // Function implementation
}

function isObject(item) {
  // Function implementation
}

export {
  deepMerge,
  isObject
};
```

**string-utils.js**
```javascript
function formatNumber(num) {
  // Function implementation
}

function computeEngagementRate(totalEngagement, followerCount) {
  // Function implementation
}

export {
  formatNumber,
  computeEngagementRate
};
```

**validation-utils.js**
```javascript
function validateCampaignData(data) {
  // Function implementation
}

function validateInfluencerData(data) {
  // Function implementation
}

export {
  validateCampaignData,
  validateInfluencerData
};
```

### 9. Scripts Directory

```
/scripts/
├── migrate-to-firestore.js    # Data migration script
└── create-indexes.js          # Firestore indexes creation script
```

**migrate-to-firestore.js**
```javascript
import { initializeFirebase } from '../config/firebase.js';
import { storage, bucketName } from '../config/storage.js';
import { getFirestore } from 'firebase-admin/firestore';

async function migrateToFirestore() {
  // Migration logic
}

migrateToFirestore().catch(console.error);
```

**create-indexes.js**
```javascript
import { initializeFirebase } from '../config/firebase.js';
import { getFirestore } from 'firebase-admin/firestore';

async function createIndexes() {
  // Index creation logic
}

createIndexes().catch(console.error);
```

### 10. Main Application File

**server.js**
```javascript
import express from 'express';
import bodyParser from 'body-parser';
import dotenv from 'dotenv';
import { initializeFirebase } from './config/firebase.js';
import { cors, corsOptions } from './middleware/cors.js';
import { errorHandler } from './middleware/error-handler.js';
import campaignRoutes from './routes/campaign-routes.js';
import influencerRoutes from './routes/influencer-routes.js';
import analysisRoutes from './routes/analysis-routes.js';
import discoveryRoutes from './routes/discovery-routes.js';

// Load environment variables
dotenv.config();

// Initialize Firebase
const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT);
const storageBucket = process.env.STORAGE_BUCKET;
initializeFirebase(serviceAccount, storageBucket);

// Create Express app
const app = express();

// Apply middleware
app.use(cors(corsOptions));
app.options("*", cors(corsOptions));
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// Register routes
app.use('/api/campaigns', campaignRoutes);
app.use('/api/influencers', influencerRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/discovery', discoveryRoutes);

// Legacy route for backward compatibility
app.post('/api/analyze', async (req, res) => {
  try {
    // Legacy route handler
  } catch (error) {
    console.error('Error performing analysis:', error);
    res.status(500).json({ error: 'Failed to perform analysis' });
  }
});

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

## Benefits of This Structure

1. **Modularity**: Each file has a single responsibility, making the code easier to understand and maintain.
2. **Testability**: The modular structure makes it easier to write unit tests for each component.
3. **Scalability**: New features can be added without modifying existing code.
4. **Maintainability**: The clear separation of concerns makes it easier for multiple developers to work on the codebase.
5. **Reusability**: Common functionality is extracted into reusable modules.

## Implementation Strategy

1. **Create the Directory Structure**: Set up the directory structure as outlined above.
2. **Implement Core Components**: Start with the config, utils, and helpers directories.
3. **Implement Connectors**: Implement the API connectors.
4. **Implement Models**: Implement the data models.
5. **Implement Services**: Implement the business logic services.
6. **Implement Routes**: Implement the API routes.
7. **Implement Middleware**: Implement the middleware components.
8. **Implement Scripts**: Implement the migration and utility scripts.
9. **Create Main Application File**: Implement the main application entry point.
10. **Test and Refine**: Test the implementation and refine as needed.

## Conclusion

This file structure provides a solid foundation for productionalizing the Firestore update. It is designed to be modular, maintainable, and scalable, making it easier for multiple developers to work on the codebase together. The structure also maintains backward compatibility with the existing JSON-based system, ensuring a smooth transition to Firestore.

# Campaign Retrieval Diagnostic Test

## Overview

This test script diagnoses the campaign data retrieval issues you're experiencing. It performs comprehensive testing of:

1. **Firebase Connection** - Verifies database connectivity and permissions
2. **CORS Configuration** - Tests allowed origins and configuration
3. **Campaign Data Retrieval** - Deep dive into the specific campaign causing issues
4. **Parameter Validation** - Tests the complete-analysis endpoint parameters

## Quick Start

### Option 1: Direct Test Execution
```bash
node test-campaign-retrieval.js
```

### Option 2: Using Test Runner
```bash
node run-campaign-test.js
```

## What the Test Does

### 🔥 Firebase Connection Test
- Tests basic Firestore read/write operations
- Verifies client collection access
- Confirms Firebase credentials are working

### 🌐 CORS Configuration Test
- Loads and displays current CORS settings
- Shows allowed origins including your specific URL
- Tests CORS options configuration

### 📋 Campaign Retrieval Deep Dive
- **Target Campaign**: `Wfs5DW2kCVV0xy4tkSGQ`
- **Target Client**: `KVywTcxSRAS5yPXD7vMx3WkUKXw1`
- **Direct Firestore Query**: Raw document retrieval
- **Field Analysis**: Checks each expected field individually
- **Validation Logic Test**: Mimics the exact validation from `getCampaignData`
- **Function Test**: Calls the actual `getCampaignData` function

### 🧪 Parameter Validation Test
- Tests the exact parameters from your failed request
- Validates required fields for complete-analysis endpoint

## Expected Output

The test will show detailed logging with emojis for easy reading:

- ✅ = Success/Valid
- ❌ = Failure/Invalid
- 🔍 = Investigation/Analysis
- 📊 = Data/Statistics
- 🧪 = Testing/Validation

## Key Things to Look For

### 1. Campaign Data Issues
Look for these patterns in the output:

```
❌ VALIDATION FAILURE - This explains the error!
   🔍 Issue: Name field is missing, null, undefined, or empty string
```

### 2. Firebase Connection Issues
```
❌ Firestore Write/Read: FAILED
❌ Firebase Connection Test Failed: [error message]
```

### 3. CORS Configuration
```
🌐 Testing CORS Origins:
   1. https://palas-find-influencer-22.lovable.app
   2. https://palas-find-influencer-22.lovable.app/select-influencer
```

## Fixes Applied

### ✅ Variable Scope Issue Fixed
- Moved variable declarations outside try block in `routes/influencer-routes.js`
- Added fallback values for undefined variables in error handling

### ✅ CORS Configuration Updated
- Added your specific URL: `https://palas-find-influencer-22.lovable.app/select-influencer`
- Enhanced CORS logging for better debugging

### ✅ Enhanced Error Handling
- Better fallback values in complete-analysis endpoint
- More robust error responses

## Troubleshooting

### If Test Fails to Start
1. Ensure you're in the correct directory
2. Check that Firebase credentials are properly configured
3. Verify all dependencies are installed: `npm install`

### If Campaign Not Found
The test will:
1. Check if the client exists
2. List all campaigns for the client
3. Show you what campaigns are actually available

### If Validation Fails
The test will show exactly which field is causing the validation failure and why.

## Next Steps

After running the test:

1. **Review the output** for any ❌ indicators
2. **Check the validation results** to see what's missing
3. **Look at the field-by-field analysis** to understand the data structure
4. **Note any CORS issues** with your specific origin

The test results will guide us to the exact fix needed for your campaign retrieval issue.

## Files Modified

- `routes/influencer-routes.js` - Fixed variable scope issue
- `middleware/cors.js` - Added your URL and enhanced logging
- `test-campaign-retrieval.js` - Comprehensive diagnostic test
- `run-campaign-test.js` - Simple test runner

## Test Data

The test uses the exact campaign ID and client ID from your error logs:
- Campaign ID: `Wfs5DW2kCVV0xy4tkSGQ`
- Client ID: `KVywTcxSRAS5yPXD7vMx3WkUKXw1`
- Username: `fbehery`
- Platform: `instagram`

# Preserving Prompts and Instructions

This document outlines how to preserve the carefully crafted prompts and instructions while transitioning to Firestore.

## Campaign Analysis Prompt (Phase 1)

### Current Implementation
```javascript
const phase1Prompt = `Project: Develop a Comprehensive Campaign Brief for **${input.campaign.name}**

**Overview:** We are initiating an influencer marketing campaign. Below are the campaign inputs and requirements. Your task is to synthesize this information into a structured campaign brief that will guide all subsequent phases.

**Campaign Inputs:**
${JSON.stringify(input.campaign)}

**Tasks:**
Craft the ideal influencer archetype for the campaign ensuring the influencers are of the optimal size and focus.`;
```

### Firestore Implementation
The prompt should remain exactly the same. The only change is in how the resulting campaign brief is stored:

```javascript
// Generate campaign brief using the same prompt
const campaignJSON = await processAgent(CAMPAIGN_ANALYSIS_AGENT, phase1Prompt);

// Store in Firestore instead of just writing to a JSON file
await db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId).set({
  // Campaign data fields
  name: campaignJSON.name,
  report_id: campaignId,
  // Other fields...
  
  // Store the original prompt for reference
  original_prompt: phase1Prompt,
  
  created_at: db.Timestamp.now(),
  updated_at: db.Timestamp.now()
});
```

## Seed Influencer Prompt

### Current Implementation
```javascript
const seedInfluencerPrompt = `Please provide the optimal seed influencers for the following campaign description\n\n${JSON.stringify(campaignJSON)}\n\nNEVER recommend any influencers who do not fit the campaign for this seed (e.g. musician for a swimwear ad unless they have a swimwear following somehow). Identify the dead middle PERFECT influencers for us to extrapolate from.`;
```

### Firestore Implementation
The prompt should remain exactly the same:

```javascript
// Generate seed influencers using the same prompt
const seedInfluencerPrompt = `Please provide the optimal seed influencers for the following campaign description\n\n${JSON.stringify(campaignJSON)}\n\nNEVER recommend any influencers who do not fit the campaign for this seed (e.g. musician for a swimwear ad unless they have a swimwear following somehow). Identify the dead middle PERFECT influencers for us to extrapolate from.`;
const seedInfluencersJSON = await processAgent(SEED_INFLUENCER_AGENT, seedInfluencerPrompt);

// Store in Firestore
await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('seed_influencers').doc().set({
    influencers: seedInfluencersJSON.influencers,
    original_prompt: seedInfluencerPrompt,
    created_at: db.Timestamp.now()
  });
```

## Discovery Ranking Prompt

### Current Implementation
```javascript
const discoveryPrompt = `Influencers to prioritize: [${JSON.stringify(discoveryJSON)}]\n\nCampaign to rank for: [${JSON.stringify(campaignJSON)}]`;
```

### Firestore Implementation
The prompt should remain exactly the same:

```javascript
// Generate discovery results using the same prompt
const discoveryPrompt = `Influencers to prioritize: [${JSON.stringify(discoveryJSON)}]\n\nCampaign to rank for: [${JSON.stringify(campaignJSON)}]`;
const rankedInfluencersJSON = await processAgent(DISCOVERY_AGENT, discoveryPrompt);

// Store in Firestore
await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('discovered_influencers').doc().set({
    similar_accounts: rankedInfluencersJSON.similar_accounts,
    original_prompt: discoveryPrompt,
    created_at: db.Timestamp.now()
  });
```

## Web Analysis Prompt (Phase 3)

### Current Implementation
```javascript
const phase3Prompt = `Project: Conduct Deep-Dive Background Research on Influencer **${influencerName}**

**Objective:** Perform a comprehensive web investigation on **${influencerName}** (also known as ${influencerInstaUsername}) to gather historical data, reputation insights, and any red flags. This is a due diligence report combining public information from news, social media, forums, and other sources. The goal is to assess credibility, alignment, and risk factors.

**Research Tasks & Methodology:**
1. **Profile Confirmation & Aliases:** Verify the influencer's identity and see if they have any alternate names, old handles, or common misspellings.
- Search for variations of their name: '"${influencerName} real name"', '"${influencerName} alias"', and check if the influencer has been known by other handles in the past.
- Also check if they have notable presence on other platforms under the same or different name (for example, search their Instagram handle on Twitter or YouTube).
- *Output:* Note any alias or if the name is often confused with someone else (to avoid mix-ups).
...`;
```

### Firestore Implementation
The prompt should remain exactly the same:

```javascript
// Generate web analysis using the same prompt
const phase3Prompt = `Project: Conduct Deep-Dive Background Research on Influencer **${influencerName}**

**Objective:** Perform a comprehensive web investigation on **${influencerName}** (also known as ${influencerInstaUsername}) to gather historical data, reputation insights, and any red flags. This is a due diligence report combining public information from news, social media, forums, and other sources. The goal is to assess credibility, alignment, and risk factors.

**Research Tasks & Methodology:**
1. **Profile Confirmation & Aliases:** Verify the influencer's identity and see if they have any alternate names, old handles, or common misspellings.
- Search for variations of their name: '"${influencerName} real name"', '"${influencerName} alias"', and check if the influencer has been known by other handles in the past.
- Also check if they have notable presence on other platforms under the same or different name (for example, search their Instagram handle on Twitter or YouTube).
- *Output:* Note any alias or if the name is often confused with someone else (to avoid mix-ups).
...`;

const deepDiveResultsJSON = await processAgent(WEB_ANALYSIS_AGENT, phase3Prompt);

// Store in Firestore
const webAnalysisRef = await db.collection('influencers').doc(influencerId)
  .collection('web_analysis').doc();
  
await webAnalysisRef.set({
  name: deepDiveResultsJSON.name,
  sentiment_score: deepDiveResultsJSON.sentiment_score,
  risk_level: deepDiveResultsJSON.risk_level,
  deep_dive_report: deepDiveResultsJSON.deep_dive_report,
  original_prompt: phase3Prompt,
  created_at: db.Timestamp.now(),
  updated_at: db.Timestamp.now()
});
```

## Aesthetic Analysis Prompt (Phase 5)

### Current Implementation
```javascript
const phase5Prompt = `Project: Qualitative Content and Aesthetic Analysis for **${influencerName}**

**Objective:** Evaluate **${influencerName}**'s recent content (visuals, videos, captions, overall style) to determine how well it aligns with our brand's aesthetic and messaging. Identify strengths in content style, any mismatches or red flags, and summarize the influencer's tone and visual themes. This complements the quantitative data with a creative fit assessment.

**Inputs:**
- Campaign Analysis: [${JSON.stringify(campaignJSON)}]
- Deep Dive Web Research: [${JSON.stringify(deepDiveResultsJSON)}]
- Base64 Encoded Image which should be the primary purpose of your analysis.

**Analysis Tasks:**
1. **Visual Style Assessment:**
    - Examine the provided images (provided as a base64 encoded image) for common elements: color palette, lighting, settings, composition, physical fit for the brand, subject matter, message, and anything else relevant. Sample considerations are below but are not limited to these... you will be evaluating many types of campaigns and influencers and the analysis should be detailed and clinical for the specific campaign.
...`;
```

### Firestore Implementation
The prompt should remain exactly the same:

```javascript
// Generate aesthetic analysis using the same prompt
const phase5Prompt = `Project: Qualitative Content and Aesthetic Analysis for **${influencerName}**

**Objective:** Evaluate **${influencerName}**'s recent content (visuals, videos, captions, overall style) to determine how well it aligns with our brand's aesthetic and messaging. Identify strengths in content style, any mismatches or red flags, and summarize the influencer's tone and visual themes. This complements the quantitative data with a creative fit assessment.

**Inputs:**
- Campaign Analysis: [${JSON.stringify(campaignJSON)}]
- Deep Dive Web Research: [${JSON.stringify(deepDiveResultsJSON)}]
- Base64 Encoded Image which should be the primary purpose of your analysis.

**Analysis Tasks:**
1. **Visual Style Assessment:**
    - Examine the provided images (provided as a base64 encoded image) for common elements: color palette, lighting, settings, composition, physical fit for the brand, subject matter, message, and anything else relevant. Sample considerations are below but are not limited to these... you will be evaluating many types of campaigns and influencers and the analysis should be detailed and clinical for the specific campaign.
...`;

// The rest of the aesthetic analysis code remains the same
// Only the storage mechanism changes to use Firestore
```

## ROI Analysis Prompt (Phase 6)

### Current Implementation
```javascript
const phase6Prompt = `Project: ROI and Strategic Fit Evaluation for Final Influencer Selection

Objective:
Integrate all previous findings—including quantitative metrics and qualitative analysis—for the below influencer and project the potential impact of partnering with them. You will perform a comprehensive comparative analysis that estimates ROI, projects reach, engagement, and conversion metrics, and conducts a SWOT analysis (detailing Strengths, Weaknesses, Opportunities, and Threats) for each influencer in the context of the campaign. Finally, provide a succinct, decisive recommendation regarding which influencer(s) offer the best strategic fit and return potential for the campaign (you will only have one influencer, so write to facilitate comparison).

BE HIGHLY CRITICAL AND DETAILED. Be perfectionistic and methodical in your analysis.

Inputs:

Campaign Goals & Key Metrics:
[${JSON.stringify(campaignJSON)}]
Influencer Data:
[${JSON.stringify(enrichmentJSON)}]
Influencer Web Report:
[${JSON.stringify(deepDiveResultsJSON)}]
Influencer Visual Aesthetic and Visual Red Flag Analysis:
[${JSON.stringify(aestheticAnalysisJSON)}]

Analysis Tasks:
...`;
```

### Firestore Implementation
The prompt should remain exactly the same:

```javascript
// Generate ROI analysis using the same prompt
const phase6Prompt = `Project: ROI and Strategic Fit Evaluation for Final Influencer Selection

Objective:
Integrate all previous findings—including quantitative metrics and qualitative analysis—for the below influencer and project the potential impact of partnering with them. You will perform a comprehensive comparative analysis that estimates ROI, projects reach, engagement, and conversion metrics, and conducts a SWOT analysis (detailing Strengths, Weaknesses, Opportunities, and Threats) for each influencer in the context of the campaign. Finally, provide a succinct, decisive recommendation regarding which influencer(s) offer the best strategic fit and return potential for the campaign (you will only have one influencer, so write to facilitate comparison).

BE HIGHLY CRITICAL AND DETAILED. Be perfectionistic and methodical in your analysis.

Inputs:

Campaign Goals & Key Metrics:
[${JSON.stringify(campaignJSON)}]
Influencer Data:
[${JSON.stringify(enrichmentJSON)}]
Influencer Web Report:
[${JSON.stringify(deepDiveResultsJSON)}]
Influencer Visual Aesthetic and Visual Red Flag Analysis:
[${JSON.stringify(aestheticAnalysisJSON)}]

Analysis Tasks:
...`;

const roiAnalysisJSON = await processAgent(ROI_ANALYSIS_AGENT, phase6Prompt);

// Store in Firestore
const roiAnalysisRef = await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('campaign_influencers').doc(influencerId)
  .collection('roi_analysis').doc();
  
await roiAnalysisRef.set({
  brand_fit_score: roiAnalysisJSON.brand_fit_score,
  brand_fit_description: roiAnalysisJSON.brand_fit_description,
  risk_level: roiAnalysisJSON.risk_level,
  risk_description: roiAnalysisJSON.risk_description,
  influencer_analysis: roiAnalysisJSON.influencer_analysis,
  original_prompt: phase6Prompt,
  created_at: db.Timestamp.now()
});
```

## System Prompts (Instructions)

### Phase 3 Instructions (Olivia, the Online Investigator)
The system instructions for the web analysis agent should remain exactly the same.

### Phase 5 Instructions (Ava, the Aesthetic Curator)
The system instructions for the aesthetic analysis agent should remain exactly the same.

## Conclusion

When transitioning to Firestore, all prompts and instructions should remain exactly as they are. The only changes should be in how the results are stored and retrieved. By preserving these carefully crafted prompts and instructions, we ensure that the system continues to produce the same high-quality analyses while benefiting from the improved data storage and retrieval capabilities of Firestore.

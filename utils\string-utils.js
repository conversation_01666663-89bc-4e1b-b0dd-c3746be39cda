// string-utils.js
// String manipulation utilities

/**
 * Format a number with commas
 * @param {number} num - The number to format
 * @returns {string} - The formatted number
 */
function formatNumber(num) {
  return (num != null && !isNaN(num)) ? Number(num).toLocaleString() : "";
}

/**
 * Compute engagement rate as a percentage string with two decimals
 * This is a simple deterministic mathematical calculation: (totalEngagement / followerCount) * 100
 * Not an ML-based inference or estimation
 * @param {number} totalEngagement - The total engagement
 * @param {number} followerCount - The follower count
 * @returns {string} - The engagement rate as a percentage
 */
function computeEngagementRate(totalEngagement, followerCount) {
  if (followerCount > 0) {
    return ((totalEngagement / followerCount) * 100).toFixed(2) + "%";
  }
  return "";
}

/**
 * Extract JSON from a string
 * @param {string} str - The string to extract <PERSON>SO<PERSON> from
 * @returns {Object|null} - The extracted JSON object or null if no valid JSON is found
 */
function extractJSON(str) {
  // Find the first occurrence of '{'
  const firstIndex = str.indexOf('{');
  if (firstIndex === -1) return null;

  // Use a stack approach to find the matching closing '}'
  let stack = 0;
  let endIndex = -1;
  for (let i = firstIndex; i < str.length; i++) {
    if (str[i] === '{') {
      stack++;
    } else if (str[i] === '}') {
      stack--;
      // When the stack is empty, we found the matching closing brace
      if (stack === 0) {
        endIndex = i;
        break;
      }
    }
  }

  // If no complete JSON object is found, return null
  if (endIndex === -1) return null;

  // Extract the potential JSON substring
  const jsonString = str.substring(firstIndex, endIndex + 1);
  try {
    // Parse and return the JSON object
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Invalid JSON format:", error);
    console.log(`Erroneous JSON: ${str}`);
    return null;
  }
}

export {
  formatNumber,
  computeEngagementRate,
  extractJSON
};

import testAnalysisFlow from './test-analysis-flow.js';

// Get command line arguments
const username = process.argv[2];

if (!username) {
  console.error('❌ Error: Please provide an influencer username');
  console.log('Usage: node run-analysis-test.js <username>');
  console.log('  - username: The influencer username (required)');
  console.log('\nExample: node run-analysis-test.js cristiano');
  process.exit(1);
}

console.log(`🚀 Running analysis flow test for ${username}`);

testAnalysisFlow(username)
  .then(results => {
    console.log('\n📊 ANALYSIS RESULTS SUMMARY:');
    console.log(JSON.stringify(results, null, 2));
    console.log('\n✅ Test completed successfully');
  })
  .catch(error => {
    console.error('\n❌ Test failed with error:');
    console.error(error);
  });

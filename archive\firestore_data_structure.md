# Firestore Data Structure for Influencer Analysis Platform

## Overview

This document outlines the proposed Firestore data structure for the influencer identification and analysis platform. The structure is designed to:

1. Store global influencer data that can be accessed by any client
2. Store client-specific campaign data and analyses
3. Support efficient querying and data retrieval
4. Allow for data enrichment over time

## Collections Structure

### 1. Influencers Collection

This is a global collection that stores all influencer data that is not client-specific.

```
/influencers/{influencer_id}/
```

**Fields:**
- `username`: String - The influencer's username (e.g., Instagram handle)
- `full_name`: String - The influencer's full name
- `platforms`: Map - Contains data for each platform the influencer is on
  - `instagram`: Map (if applicable)
    - `follower_count`: Number
    - `engagement_rate`: Number
    - `biography`: String
    - `profile_image_url`: String
    - `niche_class`: Array<String>
    - `posting_frequency`: Number
    - `hashtags`: Array<String>
    - `creator_follower_growth`: Map
      - `3_months_ago`: Number
      - `6_months_ago`: Number
      - `9_months_ago`: Number
      - `12_months_ago`: Number
  - `tiktok`: Map (similar structure to Instagram)
  - `youtube`: Map (similar structure to Instagram)
  - `twitter`: Map (similar structure to Instagram)
- `last_updated`: Timestamp - When the influencer data was last updated
- `created_at`: Timestamp - When the influencer was first added to the database

**Subcollections:**

#### 1.1 Web Analysis

```
/influencers/{influencer_id}/web_analysis/{analysis_id}/
```

**Fields:**
- `name`: String - Influencer's name
- `sentiment_score`: Number - Overall sentiment score
- `risk_level`: String - Risk assessment level
- `deep_dive_report`: Map
  - `aliases`: Array<String>
  - `timeline_events`: Array<Map>
    - `date`: String
    - `event`: String
  - `brand_mentions`: Array<Map>
    - `brand`: String
    - `sentiment`: String
    - `context`: String
  - `partnerships`: Array<Map>
    - `brand`: String
    - `year`: String
    - `description`: String
  - `controversies`: Array<Map>
    - `year`: String
    - `description`: String
    - `resolution`: String
  - `media_mentions`: Array<Map>
    - `source`: String
    - `date`: String
    - `title`: String
    - `url`: String
    - `sentiment`: String
- `created_at`: Timestamp
- `updated_at`: Timestamp

#### 1.2 Raw Data Storage References

```
/influencers/{influencer_id}/raw_data/{source_id}/
```

**Fields:**
- `source`: String - Data source name (e.g., "influencers_club")
- `storage_path`: String - Path to the raw JSON in Cloud Storage
- `processed_storage_path`: String - Path to the processed JSON in Cloud Storage
- `pulled_at`: Timestamp - When the data was pulled
- `processed_at`: Timestamp - When the data was processed

### 2. Clients Collection

```
/clients/{client_id}/
```

**Fields:**
- `name`: String - Client name
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `settings`: Map - Client-specific settings

**Subcollections:**

#### 2.1 Campaigns

```
/clients/{client_id}/campaigns/{campaign_id}/
```

**Fields:**
- `name`: String - Campaign name
- `report_id`: String - Unique report ID
- `product_description`: String
- `influencer_gender`: String
- `influencer_niche`: String
- `influencer_age`: String
- `influencer_personality`: String
- `influencer_aesthetic`: String
- `min_follower_count`: Number
- `max_follower_count`: Number
- `min_engagement_rate`: Number
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `status`: String - Campaign status (e.g., "draft", "active", "completed")

**Subcollections:**

##### 2.1.1 Discovered Influencers

```
/clients/{client_id}/campaigns/{campaign_id}/discovered_influencers/{discovery_id}/
```

**Fields:**
- `similar_accounts`: Array<Map>
  - `username`: String
  - `profile_url`: String
  - `engagement_percent`: Number
  - `follower_count`: Number
  - `sort_rationale`: String
- `created_at`: Timestamp

##### 2.1.2 Campaign Influencers

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/
```

**Fields:**
- `influencer_id`: String - Reference to the global influencer document
- `username`: String - Influencer username
- `status`: String - Status in the campaign (e.g., "discovered", "selected", "enriched", "approved")
- `created_at`: Timestamp
- `updated_at`: Timestamp

**Subcollections:**

###### ******* Aesthetic Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/aesthetic_analysis/{analysis_id}/
```

**Fields:**
- `name`: String - Influencer name
- `brand_fit_score`: Number
- `content_analysis`: Map
  - `visual_fit`: Number
  - `tone_fit`: String
  - `content_themes`: Array<String>
  - `image_analyses`: Array<Map>
    - `image_index`: Number
    - `description`: String
    - `colors`: String
    - `composition`: String
    - `alignment`: String
- `created_at`: Timestamp

###### ******* ROI Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/roi_analysis/{analysis_id}/
```

**Fields:**
- `brand_fit_score`: Number
- `brand_fit_description`: String
- `risk_level`: String
- `risk_description`: String
- `influencer_analysis`: Map
  - `roi_projection`: Map
    - `expected_impressions`: Number
    - `expected_engagement_rate`: Number
    - `expected_engagements`: Number
    - `roi_rating`: String
    - `roi_rationale`: String
    - `strengths`: Array<String>
    - `weaknesses`: Array<String>
- `created_at`: Timestamp

###### ******* Combined Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/combined_analysis/{analysis_id}/
```

**Fields:**
- `profileInfo`: Map
  - `metrics`: Map
    - `brandAesthetic`: String
    - `roiPotential`: String
    - `brandFitScore`: String
    - `styleAnalysis`: String
    - `vibeAlignment`: String
    - `visualAlignment`: String
  - `aestheticAnalysis`: Map
    - `keyObservations`: Array<String>
    - `verdict`: String
    - `verdictDescription`: String
- `roiProjection`: Map
  - `expectedImpressions`: String
  - `expectedEngagementRate`: String
  - `expectedEngagements`: String
  - `expectedEngagementDescription`: String
  - `roiRating`: String
  - `roiPotentialScore`: String
  - `riskAssessment`: String
  - `riskAssessmentDescription`: String
  - `audienceQuality`: String
  - `audienceQualityDescription`: String
- `created_at`: Timestamp

## Cloud Storage Structure

In addition to Firestore, we'll use Cloud Storage for storing large JSON files and media:

```
/influencers/{influencer_id}/raw_data/{source}_{timestamp}.json
/influencers/{influencer_id}/processed_data/{source}_{timestamp}.json
/influencers/{influencer_id}/images/{image_id}.jpg
/clients/{client_id}/campaigns/{campaign_id}/reports/{report_type}_{timestamp}.json
```

## Indexes and Query Patterns

### Key Indexes to Create:

1. Influencers by follower count and engagement rate
2. Influencers by niche categories
3. Campaign influencers by status
4. Campaigns by client and status

### Common Query Patterns:

1. Get all influencers for a specific niche with follower count in a range
2. Get all campaigns for a client
3. Get all influencers for a campaign with their analysis status
4. Get complete analysis for a specific influencer in a campaign
5. Get global influencer data with their web analysis

## Data Migration Strategy

1. **Initial Import**:
   - Import all existing influencer data into the global Influencers collection
   - Import all campaign data into the Clients/Campaigns structure
   - Store raw JSON files in Cloud Storage with references in Firestore

2. **Incremental Updates**:
   - When new data is pulled from Influencers Club or other sources, update the global influencer record
   - Add new analysis data to the appropriate subcollections
   - Keep track of data freshness with timestamps

## Helper Functions

Replace the combined_analyses.json and merged_analyses.json with helper functions:

```javascript
/**
 * Generates a combined analysis for an influencer in a specific campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The combined analysis
 */
async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  // Fetch all relevant analyses from Firestore
  // Combine them into a single object
  // Return the combined object
}

/**
 * Generates a merged analysis by combining the processed influencer data
 * with the combined analysis
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The merged analysis
 */
async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  // Get the combined analysis
  const combinedAnalysis = await generateCombinedAnalysis(clientId, campaignId, influencerId);
  
  // Get the processed influencer data
  const influencerData = await getProcessedInfluencerData(influencerId);
  
  // Merge the two objects
  return deepMerge(combinedAnalysis, influencerData);
}
```

## Security Rules

Implement Firestore security rules to ensure:

1. Global influencer data is readable by all authenticated clients
2. Client-specific data is only accessible to the respective client
3. Write operations are properly authenticated and authorized

Example security rules:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Global influencer data can be read by any authenticated user
    match /influencers/{influencerId} {
      allow read: if request.auth != null;
      allow write: if request.auth.token.admin == true;
      
      match /{document=**} {
        allow read: if request.auth != null;
        allow write: if request.auth.token.admin == true;
      }
    }
    
    // Client data can only be accessed by the respective client
    match /clients/{clientId} {
      allow read, write: if request.auth.token.client_id == clientId || request.auth.token.admin == true;
      
      match /{document=**} {
        allow read, write: if request.auth.token.client_id == clientId || request.auth.token.admin == true;
      }
    }
  }
}
```

## Conclusion

This Firestore data structure provides a scalable, efficient way to store and retrieve influencer data for the platform. It separates global influencer data from client-specific analyses, allowing for data reuse and enrichment over time. The structure supports all the current functionality while providing a foundation for future expansion.

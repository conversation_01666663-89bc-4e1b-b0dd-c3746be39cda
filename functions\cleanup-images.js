// cleanup-images.js
// Cloud Function to automatically purge images older than 30 days

const { Storage } = require('@google-cloud/storage');

/**
 * Cloud Function to clean up old images
 * @param {Object} event - The Cloud Pub/Sub event
 * @param {Object} context - The event context
 * @returns {Promise<string>} - A message indicating the number of files deleted
 */
exports.cleanupOldImages = async (event, context) => {
  const storage = new Storage();
  const bucketName = 'palas-run-cache';
  const bucket = storage.bucket(bucketName);
  
  // Calculate date 30 days ago
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  console.log(`Cleaning up images older than ${thirtyDaysAgo.toISOString()}`);
  
  // List all files in the image cache directories
  const [files] = await bucket.getFiles({
    prefix: 'image-cache/'
  });
  
  // Also check influencer image directories
  const [influencerFiles] = await bucket.getFiles({
    prefix: 'influencers/'
  });
  
  // Combine all files
  const allFiles = [...files, ...influencerFiles];
  
  // Filter files older than 30 days
  const oldFiles = [];
  for (const file of allFiles) {
    const [metadata] = await file.getMetadata();
    const createdTime = new Date(metadata.timeCreated);
    
    if (createdTime < thirtyDaysAgo) {
      oldFiles.push(file);
    }
  }
  
  console.log(`Found ${oldFiles.length} files to delete`);
  
  // Delete old files
  for (const file of oldFiles) {
    try {
      await file.delete();
      console.log(`Deleted ${file.name}`);
    } catch (error) {
      console.error(`Error deleting ${file.name}:`, error);
    }
  }
  
  console.log('Cleanup complete');
  return `Deleted ${oldFiles.length} files`;
};

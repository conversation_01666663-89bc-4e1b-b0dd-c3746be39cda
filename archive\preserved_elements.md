# Elements to Preserve in Firestore Implementation

This document identifies the critical elements in the current implementation that must be preserved during the transition to Firestore. These elements have been iteratively refined over time and are essential to the system's functionality.

## 1. OpenAI API Integration

### API Keys and Configuration
```javascript
const OPENAI_API_KEY = "********************************************************************************************************************************************************************";
const openaiClient = new OpenAI({ apiKey: OPENAI_API_KEY });
```

### Pricing Rates
```javascript
const pricingRates = {
  "gpt-4.5-preview": { input: 75.00, output: 150.00 },
  "gpt-4o": { input: 2.50, output: 10.00 },
  "gpt-4o-2024-05-13": { input: 5.00, output: 15.00 },
  "gpt-4o-audio-preview": { input: 2.50, output: 10.00 },
  "gpt-4o-realtime-preview": { input: 5.00, output: 20.00 },
  "gpt-4o-mini": { input: 0.15, output: 0.60 },
  "gpt-4o-mini-audio-preview": { input: 0.15, output: 0.60 },
  "gpt-4o-mini-realtime-preview": { input: 0.60, output: 2.40 },
  "o1": { input: 15.00, output: 60.00 },
  "o1-preview": { input: 15.00, output: 60.00 },
  "o3-mini": { input: 1.10, output: 4.40 },
  "o1-mini": { input: 1.10, output: 4.40 },
};
```

### Assistant IDs
```javascript
const SEED_INFLUENCER_AGENT = 'asst_lPjsRbNYLY0eR7mdW3XVSZFq';
```

## 2. Influencers Club API Integration

### API Key
```javascript
const INFLUENCERS_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMzQ0NzA4NDMwLCJpYXQiOjE3Mzk5MDg0MzAsImp0aSI6IjkxZTlmODlkZTI1NDQ3MWI5OTkzZTA3YjQ0YmVlMTI0IiwidXNlcl9pZCI6Nzg1MX0.qpbfGOObJMjLjYob_bzW7VX10SCdZpMVLxBEITIUuZc";
```

### API Endpoints
```javascript
const url = "https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/";
const url = "https://api-dashboard.influencers.club/public/v1/enrichment/lookalikes/";
```

## 3. Prompts

### Campaign Analysis Prompt
The prompt used for Phase 1 (Campaign Brief & Audience Analysis) must be preserved exactly as is:

```javascript
const phase1Prompt = `Project: Develop a Comprehensive Campaign Brief for **${input.campaign.name}**

**Overview:** We are initiating an influencer marketing campaign. Below are the campaign inputs and requirements. Your task is to synthesize this information into a structured campaign brief that will guide all subsequent phases.

**Campaign Inputs:**
${JSON.stringify(input.campaign)}

**Tasks:**
Craft the ideal influencer archetype for the campaign ensuring the influencers are of the optimal size and focus.`;
```

### Seed Influencer Prompt
```javascript
`Please provide the optimal seed influencers for the following campaign description\n\n${JSON.stringify(campaignJSON)}\n\nNEVER recommend any influencers who do not fit the campaign for this seed (e.g. musician for a swimwear ad unless they have a swimwear following somehow). Identify the dead middle PERFECT influencers for us to extrapolate from.`
```

### Discovery Ranking Prompt
```javascript
`Influencers to prioritize: [${JSON.stringify(discoveryJSON)}]\n\nCampaign to rank for: [${JSON.stringify(campaignJSON)}]`
```

### Web Analysis Prompt
The prompt used for Phase 3 (Deep-Dive Web Search & Historical Analysis) must be preserved exactly as is:

```javascript
const phase3Prompt = `Project: Conduct Deep-Dive Background Research on Influencer **${influencerName}**

**Objective:** Perform a comprehensive web investigation on **${influencerName}** (also known as ${influencerInstaUsername}) to gather historical data, reputation insights, and any red flags. This is a due diligence report combining public information from news, social media, forums, and other sources. The goal is to assess credibility, alignment, and risk factors.

**Research Tasks & Methodology:**
1. **Profile Confirmation & Aliases:** Verify the influencer's identity and see if they have any alternate names, old handles, or common misspellings.
- Search for variations of their name: '"${influencerName} real name"', '"${influencerName} alias"', and check if the influencer has been known by other handles in the past.
- Also check if they have notable presence on other platforms under the same or different name (for example, search their Instagram handle on Twitter or YouTube).
- *Output:* Note any alias or if the name is often confused with someone else (to avoid mix-ups).
...
```

### Aesthetic Analysis Prompt
The prompt used for Phase 5 (Visual & Content Analysis) must be preserved exactly as is:

```javascript
const phase5Prompt = `Project: Qualitative Content and Aesthetic Analysis for **${influencerName}**

**Objective:** Evaluate **${influencerName}**'s recent content (visuals, videos, captions, overall style) to determine how well it aligns with our brand's aesthetic and messaging. Identify strengths in content style, any mismatches or red flags, and summarize the influencer's tone and visual themes. This complements the quantitative data with a creative fit assessment.

**Inputs:**
- Campaign Analysis: [${JSON.stringify(campaignJSON)}]
- Deep Dive Web Research: [${JSON.stringify(deepDiveResultsJSON)}]
- Base64 Encoded Image which should be the primary purpose of your analysis.

**Analysis Tasks:**
1. **Visual Style Assessment:**
    - Examine the provided images (provided as a base64 encoded image) for common elements: color palette, lighting, settings, composition, physical fit for the brand, subject matter, message, and anything else relevant. Sample considerations are below but are not limited to these... you will be evaluating many types of campaigns and influencers and the analysis should be detailed and clinical for the specific campaign.
...
```

### ROI Analysis Prompt
The prompt used for Phase 6 (ROI & Strategic Fit Evaluation) must be preserved exactly as is:

```javascript
const phase6Prompt = `Project: ROI and Strategic Fit Evaluation for Final Influencer Selection

Objective:
Integrate all previous findings—including quantitative metrics and qualitative analysis—for the below influencer and project the potential impact of partnering with them. You will perform a comprehensive comparative analysis that estimates ROI, projects reach, engagement, and conversion metrics, and conducts a SWOT analysis (detailing Strengths, Weaknesses, Opportunities, and Threats) for each influencer in the context of the campaign. Finally, provide a succinct, decisive recommendation regarding which influencer(s) offer the best strategic fit and return potential for the campaign (you will only have one influencer, so write to facilitate comparison).

BE HIGHLY CRITICAL AND DETAILED. Be perfectionistic and methodical in your analysis.

Inputs:

Campaign Goals & Key Metrics:
[${JSON.stringify(campaignJSON)}]
Influencer Data:
[${JSON.stringify(enrichmentJSON)}]
Influencer Web Report:
[${JSON.stringify(deepDiveResultsJSON)}]
Influencer Visual Aesthetic and Visual Red Flag Analysis:
[${JSON.stringify(aestheticAnalysisJSON)}]

Analysis Tasks:
...
```

## 4. System Prompts (Instructions)

### Phase 3 Instructions (Olivia, the Online Investigator)
```javascript
const phase3Instructions = `AI Agent Instructions (Persona: Olivia, the Online Investigator)
Name & Role: Olivia acts as an Online Investigator AI, performing due diligence on influencers. She's akin to a digital private investigator or analyst who scrutinizes each influencer's background and reputation in detail.
Backstory: In persona lore, Olivia "cut her teeth" moderating online communities and digging through internet archives. She envisions herself as having a history in PR crisis management or journalistic research – the one people call to fact-check an individual's history. This background means she approaches each influencer like a story to uncover, remembering "case studies" of past influencer issues and successes.
...
```

### Phase 5 Instructions (Ava, the Aesthetic Curator)
```javascript
const phase5Instructions = `AI Agent Instructions (Persona: Ava, the Aesthetic Curator)
Name & Role: Ava is the AI persona serving as a Content & Aesthetic Analyst. She approaches the influencer's content like a brand creative director or cultural analyst, assessing visual style and messaging tone.
Backstory: Ava envisions herself as having a background in visual arts and social media trends. She "grew up" browsing Instagram feeds and YouTube vlogs, with a passion for photography and storytelling. Perhaps she was an art director for a fashion brand in a past life or a social media manager curating feeds – this history gives her a keen eye for detail in images and an ear for tone in language.
...
```

## 5. Helper Functions

### processAgent Function
```javascript
async function processAgent(agentID, prompt) {
  let attempt = 0;

  while (true) {
    attempt++;
    console.log(`Starting attempt ${attempt}...`);
    
    // Create a new thread and send the prompt as a user message
    const thread = await openaiClient.beta.threads.create();
    await openaiClient.beta.threads.messages.create(thread.id, {
      role: "user",
      content: prompt,
    });

    // Start the run with the specified Dreamvault assistant
    const run = await openaiClient.beta.threads.runs.create(thread.id, {
      assistant_id: agentID,
    });

    let cancelled = false;

    // Create a timeout promise that cancels the run after 90 seconds
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        // Cancel the run and mark as cancelled
        openaiClient.beta.threads.runs.cancel(thread.id, run.id).catch(() => {});
        cancelled = true;
        resolve("timeout");
      }, 120000);
    });

    // Poll for the run's completion status every 3 seconds
    const pollPromise = (async () => {
      let runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
      while (runStatus.status !== "completed" && !cancelled) {
        await new Promise((resolve) => setTimeout(resolve, 3000));
        runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
        console.log(`Run status: ${runStatus.status}`);
      }
      return "completed";
    })();

    // Race between the polling and the timeout
    const outcome = await Promise.race([timeoutPromise, pollPromise]);

    if (outcome === "completed") {
      // Retrieve messages from the thread
      const messagesResponse = await openaiClient.beta.threads.messages.list(thread.id);
      const assistantMessages = messagesResponse.data.filter(msg => msg.role === "assistant");

      if (assistantMessages.length === 0) {
        throw new Error("No assistant response found");
      }

      // Assume the last assistant message is the desired reply
      const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
      const assistantReply = lastAssistantMessage.content[0].text.value;

      // Attempt to parse the assistant's reply as JSON
      let parsedReply;
      try {
        parsedReply = JSON.parse(assistantReply);
        console.log("Parsed reply:", JSON.stringify(parsedReply));
      } catch (error) {
        console.error("Error parsing JSON from assistant:", error);
        throw new Error("Assistant responded with invalid JSON");
      }

      return parsedReply;
    } else {
      console.log(`Attempt ${attempt} timed out. Resubmitting the call to OpenAI...`);
      // Loop will retry by creating a new thread and resubmitting the prompt.
    }
  }
}
```

### processInstagramImagePosts Function
```javascript
async function processInstagramImagePosts(data) {
  // Validate input structure
  if (!data.instagram || !Array.isArray(data.instagram.post_data)) {
    throw new Error('Invalid input: Expected data.instagram.post_data to be an array');
  }

  const imageUrls = [];

  // Append the HD profile picture as the first image, if available
  if (data.instagram.profile_picture_hd) {
    imageUrls.push(data.instagram.profile_picture_hd);
    console.log(`Profile Picture Added: ${data.instagram.profile_picture_hd}`);
  }

  // Process each post: get the first image from each post's media array
  data.instagram.post_data.forEach(post => {
    if (Array.isArray(post.media)) {
      const firstImageItem = post.media.find(mediaItem => mediaItem.type === 'image' && mediaItem.url);
      if (firstImageItem) {
        imageUrls.push(firstImageItem.url);
        console.log(`Image Found: ${firstImageItem.url}`);
      }
    }
  });

  // If no images are found, return an empty string.
  if (imageUrls.length === 0) {
    console.log("No image posts found. Returning an empty string.");
    return '';
  }

  // Download all images concurrently
  const imageBuffers = await Promise.all(imageUrls.map(url => downloadImage(url)));

  // Merge the images vertically using join-images
  const mergedImage = await joinImages(imageBuffers, { direction: 'vertical' });
  
  // Convert the merged image to JPEG format and get the buffer
  const mergedBuffer = await mergedImage.toFormat('jpeg').toBuffer();
  const base64Image = mergedBuffer.toString('base64');
  
  return base64Image;
}
```

### extractInfluencerProfileDirect Function
```javascript
async function extractInfluencerProfileDirect(data) {
  // Helper function to format numbers with commas
  const formatNumber = num => (num != null && !isNaN(num)) ? Number(num).toLocaleString() : "";

  // Helper function to compute engagement rate as a percentage string with two decimals
  const computeEngagementRate = (totalEngagement, followerCount) => {
    if (followerCount > 0) {
      return ((totalEngagement / followerCount) * 100).toFixed(2) + "%";
    }
    return "";
  };

  // -- Basic Fields (from root) --
  const email = data.email || "";
  const location = data.location || "";
  const speakingLanguage = data.speaking_language || "";
  const firstName = data.first_name || "";
  const profilePicture = await cacheImage(data.instagram.profile_picture_hd) || "";
  const hasBrandDeals = data.has_brand_deals || false;
  const hasLinkInBio = data.has_link_in_bio || false;
  const isBusiness = data.is_business || false;
  const isCreator = data.is_creator || false;
  const postData = data.post_data || {};
  const influencerCategories = data.instagram.niche_class.join(' - ') || "";

  // -- Instagram Data Extraction --
  const insta = data.instagram || {};
  const instaUsername = insta.username || "";
  const instaFollowerCount = insta.follower_count != null ? insta.follower_count : 0;
  const instaBio = insta.biography || "";
  const instaEngagementPercent = insta.engagement_percent != null ? insta.engagement_percent : 0;
  const instaLatestPost = insta.latest_post || {};
  const instaPostEngagement = instaLatestPost.engagement || {};
  const instaPostMedia = Array.isArray(instaLatestPost.media) ? instaLatestPost.media : [];

  // -- YouTube Data Extraction --
  const yt = data.youtube || {};
  const ytSubscriberCount = yt.subscriber_count != null ? yt.subscriber_count : 0;
  const ytLatestVideo = yt.latest_video || {};
  const ytVideoViews = ytLatestVideo.views != null ? ytLatestVideo.views : 0;
  const ytVideoLikes = ytLatestVideo.likes != null ? ytLatestVideo.likes : 0;
  const ytVideoComments = ytLatestVideo.comments != null ? ytLatestVideo.comments : 0;

  // -- TikTok Data Extraction --
  const tt = data.tiktok || {};
  const ttUsername = tt.username || "";
  const ttFollowerCount = tt.follower_count != null ? tt.follower_count : 0;
  const ttLatestPost = tt.latest_post || {};
  const ttPostMedia = ttLatestPost.media || {};
  const ttPostEngagement = ttLatestPost.engagement || {};

  // -- Creator Has --
  const creatorHas = Array.isArray(data.creator_has) ? data.creator_has : [];

  // -- Assemble profileInfo --
  const profileInfo = {
    username: instaUsername,
    profileImageUrl: profilePicture,
    category: influencerCategories,
    bio: instaBio
  };

  // -- Assemble Influencer Stats --
  const platforms = [];
  
  // Instagram platform: use provided engagement percent
  platforms.push({
    name: "Instagram",
    followers: formatNumber(instaFollowerCount),
    engagement: instaEngagementPercent != null ? `${instaEngagementPercent}%` : ""
  });

  // YouTube platform: compute engagement rate if video exists
  if (ytLatestVideo.video_id) {
    const ytTotalEngagement = (ytVideoLikes || 0) + (ytVideoComments || 0);
    platforms.push({
      name: "YouTube",
      followers: formatNumber(ytSubscriberCount),
      engagement: ytSubscriberCount > 0 ? computeEngagementRate(ytTotalEngagement, ytSubscriberCount) : ""
    });
  }

  // TikTok platform: compute engagement rate using likes, comments, and shares
  if (ttLatestPost.post_id) {
    const ttTotalEngagement = (ttPostEngagement.likes || 0) + (ttPostEngagement.comments || 0) + (ttPostEngagement.shares || 0);
    platforms.push({
      name: "TikTok",
      followers: formatNumber(ttFollowerCount),
      engagement: ttFollowerCount > 0 ? computeEngagementRate(ttTotalEngagement, ttFollowerCount) : ""
    });
  }

  // Engagement Rate object (using Instagram metric)
  const engagementRate = {
    value: instaEngagementPercent != null ? `${instaEngagementPercent}%` : "",
    description: "Instagram engagement rate."
  };

  // Content Types: Extract unique media types from all Instagram posts.
  const contentTypesSet = new Set();
  if (Array.isArray(insta.post_data)) {
    insta.post_data.forEach(post => {
      if (Array.isArray(post.media)) {
        post.media.forEach(mediaItem => {
          if (mediaItem.type) {
            contentTypesSet.add(mediaItem.type);
          }
        });
      }
    });
  }
  const contentTypes = Array.from(contentTypesSet);

  const influencerStats = {
    platforms,
    engagementRate,
    contentTypes
  };

  // -- Recent Posts --
  const recentPosts = Array.isArray(insta.post_data)
    ? await Promise.all(insta.post_data.map(async post => {
        let mediaUrl = "";
        if (Array.isArray(post.media) && post.media.length > 0) {
          const firstMedia = post.media[0];
          if (firstMedia.type === "image") {
            mediaUrl = firstMedia.url;
          }
        }
        return {
          id: parseInt(post.post_id, 10) || 0,
          imageUrl: mediaUrl ? await cacheImage(mediaUrl) : "",
          likes: post.engagement && post.engagement.likes != null ? formatNumber(post.engagement.likes) : "0",
          comments: post.engagement && post.engagement.comments != null ? formatNumber(post.engagement.comments) : "0"
        };
      }))
    : [];

  // -- Return the assembled influencer profile --
  return {
    profileInfo,
    influencerStats,
    recentPosts
  };
}
```

## 6. API Endpoints and CORS Configuration

### CORS Configuration
```javascript
// CORS configuration: allow any origin that ends with "lovableproject.com"
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl)
    if (!origin) return callback(null, true);
    if (origin.endsWith("lovableproject.com") || origin.endsWith("lovable.app")) {
      return callback(null, true);
    } else {
      return callback(new Error("Not allowed by CORS"));
    }
  },
};

// Apply CORS middleware for all routes and explicitly handle preflight requests
app.use(cors(corsOptions));
app.options("*", cors(corsOptions));
app.use(bodyParser.json());
```

## 7. Error Handling and Retry Logic

### Retry Logic for Influencers Club API
```javascript
if (error.response && error.response.status === 500 && attempt < 10) {
  console.warn(`Retrying updateInfluencerFullReport (attempt ${attempt + 1}/10) after 1 second delay...`);
  await new Promise(resolve => setTimeout(resolve, 1000));
  return updateInfluencerFullReport(username, platform, requestPayload, attempt + 1);
}
```

### Retry Logic for Aesthetic Analysis
```javascript
let aestheticAnalysisJSON = null;
for (let attempt = 1; attempt <= 10; attempt++) {
  const contentAnalysisResults = await openaiClient.beta.chat.completions.parse({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: phase5Instructions },
      { 
        role: "user", 
        content: [
          { type: "text", text: phase5Prompt },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${influencerImagesBase64}` } }
        ]
      }
    ],
    max_completion_tokens: 16000,
  });

  const contentString = contentAnalysisResults.choices[0].message.content;
  aestheticAnalysisJSON = extractJSON(contentString);

  if (aestheticAnalysisJSON !== null) {
    console.log(`Success on attempt ${attempt}`);
    break;
  }
  console.log(`Attempt ${attempt} returned null. Retrying...`);
}
```

## 8. Utility Functions

### extractJSON Function
```javascript
function extractJSON(str) {
  // Find the first occurrence of '{'
  const firstIndex = str.indexOf('{');
  if (firstIndex === -1) return null;

  // Use a stack approach to find the matching closing '}'
  let stack = 0;
  let endIndex = -1;
  for (let i = firstIndex; i < str.length; i++) {
    if (str[i] === '{') {
      stack++;
    } else if (str[i] === '}') {
      stack--;
      // When the stack is empty, we found the matching closing brace
      if (stack === 0) {
        endIndex = i;
        break;
      }
    }
  }

  // If no complete JSON object is found, return null
  if (endIndex === -1) return null;

  // Extract the potential JSON substring
  const jsonString = str.substring(firstIndex, endIndex + 1);
  try {
    // Parse and return the JSON object
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Invalid JSON format:", error);
    console.log(`Erroneous JSON: ${str}`)
    return null;
  }
}
```

### deepMerge Function
```javascript
function deepMerge(target, source) {
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      // If both values are objects (and not arrays), merge them recursively
      if (
        target[key] &&
        typeof target[key] === "object" &&
        !Array.isArray(target[key]) &&
        typeof source[key] === "object" &&
        !Array.isArray(source[key])
      ) {
        target[key] = deepMerge({ ...target[key] }, source[key]);
      } else {
        // Otherwise, assign the source value to the target
        target[key] = source[key];
      }
    }
  }
  return target;
}
```

## Implementation Strategy

When implementing the Firestore database structure, these elements must be preserved exactly as they are. The implementation should:

1. Keep all prompts and instructions unchanged
2. Maintain the same API keys and endpoints
3. Preserve the helper functions' logic and behavior
4. Keep the error handling and retry logic intact
5. Ensure the utility functions work the same way

Any changes to the system should be focused solely on:
1. Changing where data is stored (from JSON files to Firestore)
2. Updating how data is retrieved (from reading JSON files to querying Firestore)
3. Adding new functionality that doesn't interfere with existing elements

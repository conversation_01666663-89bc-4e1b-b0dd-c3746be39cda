# Preserving OpenAI API Integration

This document outlines how to preserve the OpenAI API integration while transitioning to Firestore.

## Current OpenAI Integration

The current implementation uses OpenAI's API for several critical functions:

1. Campaign analysis (Phase 1)
2. Influencer discovery (Phase 2)
3. Web analysis (Phase 3)
4. Aesthetic analysis (Phase 5)
5. ROI analysis (Phase 6)

## Elements to Preserve

### 1. API Keys and Configuration

```javascript
const OPENAI_API_KEY = "********************************************************************************************************************************************************************";
const openaiClient = new OpenAI({ apiKey: OPENAI_API_KEY });
```

### 2. Assistant IDs

```javascript
const CAMPAIGN_ANALYSIS_AGENT = 'asst_8z7v7LffCsuh2Ez9tBtStYbg';
const DISCOVERY_AGENT = 'asst_we8mAAj5oluautOVautmiJqn';
const SEED_INFLUENCER_AGENT = 'asst_lPjsRbNYLY0eR7mdW3XVSZFq';
```

### 3. processAgent Function

```javascript
async function processAgent(agentID, prompt) {
  let attempt = 0;

  while (true) {
    attempt++;
    console.log(`Starting attempt ${attempt}...`);
    
    // Create a new thread and send the prompt as a user message
    const thread = await openaiClient.beta.threads.create();
    await openaiClient.beta.threads.messages.create(thread.id, {
      role: "user",
      content: prompt,
    });

    // Start the run with the specified assistant
    const run = await openaiClient.beta.threads.runs.create(thread.id, {
      assistant_id: agentID,
    });

    let cancelled = false;

    // Create a timeout promise that cancels the run after 120 seconds
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        // Cancel the run and mark as cancelled
        openaiClient.beta.threads.runs.cancel(thread.id, run.id).catch(() => {});
        cancelled = true;
        resolve("timeout");
      }, 120000);
    });

    // Poll for the run's completion status every 3 seconds
    const pollPromise = (async () => {
      let runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
      while (runStatus.status !== "completed" && !cancelled) {
        await new Promise((resolve) => setTimeout(resolve, 3000));
        runStatus = await openaiClient.beta.threads.runs.retrieve(thread.id, run.id);
        console.log(`Run status: ${runStatus.status}`);
      }
      return "completed";
    })();

    // Race between the polling and the timeout
    const outcome = await Promise.race([timeoutPromise, pollPromise]);

    if (outcome === "completed") {
      // Retrieve messages from the thread
      const messagesResponse = await openaiClient.beta.threads.messages.list(thread.id);
      const assistantMessages = messagesResponse.data.filter(msg => msg.role === "assistant");

      if (assistantMessages.length === 0) {
        throw new Error("No assistant response found");
      }

      // Assume the last assistant message is the desired reply
      const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
      const assistantReply = lastAssistantMessage.content[0].text.value;

      // Attempt to parse the assistant's reply as JSON
      let parsedReply;
      try {
        parsedReply = JSON.parse(assistantReply);
        console.log("Parsed reply:", JSON.stringify(parsedReply));
      } catch (error) {
        console.error("Error parsing JSON from assistant:", error);
        throw new Error("Assistant responded with invalid JSON");
      }

      return parsedReply;
    } else {
      console.log(`Attempt ${attempt} timed out. Resubmitting the call to OpenAI...`);
      // Loop will retry by creating a new thread and resubmitting the prompt.
    }
  }
}
```

## Integration with Firestore

When transitioning to Firestore, the OpenAI integration should remain unchanged. The only changes should be in how the results are stored and retrieved:

### 1. Campaign Analysis

**Current Implementation:**
```javascript
const campaignJSON = await processAgent(CAMPAIGN_ANALYSIS_AGENT, phase1Prompt);
await writeJsonToBucket(`/runs/${report_id}_campaign_analysis.json`, campaignJSON);
```

**Firestore Implementation:**
```javascript
const campaignJSON = await processAgent(CAMPAIGN_ANALYSIS_AGENT, phase1Prompt);

// Store in Firestore
await db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId).set({
  name: campaignJSON.name,
  report_id: campaignId,
  product_description: campaignJSON.product_description,
  influencer_gender: campaignJSON.influencer_gender,
  influencer_niche: campaignJSON.influencer_niche,
  influencer_age: campaignJSON.influencer_age,
  influencer_personality: campaignJSON.influencer_personality,
  influencer_aesthetic: campaignJSON.influencer_aesthetic,
  min_follower_count: campaignJSON.min_follower_count,
  max_follower_count: campaignJSON.max_follower_count,
  min_engagement_rate: campaignJSON.min_engagement_rate,
  created_at: db.Timestamp.now(),
  updated_at: db.Timestamp.now(),
  status: "active"
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/campaign_analysis.json`, campaignJSON);
```

### 2. Influencer Discovery

**Current Implementation:**
```javascript
const discoveryJSON = await processAgent(DISCOVERY_AGENT, discoveryPrompt);
await writeJsonToBucket(`/runs/${report_id}_discovery_results.json`, discoveryJSON);
```

**Firestore Implementation:**
```javascript
const discoveryJSON = await processAgent(DISCOVERY_AGENT, discoveryPrompt);

// Store in Firestore
const discoveryRef = await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('discovered_influencers').doc();
  
await discoveryRef.set({
  similar_accounts: discoveryJSON.similar_accounts,
  created_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/discovery_results.json`, discoveryJSON);
```

### 3. Web Analysis

**Current Implementation:**
```javascript
const deepDiveResultsJSON = await processAgent(WEB_ANALYSIS_AGENT, phase3Prompt);
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_web_analysis.json`, deepDiveResultsJSON);
```

**Firestore Implementation:**
```javascript
const deepDiveResultsJSON = await processAgent(WEB_ANALYSIS_AGENT, phase3Prompt);

// Store in Firestore
const webAnalysisRef = await db.collection('influencers').doc(influencerId)
  .collection('web_analysis').doc();
  
await webAnalysisRef.set({
  name: deepDiveResultsJSON.name,
  sentiment_score: deepDiveResultsJSON.sentiment_score,
  risk_level: deepDiveResultsJSON.risk_level,
  deep_dive_report: deepDiveResultsJSON.deep_dive_report,
  created_at: db.Timestamp.now(),
  updated_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/influencers/${influencerId}/web_analysis/${webAnalysisRef.id}.json`, deepDiveResultsJSON);
```

### 4. Aesthetic Analysis

**Current Implementation:**
```javascript
let aestheticAnalysisJSON = null;
for (let attempt = 1; attempt <= 10; attempt++) {
  const contentAnalysisResults = await openaiClient.beta.chat.completions.parse({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: phase5Instructions },
      { 
        role: "user", 
        content: [
          { type: "text", text: phase5Prompt },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${influencerImagesBase64}` } }
        ]
      }
    ],
    max_completion_tokens: 16000,
  });

  const contentString = contentAnalysisResults.choices[0].message.content;
  aestheticAnalysisJSON = extractJSON(contentString);

  if (aestheticAnalysisJSON !== null) {
    console.log(`Success on attempt ${attempt}`);
    break;
  }
  console.log(`Attempt ${attempt} returned null. Retrying...`);
}

await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_aesthetic_analysis.json`, aestheticAnalysisJSON);
```

**Firestore Implementation:**
```javascript
let aestheticAnalysisJSON = null;
for (let attempt = 1; attempt <= 10; attempt++) {
  const contentAnalysisResults = await openaiClient.beta.chat.completions.parse({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: phase5Instructions },
      { 
        role: "user", 
        content: [
          { type: "text", text: phase5Prompt },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${influencerImagesBase64}` } }
        ]
      }
    ],
    max_completion_tokens: 16000,
  });

  const contentString = contentAnalysisResults.choices[0].message.content;
  aestheticAnalysisJSON = extractJSON(contentString);

  if (aestheticAnalysisJSON !== null) {
    console.log(`Success on attempt ${attempt}`);
    break;
  }
  console.log(`Attempt ${attempt} returned null. Retrying...`);
}

// Store in Firestore
const aestheticAnalysisRef = await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('campaign_influencers').doc(influencerId)
  .collection('aesthetic_analysis').doc();
  
await aestheticAnalysisRef.set({
  name: aestheticAnalysisJSON.name,
  brand_fit_score: aestheticAnalysisJSON.brand_fit_score,
  content_analysis: aestheticAnalysisJSON.content_analysis,
  created_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/aesthetic_analysis.json`, aestheticAnalysisJSON);
```

## Conclusion

The OpenAI API integration should remain unchanged during the transition to Firestore. The only changes should be in how the results are stored and retrieved. By preserving the existing OpenAI integration, we ensure that the system continues to function as expected while benefiting from the improved data storage and retrieval capabilities of Firestore.

# Influencer Analysis Platform - Firestore Implementation Plan

## Overview

This document outlines the detailed plan for transitioning the influencer analysis platform from using JSON files to a Firestore database structure. The implementation will include:

1. A global Influencers collection for storing influencer data accessible to all clients
2. Client-specific Campaign collections for storing campaign-related data and analyses
3. Connector classes for data providers (starting with Influencers Club)
4. Helper functions to replace the combined_analyses.json and merged_analyses.json files

## Current System Flow

The current system follows this flow:

1. User submits a form → processed into campaign_analysis.json
2. System identifies influencers → creates influencer_discovery.json
3. User selects influencers to enrich → system performs:
   - Data pull from Influencers Club → raw_influencers_club_pull.json → processed_influencers_club_pull.json
   - Web search analysis → web_analysis.json
   - Visual/aesthetic analysis → aesthetic_analysis.json
   - ROI analysis → roi_analysis.json
   - Combines all analyses → combined_analyses.json
   - Merges with processed data → merged_analyses.json

## Firestore Data Structure

### 1. Influencers Collection (Global)

```
/influencers/{influencer_id}/
```

**Fields:**
- `username`: String - The influencer's username
- `full_name`: String - The influencer's full name
- `email`: String (if available)
- `location`: String (if available)
- `speaking_language`: String (if available)
- `has_brand_deals`: Boolean
- `has_link_in_bio`: Boolean
- `is_business`: Boolean
- `is_creator`: Boolean
- `platforms`: Map - Contains data for each platform
  - `instagram`: Map (if applicable)
    - `username`: String
    - `follower_count`: Number
    - `biography`: String
    - `engagement_percent`: Number
    - `niches`: Map
      - `primary`: String
      - `secondary`: Array<String>
    - `hashtags`: Array<String>
    - `posting_frequency_recent_months`: Number
    - `follower_growth`: Map
      - `three_months_ago`: Number
      - `six_months_ago`: Number
      - `nine_months_ago`: Number
      - `twelve_months_ago`: Number
    - `profile_image_url`: String
  - `tiktok`: Map (similar structure)
  - `youtube`: Map (similar structure)
  - `twitter`: Map (similar structure)
  - Other platforms as needed
- `creator_has`: Array<Map> - List of all platforms the creator has
- `last_updated`: Timestamp
- `created_at`: Timestamp

**Subcollections:**

#### 1.1 Web Analysis

```
/influencers/{influencer_id}/web_analysis/{analysis_id}/
```

**Fields:**
- `name`: String
- `sentiment_score`: Number
- `risk_level`: String
- `deep_dive_report`: Map
  - `aliases`: Array<String>
  - `timeline_events`: Array<Map>
  - `brand_mentions`: Array<Map>
  - `partnerships`: Array<Map>
  - `controversies`: Array<Map>
  - `media_mentions`: Array<Map>
- `created_at`: Timestamp
- `updated_at`: Timestamp

#### 1.2 Raw Data Storage References

```
/influencers/{influencer_id}/raw_data/{source_id}/
```

**Fields:**
- `source`: String (e.g., "influencers_club")
- `storage_path`: String - Path to raw JSON in Cloud Storage
- `processed_storage_path`: String - Path to processed JSON
- `pulled_at`: Timestamp
- `processed_at`: Timestamp

### 2. Clients Collection

```
/clients/{client_id}/
```

**Fields:**
- `name`: String
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `settings`: Map

**Subcollections:**

#### 2.1 Campaigns

```
/clients/{client_id}/campaigns/{campaign_id}/
```

**Fields:**
- `name`: String
- `report_id`: String
- `product_description`: String
- `influencer_gender`: String
- `influencer_niche`: String
- `influencer_age`: String
- `influencer_personality`: String
- `influencer_aesthetic`: String
- `min_follower_count`: Number
- `max_follower_count`: Number
- `min_engagement_rate`: Number
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `status`: String

**Subcollections:**

##### 2.1.1 Discovered Influencers

```
/clients/{client_id}/campaigns/{campaign_id}/discovered_influencers/{discovery_id}/
```

**Fields:**
- `similar_accounts`: Array<Map>
  - `username`: String
  - `profile_url`: String
  - `engagement_percent`: Number
  - `follower_count`: Number
  - `sort_rationale`: String
- `created_at`: Timestamp

##### 2.1.2 Campaign Influencers

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/
```

**Fields:**
- `influencer_id`: String - Reference to global influencer
- `username`: String
- `status`: String (e.g., "discovered", "selected", "enriched")
- `created_at`: Timestamp
- `updated_at`: Timestamp

**Subcollections:**

###### 2.1.2.1 Aesthetic Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/aesthetic_analysis/{analysis_id}/
```

**Fields:**
- `name`: String
- `brand_fit_score`: Number
- `content_analysis`: Map
  - `visual_fit`: Number
  - `tone_fit`: String
  - `content_themes`: Array<String>
  - `image_analyses`: Array<Map>
- `created_at`: Timestamp

###### ******* ROI Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/roi_analysis/{analysis_id}/
```

**Fields:**
- `brand_fit_score`: Number
- `brand_fit_description`: String
- `risk_level`: String
- `risk_description`: String
- `influencer_analysis`: Map
  - `roi_projection`: Map
    - `expected_impressions`: Number
    - `expected_engagement_rate`: Number
    - `expected_engagements`: Number
    - `roi_rating`: String
    - `roi_rationale`: String
    - `strengths`: Array<String>
    - `weaknesses`: Array<String>
- `created_at`: Timestamp

## Cloud Storage Structure

```
/influencers/{influencer_id}/raw_data/{source}_{timestamp}.json
/influencers/{influencer_id}/processed_data/{source}_{timestamp}.json
/influencers/{influencer_id}/images/{image_id}.jpg
/clients/{client_id}/campaigns/{campaign_id}/reports/{report_type}_{timestamp}.json
```

## Implementation Components

### 1. Influencers Club Connector

The Influencers Club connector needs to be enhanced to capture ALL relevant data from the API:

```javascript
// Pseudocode for enhanced connector
class InfluencersClubConnector {
  constructor(apiKey, bucketName) {
    // Initialize with API key and storage bucket
  }
  
  async fetchInfluencerData(username, platform = "instagram", options = {}) {
    // Set default options
    const defaultOptions = {
      filterKey: "username",
      emailRequired: "must_have",
      postDataRequired: true
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    // Validate platform is supported
    // Supported platforms: instagram, youtube, tiktok, onlyfans, twitter, twitch, discord, facebook, snapchat, pinterest
    
    // Make API request to /public/v1/enrichment/single_enrich/
    // Handle errors and return data
  }
  
  async processRawData(rawData) {
    // Create standardized format object
    const processed = {
      profileInfo: { /* basic info */ },
      influencerStats: { /* stats across platforms */ },
      audienceInsights: { /* demographic data */ },
      recentPosts: [],
      brandMentions: [],
      partnerships: []
    };
    
    // Process data for each platform (Instagram, YouTube, TikTok, Twitter, etc.)
    // For Instagram:
    if (rawData.instagram) {
      // Extract username, follower_count, biography, engagement_percent
      // Extract niches (primary and secondary)
      // Extract hashtags
      // Extract posting frequency
      // Extract follower growth trends
      // Process latest posts if available
      // Extract audience demographics if available
    }
    
    // Similar processing for other platforms
    
    // Extract brand mentions and partnerships
    
    return processed;
  }
  
  async cacheImage(imageUrl, filename) {
    // Download and store image in Cloud Storage
    // Return public URL
  }
  
  async enrichInfluencer(username, platform = "instagram") {
    // Check if influencer exists in Firestore
    // Fetch raw data from API
    // Store raw data in Cloud Storage
    // Process raw data
    // Store processed data in Cloud Storage
    // Update Firestore with latest data
    // Return processed data and influencer ID
  }
}
```

### 2. Analysis Generator Functions

Replace the combined_analyses.json and merged_analyses.json with helper functions:

```javascript
// Pseudocode for analysis generators
async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  // Get the campaign influencer document
  // Get the aesthetic analysis
  // Get the ROI analysis
  // Get the web analysis
  // Get the global influencer data
  
  // Combine all data into a structured object
  const combinedAnalysis = {
    profileInfo: { /* metrics and aesthetic analysis */ },
    roiProjection: { /* ROI data */ },
    webAnalysis: { /* Web analysis data */ },
    brandFit: { /* Brand fit data */ }
  };
  
  return combinedAnalysis;
}

async function getProcessedInfluencerData(influencerId) {
  // Get the latest processed data reference
  // Get the processed data from Cloud Storage
  // Return the processed data
}

async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  // Get the combined analysis
  // Get the processed influencer data
  // Merge the two objects
  // Return the merged object
}
```

### 3. Campaign Analysis Flow

The campaign analysis flow needs to be updated to use Firestore:

```javascript
// Pseudocode for campaign analysis flow
async function createCampaign(campaignData, clientId) {
  // Create a new campaign document in Firestore
  // Process campaign data with AI
  // Store campaign analysis in Firestore
  // Return campaign ID and analysis
}

async function discoverInfluencers(campaignId, clientId) {
  // Get campaign data from Firestore
  // Use AI to identify potential influencers
  // Store discovered influencers in Firestore
  // Return list of discovered influencers
}

async function enrichInfluencer(influencerUsername, campaignId, clientId) {
  // Check if influencer exists in global collection
  // If not, create new influencer document
  
  // Enrich with Influencers Club data
  const connector = new InfluencersClubConnector(apiKey, bucketName);
  const { influencerId, processedData } = await connector.enrichInfluencer(influencerUsername);
  
  // Add influencer to campaign
  // Create campaign influencer document
  
  // Perform web analysis
  // Store web analysis in global influencer document
  
  // Perform aesthetic analysis
  // Store aesthetic analysis in campaign influencer document
  
  // Perform ROI analysis
  // Store ROI analysis in campaign influencer document
  
  // Generate combined and merged analyses on-demand
  // Return merged analysis
}
```

### 4. API Endpoints

Update the API endpoints to use Firestore:

```javascript
// Pseudocode for API endpoints
app.post('/api/campaigns', async (req, res) => {
  // Create a new campaign
  const { campaignData, clientId } = req.body;
  const result = await createCampaign(campaignData, clientId);
  res.status(200).json(result);
});

app.post('/api/campaigns/:campaignId/discover', async (req, res) => {
  // Discover influencers for a campaign
  const { campaignId } = req.params;
  const { clientId } = req.body;
  const result = await discoverInfluencers(campaignId, clientId);
  res.status(200).json(result);
});

app.post('/api/campaigns/:campaignId/enrich', async (req, res) => {
  // Enrich an influencer for a campaign
  const { campaignId } = req.params;
  const { influencerUsername, clientId } = req.body;
  const result = await enrichInfluencer(influencerUsername, campaignId, clientId);
  res.status(200).json(result);
});

app.get('/api/campaigns/:campaignId/influencers/:influencerId', async (req, res) => {
  // Get merged analysis for an influencer in a campaign
  const { campaignId, influencerId } = req.params;
  const { clientId } = req.query;
  const result = await generateMergedAnalysis(clientId, campaignId, influencerId);
  res.status(200).json(result);
});
```

## Influencers Club API Data Capture

Based on the documentation, we need to ensure our connector captures ALL relevant data from the Influencers Club API:

### General Information
- Email (if available)
- Location (city, country)
- Speaking Language
- First Name
- Business Account Status
- Creator Status
- Has Brand Deals
- Has Link in Bio

### Instagram Insights
- Username
- Follower Count
- Biography
- Engagement Rate
- Primary & Sub Niche Classification
- Recent Posting Frequency
- Follower Growth Trends
- Latest Post Data (caption, engagement, media type, hashtags)
- Geotagged Location
- Tagged Users in Posts

### YouTube Insights
- Channel Title & Description
- Subscriber Count
- Average Video Views
- Monetization Status
- Engagement Rate
- Posting Frequency
- Follower Growth Trends
- Recent Video Data

### TikTok Insights
- Username
- Follower Count
- Biography
- Category
- Average Likes Per Video
- Engagement Rate
- Posting Frequency
- Follower Growth Trends
- Recent Video Data
- Has TikTok Shop

### Platform Presence
- Creator Platform List (all social media platforms)

## Migration Strategy

1. **Setup Firestore Database**
   - Create the collections and document structure
   - Set up security rules

2. **Implement Connectors**
   - Create the enhanced Influencers Club connector
   - Ensure it captures ALL relevant data

3. **Implement Helper Functions**
   - Create the analysis generator functions
   - Test with sample data

4. **Update API Endpoints**
   - Modify existing endpoints to use Firestore
   - Add new endpoints as needed

5. **Data Migration**
   - Import existing data into Firestore
   - Validate data integrity

6. **Testing**
   - Test each component individually
   - Test the entire flow end-to-end

## Conclusion

This implementation plan provides a detailed roadmap for transitioning the influencer analysis platform from JSON files to a Firestore database. The plan ensures that all relevant data is captured and stored in a structured, efficient manner, while maintaining the existing functionality of the platform.

The use of Firestore will provide several benefits:
- Scalable, real-time database
- Automatic synchronization across clients
- Improved query capabilities
- Better security and access control
- Reduced storage overhead

By implementing this plan, the platform will be better positioned for future growth and feature enhancements.

OpenAI API: GPT-4.1 Model Overview
📘 Documentation Access
Docs: platform.openai.com/docs

API Reference: platform.openai.com/docs/api-reference/introduction

Signup: platform.openai.com/signup

📖 Core Concepts
The API and documentation cover several foundational areas for developers and users:

Text and prompting

Images and vision

Audio and speech

Structured outputs

Function calling

Conversation state

Streaming

File inputs

Reasoning

Evals

🧰 Built-In Tools
The platform includes multiple integrated tools:

Web search

Search K

Cookbook: cookbook.openai.com

Forum: community.openai.com/categories

Help Center

🤖 GPT-4.1 — Flagship Model for Complex Tasks
Intelligence: Higher
GPT-4.1 is optimized for challenging problem-solving across diverse domains, making it ideal for advanced use cases that demand reasoning, analysis, and nuanced language handling.

Speed: Medium
Performance is balanced to ensure both depth of output and responsiveness.

Input Modalities:
Text

Image

Output Modality:
Text

📏 Model Limits
Context Window: 1,047,576 tokens
(This likely reflects the total token capacity for archived context—specific to certain tools or workflows.)

Maximum Output Tokens: 32,768 tokens
(This is the hard limit for a single generation response.)

Knowledge Cutoff: May 31, 2024

💲 Pricing Details
Prices are based on token usage, and additional tool-specific fees may apply (e.g., for search or computer tools).


Model	Input per 1M Tokens	Cached Input	Output per 1M Tokens
GPT-4.1	$2.00	$0.50	$8.00
GPT-4o	$2.50	N/A	N/A
o3-mini	$1.10	N/A	N/A
🔹 "Cached input" refers to reuse of previously submitted inputs, which are billed at a discounted rate.

🔹 Pricing varies by model and is listed in detail on the official Pricing Page.
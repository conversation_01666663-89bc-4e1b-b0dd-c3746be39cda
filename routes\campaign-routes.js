// campaign-routes.js
// Campaign-related routes

import express from 'express';
import { createCampaign, getCampaign, updateCampaign, generateCampaignBrief } from '../services/campaign-service.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

const router = express.Router();

/**
 * Create a new campaign
 * POST /api/campaigns
 */
router.post('/', async (req, res) => {
  try {
    const { campaignData, clientId = DEFAULT_CLIENT_ID } = req.body;
    const campaignId = await createCampaign(clientId, campaignData);
    res.status(200).json({ campaignId });
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({ error: 'Failed to create campaign' });
  }
});

/**
 * Generate a campaign brief
 * POST /api/campaigns/brief
 */
router.post('/brief', async (req, res) => {
  try {
    const result = await generateCampaignBrief(req.body);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error generating campaign brief:', error);
    res.status(500).json({ error: 'Failed to generate campaign brief' });
  }
});

/**
 * Get a campaign
 * GET /api/campaigns/:campaignId
 */
router.get('/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId = DEFAULT_CLIENT_ID } = req.query;
    const campaign = await getCampaign(clientId, campaignId);
    res.status(200).json(campaign);
  } catch (error) {
    console.error('Error getting campaign:', error);
    res.status(500).json({ error: 'Failed to get campaign' });
  }
});

/**
 * Update a campaign
 * PUT /api/campaigns/:campaignId
 */
router.put('/:campaignId', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { campaignData, clientId = DEFAULT_CLIENT_ID } = req.body;
    await updateCampaign(clientId, campaignId, campaignData);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error updating campaign:', error);
    res.status(500).json({ error: 'Failed to update campaign' });
  }
});

export default router;

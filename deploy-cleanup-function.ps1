# PowerShell script for deploying the image cleanup function
# Project: Palas Influencer Intelligence Platform

# Set environment variables
$env:PROJECT_ID = "studious-booth-446123-h5"
$env:REGION = "us-central1"

Write-Host "Starting deployment of cleanup function for project: $env:PROJECT_ID" -ForegroundColor Green

# Step 1: Set the project in gcloud
Write-Host "Setting Google Cloud project..." -ForegroundColor Cyan
gcloud config set project $env:PROJECT_ID

# Step 2: Enable required APIs
Write-Host "Enabling required Google Cloud APIs..." -ForegroundColor Cyan
gcloud services enable cloudfunctions.googleapis.com cloudscheduler.googleapis.com pubsub.googleapis.com

# Step 3: Create a Pub/Sub topic for triggering the function
Write-Host "Creating Pub/Sub topic for cleanup function..." -ForegroundColor Cyan
gcloud pubsub topics create daily-cleanup --project=$env:PROJECT_ID

# Step 4: Deploy the cleanup function
Write-Host "Deploying image cleanup function..." -ForegroundColor Cyan
gcloud functions deploy cleanupOldImages `
  --runtime nodejs16 `
  --trigger-topic daily-cleanup `
  --memory 256MB `
  --timeout 540s `
  --project $env:PROJECT_ID `
  --region $env:REGION `
  --source functions `
  --entry-point cleanupOldImages

# Step 5: Create Cloud Scheduler job to trigger cleanup daily
Write-Host "Creating Cloud Scheduler job for daily cleanup..." -ForegroundColor Cyan
gcloud scheduler jobs create pubsub cleanup-images-daily `
  --schedule "0 0 * * *" `
  --topic daily-cleanup `
  --message-body "cleanup" `
  --project $env:PROJECT_ID `
  --location $env:REGION

Write-Host "Deployment of cleanup function completed." -ForegroundColor Green

// run-campaign-test.js
// Simple test runner for campaign retrieval diagnostics

import { spawn } from 'child_process';
import path from 'path';

console.log('🚀 Starting Campaign Retrieval Diagnostic Test...\n');

const testProcess = spawn('node', ['test-campaign-retrieval.js'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

testProcess.on('close', (code) => {
  console.log(`\n🏁 Test process exited with code ${code}`);
  
  if (code === 0) {
    console.log('✅ Test completed successfully!');
  } else {
    console.log('❌ Test failed with errors');
  }
  
  process.exit(code);
});

testProcess.on('error', (error) => {
  console.error('💥 Failed to start test process:', error);
  process.exit(1);
});

# PowerShell script for manually deploying to Google Cloud Run
# Project: Palas Influencer Intelligence Platform

# Set environment variables
$env:PROJECT_ID = "studious-booth-446123-h5"
$env:REGION = "us-central1"
$env:SERVICE_NAME = "palas-influencer-intelligence"
$env:IMAGE_NAME = "gcr.io/$env:PROJECT_ID/$env:SERVICE_NAME:latest"
$env:OPENAI_API_KEY = "********************************************************************************************************************************************************************"
$env:INFLUENCERS_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMzQ0NzA4NDMwLCJpYXQiOjE3Mzk5MDg0MzAsImp0aSI6IjkxZTlmODlkZTI1NDQ3MWI5OTkzZTA3YjQ0YmVlMTI0IiwidXNlcl9pZCI6Nzg1MX0.qpbfGOObJMjLjYob_bzW7VX10SCdZpMVLxBEITIUuZc"

Write-Host "Starting manual deployment process for project: $env:PROJECT_ID" -ForegroundColor Green

# Step 1: Set the project in gcloud
Write-Host "Setting Google Cloud project..." -ForegroundColor Cyan
gcloud config set project $env:PROJECT_ID

# Step 2: Build the Docker image
Write-Host "Building Docker image..." -ForegroundColor Cyan
docker build -t $env:IMAGE_NAME .

# Step 3: Configure Docker to use gcloud as a credential helper
Write-Host "Configuring Docker authentication..." -ForegroundColor Cyan
gcloud auth configure-docker

# Step 4: Push the image to Container Registry
Write-Host "Pushing image to Container Registry..." -ForegroundColor Cyan
docker push $env:IMAGE_NAME

# Step 5: Deploy to Cloud Run
Write-Host "Deploying to Cloud Run..." -ForegroundColor Cyan
gcloud run deploy $env:SERVICE_NAME `
  --image=$env:IMAGE_NAME `
  --platform=managed `
  --region=$env:REGION `
  --allow-unauthenticated `
  --memory=2Gi `
  --cpu=2 `
  --min-instances=1 `
  --max-instances=10 `
  --set-env-vars=NODE_ENV=production `
  --set-secrets=FIREBASE_SERVICE_ACCOUNT=firebase-service-account:latest,OPENAI_API_KEY=openai-api-key:latest,INFLUENCERS_API_KEY=influencers-api-key:latest `
  --set-env-vars=STORAGE_BUCKET=palas-run-cache `
  --project=$env:PROJECT_ID

# Step 6: Verify deployment
Write-Host "Verifying deployment..." -ForegroundColor Cyan
$serviceUrl = $(gcloud run services describe $env:SERVICE_NAME --platform=managed --region=$env:REGION --project=$env:PROJECT_ID --format="value(status.url)")

if ($serviceUrl) {
    Write-Host "Deployment successful! Your service is available at: $serviceUrl" -ForegroundColor Green

    # Test the health endpoint
    Write-Host "Testing health endpoint..." -ForegroundColor Cyan
    try {
        $response = Invoke-RestMethod -Uri "$serviceUrl/_health" -Method Get
        Write-Host "Health check successful: $($response | ConvertTo-Json)" -ForegroundColor Green
    }
    catch {
        Write-Host "Health check failed: $_" -ForegroundColor Red
    }
}
else {
    Write-Host "Deployment verification failed. Check the Cloud Run console for details." -ForegroundColor Red
}

Write-Host "Manual deployment process completed." -ForegroundColor Green

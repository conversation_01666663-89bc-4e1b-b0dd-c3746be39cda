# Firestore Database Structure and Security

## Overview

This document provides a detailed specification for the Firestore database structure and security rules for the influencer analysis platform. The database is designed to store both global influencer data and client-specific campaign data.

## Database Structure

### 1. Influencers Collection (Global)

The Influencers collection stores data about influencers that is not client-specific and can be accessed by any client.

```
/influencers/{influencer_id}/
```

**Fields:**
- `username`: String - The influencer's username (e.g., Instagram handle)
- `full_name`: String - The influencer's full name
- `email`: String - The influencer's email (if available)
- `location`: String - The influencer's location (city, country)
- `speaking_language`: String - The influencer's primary language
- `has_brand_deals`: Boolean - Whether the influencer has worked with brands
- `has_link_in_bio`: Boolean - Whether the influencer has a link in their bio
- `is_business`: Boolean - Whether the profile is marked as a business
- `is_creator`: Boolean - Whether the profile belongs to a creator
- `platforms`: Map - Contains data for each platform the influencer is on
  - `instagram`: Map (if applicable)
    - `username`: String - Instagram handle
    - `follower_count`: Number - Number of followers
    - `biography`: String - Profile bio
    - `engagement_percent`: Number - Engagement rate
    - `niches`: Map
      - `primary`: String - Primary niche
      - `secondary`: Array<String> - Secondary niches
    - `hashtags`: Array<String> - Commonly used hashtags
    - `posting_frequency_recent_months`: Number - Posting frequency
    - `follower_growth`: Map
      - `three_months_ago`: Number - Growth percentage
      - `six_months_ago`: Number - Growth percentage
      - `nine_months_ago`: Number - Growth percentage
      - `twelve_months_ago`: Number - Growth percentage
    - `profile_image_url`: String - URL to cached profile image
  - `tiktok`: Map (similar structure to Instagram)
  - `youtube`: Map (similar structure to Instagram)
  - `twitter`: Map (similar structure to Instagram)
  - Other platforms as needed
- `creator_has`: Array<Map> - List of all platforms the creator has
  - `platform`: String - Platform name
  - `url`: String - Profile URL (if available)
- `last_updated`: Timestamp - When the influencer data was last updated
- `created_at`: Timestamp - When the influencer was first added to the database

**Subcollections:**

#### 1.1 Web Analysis

```
/influencers/{influencer_id}/web_analysis/{analysis_id}/
```

**Fields:**
- `name`: String - Influencer's name
- `sentiment_score`: Number - Overall sentiment score
- `risk_level`: String - Risk assessment level
- `deep_dive_report`: Map
  - `aliases`: Array<String> - Alternative names
  - `timeline_events`: Array<Map>
    - `date`: String - Event date
    - `event`: String - Event description
  - `brand_mentions`: Array<Map>
    - `brand`: String - Brand name
    - `sentiment`: String - Sentiment (positive, negative, neutral)
    - `context`: String - Context of the mention
  - `partnerships`: Array<Map>
    - `brand`: String - Brand name
    - `year`: String - Year of partnership
    - `description`: String - Partnership description
  - `controversies`: Array<Map>
    - `year`: String - Year of controversy
    - `description`: String - Description of controversy
    - `resolution`: String - How it was resolved (if applicable)
  - `media_mentions`: Array<Map>
    - `source`: String - Media source
    - `date`: String - Publication date
    - `title`: String - Article title
    - `url`: String - Article URL
    - `sentiment`: String - Sentiment (positive, negative, neutral)
- `created_at`: Timestamp - When the analysis was created
- `updated_at`: Timestamp - When the analysis was last updated

#### 1.2 Raw Data Storage References

```
/influencers/{influencer_id}/raw_data/{source_id}/
```

**Fields:**
- `source`: String - Data source name (e.g., "influencers_club")
- `storage_path`: String - Path to the raw JSON in Cloud Storage
- `processed_storage_path`: String - Path to the processed JSON in Cloud Storage
- `pulled_at`: Timestamp - When the data was pulled
- `processed_at`: Timestamp - When the data was processed

### 2. Clients Collection

```
/clients/{client_id}/
```

**Fields:**
- `name`: String - Client name
- `email`: String - Client email
- `created_at`: Timestamp - When the client was created
- `updated_at`: Timestamp - When the client was last updated
- `settings`: Map - Client-specific settings
  - `default_min_follower_count`: Number - Default minimum follower count
  - `default_max_follower_count`: Number - Default maximum follower count
  - `default_min_engagement_rate`: Number - Default minimum engagement rate
  - Other settings as needed

**Subcollections:**

#### 2.1 Campaigns

```
/clients/{client_id}/campaigns/{campaign_id}/
```

**Fields:**
- `name`: String - Campaign name
- `report_id`: String - Unique report ID
- `product_description`: String - Description of the product
- `influencer_gender`: String - Target influencer gender
- `influencer_niche`: String - Target influencer niche
- `influencer_age`: String - Target influencer age range
- `influencer_personality`: String - Target influencer personality
- `influencer_aesthetic`: String - Target influencer aesthetic
- `min_follower_count`: Number - Minimum follower count
- `max_follower_count`: Number - Maximum follower count
- `min_engagement_rate`: Number - Minimum engagement rate
- `created_at`: Timestamp - When the campaign was created
- `updated_at`: Timestamp - When the campaign was last updated
- `status`: String - Campaign status (e.g., "draft", "active", "completed")

**Subcollections:**

##### 2.1.1 Discovered Influencers

```
/clients/{client_id}/campaigns/{campaign_id}/discovered_influencers/{discovery_id}/
```

**Fields:**
- `similar_accounts`: Array<Map>
  - `username`: String - Influencer username
  - `profile_url`: String - Profile URL
  - `engagement_percent`: Number - Engagement rate
  - `follower_count`: Number - Follower count
  - `sort_rationale`: String - Rationale for including this influencer
- `created_at`: Timestamp - When the discovery was created

##### 2.1.2 Campaign Influencers

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/
```

**Fields:**
- `influencer_id`: String - Reference to the global influencer document
- `username`: String - Influencer username
- `status`: String - Status in the campaign (e.g., "discovered", "selected", "enriched", "approved")
- `created_at`: Timestamp - When the influencer was added to the campaign
- `updated_at`: Timestamp - When the influencer was last updated

**Subcollections:**

###### ******* Aesthetic Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/aesthetic_analysis/{analysis_id}/
```

**Fields:**
- `name`: String - Influencer name
- `brand_fit_score`: Number - Overall brand fit score
- `content_analysis`: Map
  - `visual_fit`: Number - Visual fit score
  - `tone_fit`: String - Description of tone fit
  - `content_themes`: Array<String> - Content themes
  - `image_analyses`: Array<Map>
    - `image_index`: Number - Image index
    - `description`: String - Image description
    - `colors`: String - Color palette
    - `composition`: String - Composition analysis
    - `alignment`: String - Brand alignment
- `created_at`: Timestamp - When the analysis was created

###### ******* ROI Analysis

```
/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/roi_analysis/{analysis_id}/
```

**Fields:**
- `brand_fit_score`: Number - Overall brand fit score
- `brand_fit_description`: String - Description of brand fit
- `risk_level`: String - Risk assessment level
- `risk_description`: String - Description of risks
- `influencer_analysis`: Map
  - `roi_projection`: Map
    - `expected_impressions`: Number - Projected impressions
    - `expected_engagement_rate`: Number - Projected engagement rate
    - `expected_engagements`: Number - Projected engagements
    - `roi_rating`: String - ROI rating (e.g., "High", "Medium", "Low")
    - `roi_rationale`: String - Rationale for ROI rating
    - `strengths`: Array<String> - Influencer strengths
    - `weaknesses`: Array<String> - Influencer weaknesses
- `created_at`: Timestamp - When the analysis was created

## Security Rules

The following security rules ensure that data is properly protected:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && request.auth.token.admin == true;
    }
    
    function isClientMember(clientId) {
      return isAuthenticated() && 
        (request.auth.token.client_id == clientId || 
         request.auth.token.client_ids.hasAny([clientId]));
    }
    
    // Global influencer data can be read by any authenticated user
    match /influencers/{influencerId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      
      // Web analysis can be read by any authenticated user
      match /web_analysis/{analysisId} {
        allow read: if isAuthenticated();
        allow write: if isAdmin();
      }
      
      // Raw data references can be read by any authenticated user
      match /raw_data/{sourceId} {
        allow read: if isAuthenticated();
        allow write: if isAdmin();
      }
    }
    
    // Client data can only be accessed by the respective client or admin
    match /clients/{clientId} {
      allow read: if isClientMember(clientId) || isAdmin();
      allow write: if isAdmin();
      
      // Campaigns can be accessed by client members or admin
      match /campaigns/{campaignId} {
        allow read: if isClientMember(clientId) || isAdmin();
        allow write: if isClientMember(clientId) || isAdmin();
        
        // Discovered influencers can be accessed by client members or admin
        match /discovered_influencers/{discoveryId} {
          allow read: if isClientMember(clientId) || isAdmin();
          allow write: if isClientMember(clientId) || isAdmin();
        }
        
        // Campaign influencers can be accessed by client members or admin
        match /campaign_influencers/{influencerId} {
          allow read: if isClientMember(clientId) || isAdmin();
          allow write: if isClientMember(clientId) || isAdmin();
          
          // Aesthetic analysis can be accessed by client members or admin
          match /aesthetic_analysis/{analysisId} {
            allow read: if isClientMember(clientId) || isAdmin();
            allow write: if isClientMember(clientId) || isAdmin();
          }
          
          // ROI analysis can be accessed by client members or admin
          match /roi_analysis/{analysisId} {
            allow read: if isClientMember(clientId) || isAdmin();
            allow write: if isClientMember(clientId) || isAdmin();
          }
        }
      }
    }
  }
}
```

## Indexes

The following indexes should be created to support efficient queries:

### Single-Field Indexes

1. `/influencers` collection:
   - `username` (ASC)
   - `last_updated` (DESC)
   - `created_at` (DESC)

2. `/clients/{clientId}/campaigns` collection:
   - `status` (ASC)
   - `created_at` (DESC)
   - `updated_at` (DESC)

3. `/clients/{clientId}/campaigns/{campaignId}/campaign_influencers` collection:
   - `status` (ASC)
   - `created_at` (DESC)
   - `updated_at` (DESC)

### Composite Indexes

1. `/influencers` collection:
   - `platforms.instagram.follower_count` (ASC), `platforms.instagram.engagement_percent` (DESC)
   - `platforms.instagram.niche_class` (ASC), `platforms.instagram.follower_count` (DESC)

2. `/clients/{clientId}/campaigns/{campaignId}/campaign_influencers` collection:
   - `status` (ASC), `created_at` (DESC)

3. `/influencers/{influencerId}/raw_data` collection:
   - `source` (ASC), `pulled_at` (DESC)

## Cloud Storage Structure

In addition to Firestore, Cloud Storage is used to store large JSON files and media:

```
/influencers/{influencer_id}/raw_data/{source}_{timestamp}.json
/influencers/{influencer_id}/processed_data/{source}_{timestamp}.json
/palas-image-cache/{image_id}.jpg
/clients/{client_id}/campaigns/{campaign_id}/reports/{report_type}_{timestamp}.json
```

## Storage Security Rules

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && request.auth.token.admin == true;
    }
    
    function isClientMember(clientId) {
      return isAuthenticated() && 
        (request.auth.token.client_id == clientId || 
         request.auth.token.client_ids.hasAny([clientId]));
    }
    
    // Global influencer data can be read by any authenticated user
    match /influencers/{influencerId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Image cache can be read by anyone (public)
    match /palas-image-cache/{imageId} {
      allow read: if true;
      allow write: if isAuthenticated();
    }
    
    // Client data can only be accessed by the respective client or admin
    match /clients/{clientId}/{allPaths=**} {
      allow read: if isClientMember(clientId) || isAdmin();
      allow write: if isClientMember(clientId) || isAdmin();
    }
  }
}
```

## Data Migration Strategy

To migrate from the current JSON-based system to Firestore:

1. **Create Firestore Collections**:
   - Set up the Influencers collection
   - Set up the Clients collection
   - Set up the Campaigns subcollection

2. **Import Existing Data**:
   - Import influencer data from processed_influencers_club_pull.json files
   - Import campaign data from campaign_analysis.json files
   - Import web analysis data from web_analysis.json files
   - Import aesthetic analysis data from aesthetic_analysis.json files
   - Import ROI analysis data from roi_analysis.json files

3. **Update References**:
   - Link campaign influencers to global influencer documents
   - Update storage paths for raw and processed data

4. **Validate Data**:
   - Ensure all data is properly imported
   - Verify relationships between documents
   - Test queries to ensure efficiency

## Conclusion

This database structure provides a scalable, secure foundation for the influencer analysis platform. By separating global influencer data from client-specific campaign data, the platform can efficiently store and retrieve information while maintaining proper access controls.

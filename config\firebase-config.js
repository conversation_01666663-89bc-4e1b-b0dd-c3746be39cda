/**
 * firebase-config.js
 * Centralized Firebase configuration for both testing and production
 */

import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Initialize Firebase with credentials
 * @param {boolean} verbose - Whether to log verbose output
 * @returns {Object} - Firebase app instance
 */
export function initializeFirebase(verbose = false) {
  // If Firebase is already initialized, return the existing app
  if (getApps().length > 0) {
    if (verbose) console.log('Firebase already initialized');
    return getApps()[0];
  }

  if (verbose) console.log('Initializing Firebase');

  // Check for Firebase service account in environment variable (for Cloud Run)
  if (process.env.FIREBASE_SERVICE_ACCOUNT) {
    try {
      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT);
      const app = initializeApp({
        credential: cert(serviceAccount),
        storageBucket: process.env.STORAGE_BUCKET || 'palas-run-cache'
      });

      if (verbose) console.log('Firebase initialized with service account from environment variable');
      return app;
    } catch (error) {
      console.error('Failed to initialize Firebase with service account from environment variable:', error);
    }
  }

  // Try using credentials file as fallback
  const credentialsPath = path.join(__dirname, '..', 'credentials', 'palas-influencer-intelligence-firebase-adminsdk-fbsvc-7f452c5b1f.json');

  if (fs.existsSync(credentialsPath)) {
    try {
      const app = initializeApp({
        credential: cert(credentialsPath),
        storageBucket: process.env.STORAGE_BUCKET || 'palas-run-cache'
      });

      if (verbose) console.log('Firebase initialized with credentials file');
      return app;
    } catch (error) {
      console.error('Failed to initialize Firebase with credentials file:', error);
    }
  } else {
    console.warn(`Firebase credentials file not found at ${credentialsPath}`);
  }

  // Try using default credentials as last resort
  try {
    if (verbose) console.log('Attempting to initialize Firebase with default credentials');
    return initializeApp();
  } catch (error) {
    console.error('Failed to initialize Firebase with default credentials:', error);
    throw new Error('Failed to initialize Firebase: No valid credentials found');
  }
}

/**
 * Get Firestore instance
 * @returns {Object} - Firestore instance
 */
export function getFirestoreDb() {
  initializeFirebase();
  return getFirestore();
}

/**
 * Get Storage instance
 * @returns {Object} - Storage instance
 */
export function getStorageInstance() {
  initializeFirebase();
  return getStorage();
}

// Export default for convenience
export default {
  initializeFirebase,
  getFirestoreDb,
  getStorageInstance
};

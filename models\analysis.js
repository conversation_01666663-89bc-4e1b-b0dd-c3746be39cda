// analysis.js
// Analysis data models

import { getFirestoreDb } from '../config/firebase-config.js';
import { writeJsonToBucket } from '../helpers/storage-helpers.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

/**
 * Web Analysis model
 */
class WebAnalysis {
  /**
   * Create a new WebAnalysis instance
   * @param {Object} data - The web analysis data
   */
  constructor(data = {}) {
    this.name = data.name || '';
    this.sentiment_score = data.sentiment_score || 0;
    this.risk_level = data.risk_level || 'Medium';
    this.deep_dive_report = data.deep_dive_report || {};
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  /**
   * Create a new web analysis in Firestore
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The web analysis data
   * @param {string} clientId - The client ID (optional)
   * @param {string} campaignId - The campaign ID (optional)
   * @returns {string} - The web analysis ID
   */
  static async create(influencerId, data, clientId = DEFAULT_CLIENT_ID, campaignId = null) {
    const db = getFirestoreDb();

    // Create a new web analysis document in the global influencer collection
    const webAnalysisRef = db.collection('influencers').doc(influencerId)
      .collection('web_analysis').doc();
    const webAnalysisId = webAnalysisRef.id;

    // Prepare web analysis data
    const webAnalysisData = {
      name: data.name || '',
      sentiment_score: data.sentiment_score || 0,
      risk_level: data.risk_level || 'Medium',
      deep_dive_report: data.deep_dive_report || {},
      created_at: new Date(),
      updated_at: new Date()
    };

    // Add client and campaign IDs if provided
    if (clientId) {
      webAnalysisData.client_id = clientId;
    }

    if (campaignId) {
      webAnalysisData.campaign_id = campaignId;
    }

    // Save to Firestore
    await webAnalysisRef.set(webAnalysisData);

    // Save to Cloud Storage with a more structured path
    let storagePath;
    if (campaignId) {
      // If campaign ID is provided, use a client/campaign-specific path
      storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/web_analysis.json`;
    } else {
      // Otherwise, use the global influencer path
      storagePath = `/influencers/${influencerId}/web_analysis/${webAnalysisId}.json`;
    }

    await writeJsonToBucket(storagePath, webAnalysisData, webAnalysisRef);
    console.log(`Web analysis saved to Cloud Storage at ${storagePath}`);

    return webAnalysisId;
  }
}

/**
 * Aesthetic Analysis model
 */
class AestheticAnalysis {
  /**
   * Create a new AestheticAnalysis instance
   * @param {Object} data - The aesthetic analysis data
   */
  constructor(data = {}) {
    this.name = data.name || '';
    this.brand_fit_score = data.brand_fit_score || 0;
    this.content_analysis = data.content_analysis || {};
    this.created_at = data.created_at || null;
  }

  /**
   * Create a new aesthetic analysis in Firestore
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The aesthetic analysis data
   * @returns {string} - The aesthetic analysis ID
   */
  static async create(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, data) {
    const db = getFirestoreDb();

    // Create a new aesthetic analysis document
    const aestheticAnalysisRef = db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .collection('aesthetic_analysis').doc();
    const aestheticAnalysisId = aestheticAnalysisRef.id;

    // Prepare aesthetic analysis data
    const aestheticAnalysisData = {
      name: data.name || '',
      brand_fit_score: data.brand_fit_score || 0,
      content_analysis: data.content_analysis || {},
      created_at: new Date(),
      client_id: clientId,
      campaign_id: campaignId,
      influencer_id: influencerId
    };

    // Save to Firestore
    await aestheticAnalysisRef.set(aestheticAnalysisData);

    // Save to Cloud Storage with a structured path
    const storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/aesthetic_analysis.json`;
    await writeJsonToBucket(storagePath, aestheticAnalysisData, aestheticAnalysisRef);
    console.log(`Aesthetic analysis saved to Cloud Storage at ${storagePath}`);

    return aestheticAnalysisId;
  }
}

/**
 * ROI Analysis model
 */
class ROIAnalysis {
  /**
   * Create a new ROIAnalysis instance
   * @param {Object} data - The ROI analysis data
   */
  constructor(data = {}) {
    this.brand_fit_score = data.brand_fit_score || 0;
    this.brand_fit_description = data.brand_fit_description || '';
    this.risk_level = data.risk_level || 'Medium';
    this.risk_description = data.risk_description || '';
    this.influencer_analysis = data.influencer_analysis || {};
    this.created_at = data.created_at || null;
  }

  /**
   * Create a new ROI analysis in Firestore
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The ROI analysis data
   * @returns {string} - The ROI analysis ID
   */
  static async create(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, data) {
    const db = getFirestoreDb();

    // Create a new ROI analysis document
    const roiAnalysisRef = db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .collection('roi_analysis').doc();
    const roiAnalysisId = roiAnalysisRef.id;

    // Prepare ROI analysis data
    const roiAnalysisData = {
      brand_fit_score: data.brand_fit_score || 0,
      brand_fit_description: data.brand_fit_description || '',
      risk_level: data.risk_level || 'Medium',
      risk_description: data.risk_description || '',
      influencer_analysis: data.influencer_analysis || {},
      created_at: new Date(),
      client_id: clientId,
      campaign_id: campaignId,
      influencer_id: influencerId
    };

    // Save to Firestore
    await roiAnalysisRef.set(roiAnalysisData);

    // Save to Cloud Storage with a structured path
    const storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/roi_analysis.json`;
    await writeJsonToBucket(storagePath, roiAnalysisData, roiAnalysisRef);
    console.log(`ROI analysis saved to Cloud Storage at ${storagePath}`);

    return roiAnalysisId;
  }
}

/**
 * Merged Analysis model
 */
class MergedAnalysis {
  /**
   * Create a new MergedAnalysis instance
   * @param {Object} data - The merged analysis data
   */
  constructor(data = {}) {
    this.profileInfo = data.profileInfo || {};
    this.roiProjection = data.roiProjection || {};
    this.webAnalysis = data.webAnalysis || {};
    this.brandFit = data.brandFit || {};
    this.created_at = data.created_at || null;
  }

  /**
   * Create a new merged analysis in Firestore
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The merged analysis data
   * @returns {string} - The merged analysis ID
   */
  static async create(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, data) {
    const db = getFirestoreDb();

    // Create a new merged analysis document
    const mergedAnalysisRef = db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .collection('merged_analysis').doc();
    const mergedAnalysisId = mergedAnalysisRef.id;

    // Prepare merged analysis data
    const mergedAnalysisData = {
      ...data,
      created_at: new Date(),
      client_id: clientId,
      campaign_id: campaignId,
      influencer_id: influencerId
    };

    // Save to Firestore
    await mergedAnalysisRef.set(mergedAnalysisData);

    // Save to Cloud Storage with a structured path
    const storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/merged_analysis.json`;
    await writeJsonToBucket(storagePath, mergedAnalysisData, mergedAnalysisRef);
    console.log(`Merged analysis saved to Cloud Storage at ${storagePath}`);

    return mergedAnalysisId;
  }
}

/**
 * Partnership Analysis model
 */
class PartnershipAnalysis {
  /**
   * Create a new PartnershipAnalysis instance
   * @param {Object} data - The partnership analysis data
   */
  constructor(data = {}) {
    this.partnership_status = data.partnership_status || {};
    this.contractual_readiness = data.contractual_readiness || {};
    this.conflict_analysis = data.conflict_analysis || {};
    this.alignment_risks = data.alignment_risks || {};
    this.recommendation = data.recommendation || {};
    this.created_at = data.created_at || null;
  }

  /**
   * Create a new partnership analysis in Firestore
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The partnership analysis data
   * @returns {string} - The partnership analysis ID
   */
  static async create(clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, data) {
    const db = getFirestoreDb();

    // Create a new partnership analysis document
    const partnershipAnalysisRef = db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .collection('partnership_analysis').doc();
    const partnershipAnalysisId = partnershipAnalysisRef.id;

    // Prepare partnership analysis data
    const partnershipAnalysisData = {
      partnership_status: data.partnership_status || {},
      contractual_readiness: data.contractual_readiness || {},
      conflict_analysis: data.conflict_analysis || {},
      alignment_risks: data.alignment_risks || {},
      recommendation: data.recommendation || {},
      created_at: new Date(),
      client_id: clientId,
      campaign_id: campaignId,
      influencer_id: influencerId
    };

    // Save to Firestore
    await partnershipAnalysisRef.set(partnershipAnalysisData);

    // Save to Cloud Storage with a structured path
    const storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/partnership_analysis.json`;
    await writeJsonToBucket(storagePath, partnershipAnalysisData, partnershipAnalysisRef);
    console.log(`Partnership analysis saved to Cloud Storage at ${storagePath}`);

    return partnershipAnalysisId;
  }
}

/**
 * FormatterAnalysis model for storing formatted analysis data
 */
class FormatterAnalysis {
  /**
   * Create a new formatted analysis
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {string} influencerId - The influencer ID
   * @param {Object} data - The formatted analysis data
   * @returns {string} - The formatted analysis ID
   */
  static async create(clientId, campaignId, influencerId, data) {
    const db = getFirestore();

    // Create a reference to the formatted analysis collection
    const formattedAnalysisRef = db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .collection('formatted_analysis').doc();

    const formattedAnalysisId = formattedAnalysisRef.id;

    // Prepare formatted analysis data
    const formattedAnalysisData = {
      ...data,
      created_at: new Date(),
      client_id: clientId,
      campaign_id: campaignId,
      influencer_id: influencerId
    };

    // Save to Firestore
    await formattedAnalysisRef.set(formattedAnalysisData);

    // Save to Cloud Storage with a structured path
    const storagePath = `/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/formatted_analysis.json`;
    await writeJsonToBucket(storagePath, formattedAnalysisData, formattedAnalysisRef);
    console.log(`Formatted analysis saved to Cloud Storage at ${storagePath}`);

    return formattedAnalysisId;
  }
}

export {
  WebAnalysis,
  AestheticAnalysis,
  ROIAnalysis,
  PartnershipAnalysis,
  MergedAnalysis,
  FormatterAnalysis
};

influencers.club API Documentation
Welcome to influencers.club official API documentation. Our API allows you to enrich user data by providing insights into social media profiles.

Use Cases
Enhance your CRM – Automatically append social media data to email signups.
Find creator details – Get social media profiles and email addresses for users signing up with only a username.
Social Media Insights – Retrieve engagement, follower counts, and platform presence for creators.
It works in two ways:

Database Enrichment – If we have the requested data, we return it instantly.
Real-Time Data – If we don’t have the data internally, we fetch it directly from the relevant platform in real-time.
For any API-related questions, contact us.

To access the API, you must obtain an API Key.

How to Get Your API Key
Register at Influencers Club Dashboard.
After logging in, click on your profile icon in the top right.
Navigate to your Profile Page.
Your Enrichment API Key will be available there.
Using the API Key
All requests require a Bearer Token:

Authorization: Bearer YOUR_API_KEY

Rate Limits
To be added. (Pending confirmation from developers.)

Was this section helpful?
Yes
No
Base URL

Production:

https://api-dashboard.influencers.club

Language Box

cURL
Ruby
Ruby
Python
Python
PHP
PHP
Java
Java
Node.js
Node.js
Go
Go
.NET
.NET
1. Enrich Creator Profile
This endpoint retrieves detailed insights about a creator’s social media presence based on their email, username, or profile URL. It provides information on key attributes such as follower count, engagement metrics, content data, and platform-specific details for Instagram, YouTube, and TikTok.

Key Data Points
General Information
Email (if available and requested)
Location (city, country if available)
Speaking Language (detected primary language)
First Name (if publicly available)
Business Account Status (is the profile marked as a business?)
Creator Status (does the profile belong to a creator?)
Has Brand Deals (is there an indication that the creator has worked with brands?)
Has Link in Bio (is there a link in the creator’s bio? Useful for tracking affiliate or sponsored links.)
Instagram Insights
Username (official IG handle)
Follower Count (current number of followers)
Biography (self-description, often includes brand collaborations, niches, and relevant details)
Engagement Rate (percentage of audience interacting with posts)
Primary & Sub Niche Classification (automatically categorized niche)
Recent Posting Frequency (how often they post content)
Follower Growth Trends (growth history over the past 3, 6, 9, and 12 months)
Latest Post Data (optional – includes post caption, engagement, media type, and hashtags)
Geotagged Location (if posts have location data, useful for local targeting)
Tagged Users in Posts (identifies collaborations with other influencers or brands)
YouTube Insights
Channel Title & Description (summary of content focus)
Subscriber Count (audience size)
Average Video Views (how many views they typically get per video)
Monetization Status (is the channel eligible for ads and sponsorships?)
Engagement Rate (likes, comments relative to views)
Posting Frequency (how often they upload new content)
Follower Growth Trends (tracking audience increase over time)
Recent Video Data (optional – includes video title, category, engagement, and hashtags)
TikTok Insights
Username (official TikTok handle)
Follower Count (audience size)
Biography (self-description, often includes brand collaborations)
Category (TikTok’s classification of the content type)
Average Likes Per Video (indicates engagement levels)
Engagement Rate (how engaged the audience is)
Posting Frequency (how often they upload videos)
Follower Growth Trends (tracking audience increase over time)
Recent Video Data (optional – includes video caption, engagement, and media type)
Has TikTok Shop (does the creator use TikTok’s shopping feature?)
Platform Presence
Creator Platform List (lists of all social media platforms for a given profile)
Important Notes
Email availability is limited – Emails are only returned if email_required = true and if the creator has a public email.
Header Parameters
Authorization
string
Find your API key on the Profile Page after logging into your account.

Body Parameters
filter_value
string
Required
The input value to search for (e.g., email, username, or profile handle).

Min length
1
filter_key
string
Required
Specifies the type of the filter (e.g., email, username, or profile URL).

Enum values:
url
email
username
platform
string
The social media platform from which the data will be retrieved.

Enum values:
instagram
youtube
tiktok
onlyfans
twitter
twitch
discord
facebook
snapchat
pinterest
... 2 other enums

Show more

email_required
string
Options are 'preferred', 'must_have' , 'not_needed', true and false

Enum values:
not_needed
preferred
must_have
post_data_required
boolean
True if post data should be returned

Was this section helpful?
Yes
No
POST

/public/v1/enrichment/single_enrich/

Node.js


var myHeaders = new Headers();
myHeaders.append("Authorization", "Bearer YOUR_API_KEY");

var raw = JSON.stringify({
  "filter_value": "Creator email, username, or profile handle\t",
  "filter_key": "email",
  "platform": "instagram",
  "email_required": "preferred",
  "post_data_required": false
});

var requestOptions = {
  method: 'POST',
  headers: myHeaders,
  body: raw,
  redirect: 'follow'
};

fetch("https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/", requestOptions)
  .then(response => response.text())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
Response

200
{
  "email": "",
  "location": "",
  "speaking_language": "",
  "first_name": "",
  "has_brand_deals": false,
  "has_link_in_bio": false,
  "is_business": false,
  "is_creator": false,
  "instagram": {
    "username": "",
    "follower_count": null,
    "biography": "",
    "engagement_percent": null,
    "niches": {
      "primary": "",
      "secondary": []
    },
    "hashtags": [],
    "posting_frequency_recent_months": null,
    "follower_growth": {
      "six_months_ago": null,
      "twelve_months_ago": null,
      "three_months_ago": null,
      "nine_months_ago": null
    },
    "latest_post": {
      "post_id": "",
      "created_at": "",
      "caption": "",
      "hashtags": [],
      "post_url": "",
      "media": [
        {
          "media_id": "",
          "type": "",
          "url": ""
        }
      ],
      "engagement": {
        "likes": null,
        "comments": null,
        "shares": null,
        "views": null
      },
      "location": {
        "name": "",
        "latitude": null,
        "longitude": null
      },
      "tagged_users": []
    }
  },
  "youtube": {
    "title": "",
    "description": "",
    "subscriber_count": null,
    "niches": {
      "primary": "",
      "secondary": []
    },
    "video_count": null,
    "average_views": null,
    "engagement_percent": null,
    "hashtags": [],
    "is_monetized": false,
    "has_community_posts": false,
    "follower_growth": {
      "six_months_ago": null,
      "twelve_months_ago": null,
      "three_months_ago": null,
      "nine_months_ago": null
    },
    "latest_video": {
      "video_id": "",
      "title": "",
      "published_at": "",
      "views": null,
      "likes": null,
      "comments": null,
      "duration": "",
      "language": "",
      "topic_categories": []
    }
  },
  "tiktok": {
    "username": "",
    "biography": "",
    "category": "",
    "follower_count": null,
    "niches": {
      "primary": "",
      "secondary": []
    },
    "average_likes": null,
    "engagement_percent": null,
    "hashtags": [],
    "follower_growth": {
      "six_months_ago": null,
      "twelve_months_ago": null,
      "three_months_ago": null,
      "nine_months_ago": null
    },
    "latest_post": {
      "post_id": "",
      "created_at": "",
      "caption": "",
      "hashtags": [],
      "post_url": "",
      "media": {
        "type": "",
        "url": "",
        "duration": null
      },
      "engagement": {
        "likes": null,
        "comments": null,
        "views": null,
        "shares": null
      },
      "sound": {
        "name": "",
        "url": ""
      }
    }
  },
  "creator_has": [
    {
      "platform": ""
    }
  ]
}

Show more
2. Find Similar Creators
This endpoint helps identify creators who are similar to a given influencer based on their social media presence, niche, engagement patterns, and audience characteristics. It allows businesses to discover new potential partners, expand outreach efforts, and optimize influencer marketing campaigns by targeting lookalike creators.

How It Works
The system analyzes a provided influencer’s profile and returns a list of creators with similar attributes. The similarity is based on multiple factors such as:

Niche classification (Primary & Sub Niche)
Engagement levels (Likes, comments, shares, follower growth trends)
Content type (Video, posts, reels, TikTok format, etc.)
Brand affiliations (Whether the influencer has done brand collaborations)
Header Parameters
Authorization
string
Find your API key on the Profile Page after logging into your account.

Body Parameters
filter_value
string
Required
Accepts a full platform URL or a profile handle.

Min length
1
filter_key
string
Required
Defines the type of input being queried. Accepts a URL, or username.

Enum values:
url
username
platform
string
Required
Specifies the social media platform where the creator's data should be retrieved.

Enum values:
instagram
youtube
tiktok
onlyfans
twitter
Was this section helpful?
Yes
No
POST

/public/v1/enrichment/lookalikes/

Node.js


var myHeaders = new Headers();
myHeaders.append("Authorization", "Bearer {YOUR_API_KEY}");

var raw = JSON.stringify({
  "filter_value": "instagram.com/<username>",
  "filter_key": "url",
  "platform": "instagram"
});

var requestOptions = {
  method: 'POST',
  headers: myHeaders,
  body: raw,
  redirect: 'follow'
};

fetch("https://api-dashboard.influencers.club/public/v1/enrichment/lookalikes/", requestOptions)
  .then(response => response.text())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
Response

200
{
  "similar_accounts": [
    {
      "username": "",
      "profile_url": ""
    }
  ]
}

3. Discovery API
Header Parameters
Authorization
string
Required
Find your API key on the Profile Page after logging into your account.

Body ParametersExpand all
platform
string
Required
Enum values:
instagram
youtube
tiktok
twitch
twitter
onlyfans
paging
object
Show child attributes

sort
object
Show child attributes

filters
object
Show child attributes

ResponseExpand all
200
Object
Response Attributes
total
integer
Required
limit
integer
Required
credits_left
string (decimal)
Required
accounts
array
Show child attributes

POST

/public/v1/discovery/

Node.js


var myHeaders = new Headers();
myHeaders.append("Authorization", "Bearer YOUR API KEY");

var raw = JSON.stringify({
  "platform": "instagram",
  "paging": {
    "limit": null,
    "page": null
  },
  "sort": {
    "sort_by": "relevancy",
    "sort_order": "ASC"
  },
  "filters": {
    "location": "",
    "type": "",
    "gender": "",
    "profile_language": "",
    "ai_search": "",
    "number_of_followers": {
      "min": null,
      "max": null
    },
    "posting_frequency": null,
    "follower_growth": {
      "growth_percentage": null,
      "time_range_months": null
    },
    "average_likes": {
      "min": null,
      "max": null
    },
    "average_comments": {
      "min": null,
      "max": null
    },
    "average_views": {
      "min": null,
      "max": null
    },
    "average_video_downloads": {
      "min": null,
      "max": null
    },
    "has_tik_tok_shop": false,
    "exclude_private_profile": false,
    "is_verified": false,
    "keywords_in_bio": [
      ""
    ],
    "keywords_in_tweets": [
      ""
    ],
    "number_of_tweets": {
      "min": null,
      "max": null
    },
    "last_post": "",
    "number_of_subscribers": {
      "min": null,
      "max": null
    },
    "topics": [
      ""
    ],
    "keywords_in_video_titles": [
      ""
    ],
    "keywords_in_description": [
      ""
    ],
    "keywords_in_video_description": [
      ""
    ],
    "subscriber_growth": {
      "growth_percentage": null,
      "time_range_months": null
    },
    "has_shorts": false,
    "shorts_percentage": {
      "min": null,
      "max": null
    },
    "has_community_posts": false,
    "streams_live": false,
    "has_merch": false,
    "average_views_on_long_videos": {
      "min": null,
      "max": null
    },
    "average_views_on_shorts": {
      "min": null,
      "max": null
    },
    "number_of_videos": {
      "min": null,
      "max": null
    },
    "is_monetizing": false,
    "number_of_posts": {
      "min": null,
      "max": null
    },
    "reels_percent": {
      "min": null,
      "max": null
    },
    "average_views_for_reels": {
      "min": null,
      "max": null
    },
    "subscription_price": {
      "min": null,
      "max": null
    },
    "number_of_photos": {
      "min": null,
      "max": null
    },
    "number_of_likes": {
      "min": null,
      "max": null
    },
    "followers": {
      "min": null,
      "max": null
    },
    "active_subscribers": {
      "min": null,
      "max": null
    },
    "streamed_hours_last_30_days": {
      "min": null,
      "max": null
    },
    "total_hours_streamed": {
      "min": null,
      "max": null
    },
    "maximum_views_count": {
      "min": null,
      "max": null
    },
    "avg_views_last_30_days": {
      "min": null,
      "max": null
    },
    "streams_count_last_30_days": {
      "min": null,
      "max": null
    },
    "games_played": [
      ""
    ],
    "is_twitch_partner": false
  }
});

var requestOptions = {
  method: 'POST',
  headers: myHeaders,
  body: raw,
  redirect: 'follow'
};

fetch("https://api-dashboard.influencers.club/public/v1/discovery/", requestOptions)
  .then(response => response.text())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
Response

200
{
  "total": null,
  "limit": null,
  "credits_left": "",
  "accounts": [
    {
      "user_id": "",
      "profile": {
        "full_name": "",
        "username": "",
        "picture": "",
        "followers": null,
        "engagement_percent": null
      }
    }
  ]
}


Show more

Supported Platforms
Currently Integrated:
Instagram
TikTok
YouTube
OnlyFans
Twitter
Twitch
Planned Integrations:
LinkedIn
Reddit
Facebook
Pinterest
Discord
Douyin
Was this section helpful?
Yes
No
Pricing & Credit System
The API operates on a credit-based system. Each request consumes credits based on the data and features retrieved. Below is a detailed breakdown of the pricing structure:

Credits

Real-time Social Stats

Latest Post Data

Set Follower Count Limit

Email

All Social Platforms

Pro Filters

Creator Platform Links

0.01

✔

X

X

X

X

X

X

0.02

✔

✔

X

X

X

X

X

0.1

X

X

Less than 1000 followers on every platform

✔

X (highest follower count only)

✔

✔

1

✔

✔

1000 followers + default limit

✔

✔

✔

✔

Was this section helpful?
Yes
No
Error Handling
(Pending developer confirmation.)

400 Bad Request – Invalid request parameters.
401 Unauthorized – Invalid API key.
403 Forbidden – Insufficient permissions.
404 Not Found – No data found for given input.
429 Too Many Requests – Rate limit exceeded.
📌 For any issues, reach out at Influencers Club API Support.
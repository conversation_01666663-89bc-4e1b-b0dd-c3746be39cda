#!/bin/bash

# This script sets up the necessary secrets in Google Cloud Secret Manager

# Set your Google Cloud project ID
PROJECT_ID="palas-influencer-intelligence"

# Create secrets if they don't exist
echo "Creating secrets in Secret Manager..."

# Firebase service account
gcloud secrets create firebase-service-account --project=$PROJECT_ID || true
cat credentials/palas-influencer-intelligence-firebase-adminsdk-fbsvc-7f452c5b1f.json | gcloud secrets versions add firebase-service-account --data-file=- --project=$PROJECT_ID

# OpenAI API key
gcloud secrets create openai-api-key --project=$PROJECT_ID || true
echo -n "$OPENAI_API_KEY" | gcloud secrets versions add openai-api-key --data-file=- --project=$PROJECT_ID

# Influencers Club API key
gcloud secrets create influencers-api-key --project=$PROJECT_ID || true
echo -n "$INFLUENCERS_API_KEY" | gcloud secrets versions add influencers-api-key --data-file=- --project=$PROJECT_ID

# Grant the Cloud Run service account access to the secrets
SERVICE_ACCOUNT="$(gcloud iam service-accounts list --filter="displayName:Cloud Run Service Agent" --format='value(email)' --project=$PROJECT_ID)"
echo "Granting access to service account: $SERVICE_ACCOUNT"

gcloud secrets add-iam-policy-binding firebase-service-account \
  --member="serviceAccount:$SERVICE_ACCOUNT" \
  --role="roles/secretmanager.secretAccessor" \
  --project=$PROJECT_ID

gcloud secrets add-iam-policy-binding openai-api-key \
  --member="serviceAccount:$SERVICE_ACCOUNT" \
  --role="roles/secretmanager.secretAccessor" \
  --project=$PROJECT_ID

gcloud secrets add-iam-policy-binding influencers-api-key \
  --member="serviceAccount:$SERVICE_ACCOUNT" \
  --role="roles/secretmanager.secretAccessor" \
  --project=$PROJECT_ID

echo "Secrets setup complete!"

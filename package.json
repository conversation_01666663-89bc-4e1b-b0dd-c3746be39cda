{"name": "palas-firestore", "version": "1.0.0", "description": "Influencer analysis platform with Firestore integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "test-influencer": "node run-test.js"}, "dependencies": {"@google-cloud/storage": "^6.12.0", "axios": "^1.9.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.1", "join-images": "^1.1.3", "node-fetch": "^3.3.2", "openai": "^4.0.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}
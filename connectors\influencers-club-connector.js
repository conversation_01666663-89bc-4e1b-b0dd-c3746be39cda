// influencers-club-connector.js
// Influencers Club API connector

import axios from 'axios';
import { getFirestoreDb } from '../config/firebase-config.js';
import { getStorageInstance } from '../config/firebase-config.js';
import { DEFAULT_BUCKET_NAME, INFLUENCERS_API_KEY } from '../config/constants.js';
import { cacheImage } from '../helpers/image-processors.js';
import { Timestamp } from 'firebase-admin/firestore';

/**
 * Influencers Club API connector
 */
class InfluencersClubConnector {
  /**
   * Create a new Influencers Club connector
   * @param {string} apiKey - The Influencers Club API key
   * @param {string} bucketName - The storage bucket name
   */
  constructor(apiKey = INFLUENCERS_API_KEY, bucketName = DEFAULT_BUCKET_NAME) {
    this.apiKey = apiKey;
    this.bucketName = bucketName;
    this.db = getFirestoreDb();
    this.storage = getStorageInstance();
  }

  /**
   * Fetch influencer data from Influencers Club API
   * @param {string} filterValue - The value to search for (username, email, or URL)
   * @param {string} filterKey - The type of filter (username, email, or url)
   * @param {string} platform - The platform to fetch data from
   * @param {string|boolean} emailRequired - Email requirement (must_have, preferred, not_needed, true, false)
   * @param {boolean} postDataRequired - Whether to include post data
   * @returns {Object} - The raw API response
   */
  async fetchInfluencerData(filterValue, filterKey = "username", platform = "instagram", emailRequired = "must_have", postDataRequired = true) {
    // Validate platform is supported
    const supportedPlatforms = [
      "instagram", "youtube", "tiktok", "onlyfans",
      "twitter", "twitch", "discord", "facebook",
      "snapchat", "pinterest"
    ];

    if (!supportedPlatforms.includes(platform)) {
      console.warn(`Platform ${platform} may not be supported. Supported platforms: ${supportedPlatforms.join(', ')}`);
    }

    // Validate filter key
    const validFilterKeys = ["url", "email", "username"];
    if (!validFilterKeys.includes(filterKey)) {
      throw new Error(`Invalid filter_key: ${filterKey}. Must be one of: ${validFilterKeys.join(', ')}`);
    }

    // Validate email_required parameter
    const validEmailOptions = ["not_needed", "preferred", "must_have", true, false];
    if (!validEmailOptions.includes(emailRequired)) {
      console.warn(`Invalid email_required value: ${emailRequired}. Using default "must_have"`);
      emailRequired = "must_have";
    }

    // Prepare request
    const url = "https://api-dashboard.influencers.club/public/v1/enrichment/single_enrich/";
    const payload = {
      filter_value: filterValue,
      filter_key: filterKey,
      platform,
      email_required: emailRequired,
      post_data_required: postDataRequired
    };

    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };

    // Make request
    try {
      console.log(`Fetching data for ${filterValue} on ${platform} platform...`);
      const response = await axios.post(url, payload, { headers });
      return response.data;
    } catch (error) {
      // Handle errors
      if (error.response) {
        console.error(`Error ${error.response.status}: ${error.response.statusText}`);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        console.error('No response received from server:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }
      throw error;
    }
  }

  /**
   * Process raw influencer data into standardized format
   * @param {Object} rawData - The raw data from Influencers Club API
   * @param {string} influencerId - The influencer ID for caching images
   * @returns {Object} - The processed data in standardized format
   */
  async processRawData(rawData, influencerId) {
    // Initialize processed data object
    const processed = {
      profileInfo: {
        username: '',
        fullName: '',
        profileImageUrl: '',
        category: '',
        bio: '',
        email: rawData.email || '',
        location: rawData.location || '',
        speakingLanguage: rawData.speaking_language || '',
        isBusinessAccount: rawData.is_business || false,
        isCreator: rawData.is_creator || false,
        hasBrandDeals: rawData.has_brand_deals || false,
        hasLinkInBio: rawData.has_link_in_bio || false
      },
      influencerStats: {
        platforms: [],
        engagementRate: {
          value: '',
          description: ''
        },
        contentTypes: [],
        postingFrequency: 0,
        followerGrowth: {
          threeMonths: 0,
          sixMonths: 0,
          nineMonths: 0,
          twelveMonths: 0
        }
      },
      audienceInsights: {
        demographics: {
          age: [],
          gender: {}
        },
        locations: [],
        interests: []
      },
      recentPosts: [],
      brandMentions: [],
      partnerships: [],
      platformPresence: []
    };

    // Add platform presence information
    if (rawData.creator_has && Array.isArray(rawData.creator_has)) {
      processed.platformPresence = rawData.creator_has.map(platform => ({
        platform: platform.platform || '',
        url: platform.url || ''
      }));
    }

    // Track the main platform for engagement rate
    let mainPlatform = null;
    let highestEngagementRate = 0;

    // Process Instagram data - always process this first for visual analysis
    if (rawData.instagram) {
      const insta = rawData.instagram;

      // Extract profile info
      processed.profileInfo.username = insta.username || '';
      processed.profileInfo.fullName = insta.full_name || '';
      // Profile image URL will be set later when processing posts
      processed.profileInfo.profileImageUrl = '';
      processed.profileInfo.category = insta.niche_class ? insta.niche_class.join(' - ') : '';
      processed.profileInfo.bio = insta.biography || '';

      // Extract stats
      processed.influencerStats.platforms.push({
        name: 'Instagram',
        followers: insta.follower_count ? insta.follower_count.toLocaleString() : '0',
        engagement: insta.engagement_percent ? `${insta.engagement_percent}%` : '0%'
      });

      // Set Instagram as the initial main platform for engagement rate
      mainPlatform = 'Instagram';
      highestEngagementRate = insta.engagement_percent || 0;

      processed.influencerStats.engagementRate = {
        value: insta.engagement_percent ? `${insta.engagement_percent}%` : '0%',
        description: 'Instagram engagement rate'
      };

      // Extract content types
      const contentTypesSet = new Set();
      if (Array.isArray(insta.post_data)) {
        insta.post_data.forEach(post => {
          if (Array.isArray(post.media)) {
            post.media.forEach(mediaItem => {
              if (mediaItem.type) {
                contentTypesSet.add(mediaItem.type);
              }
            });
          }
        });
      }
      processed.influencerStats.contentTypes = Array.from(contentTypesSet);

      // Extract posting frequency
      processed.influencerStats.postingFrequency = insta.posting_frequency_recent_months || 0;

      // Extract follower growth
      if (insta.follower_growth || insta.creator_follower_growth) {
        const growth = insta.follower_growth || insta.creator_follower_growth;
        processed.influencerStats.followerGrowth = {
          threeMonths: growth.three_months_ago || 0,
          sixMonths: growth.six_months_ago || 0,
          nineMonths: growth.nine_months_ago || 0,
          twelveMonths: growth.twelve_months_ago || 0
        };
      }

      // Extract audience insights
      if (insta.audience_insights) {
        const insights = insta.audience_insights;

        // Demographics
        if (insights.age_distribution) {
          processed.audienceInsights.demographics.age = Object.entries(insights.age_distribution)
            .map(([range, percentage]) => ({ range, percentage }));
        }

        if (insights.gender_distribution) {
          processed.audienceInsights.demographics.gender = insights.gender_distribution;
        }

        // Locations
        if (insights.location_distribution) {
          processed.audienceInsights.locations = Object.entries(insights.location_distribution)
            .map(([location, percentage]) => ({ location, percentage }));
        }

        // Interests
        if (insights.interests) {
          processed.audienceInsights.interests = insights.interests;
        }
      }

      // Extract recent posts and store all post images
      processed.recentPosts = [];
      processed.allPostImages = []; // New field to store all post images

      if (Array.isArray(insta.post_data)) {
        // Cache profile picture with special flag
        processed.profileInfo.profileImageUrl = await cacheImage(
          insta.profile_picture_hd,
          influencerId,
          true // Mark as profile picture
        );

        // Process each post
        for (const post of insta.post_data) {
          const postId = parseInt(post.post_id, 10) || 0;
          const likes = post.engagement && post.engagement.likes != null ? post.engagement.likes.toLocaleString() : '0';
          const comments = post.engagement && post.engagement.comments != null ? post.engagement.comments.toLocaleString() : '0';
          const caption = post.caption || '';
          const timestamp = post.timestamp || null;

          // Process all media items in the post
          if (Array.isArray(post.media)) {
            let firstImageProcessed = false;

            for (const mediaItem of post.media) {
              if (mediaItem.type === 'image' && mediaItem.url) {
                // Cache the image
                const cachedImageUrl = await cacheImage(mediaItem.url, influencerId);

                // Add to allPostImages array with engagement metrics
                processed.allPostImages.push({
                  id: postId,
                  imageUrl: cachedImageUrl,
                  likes: likes,
                  comments: comments,
                  caption: caption,
                  timestamp: timestamp,
                  type: mediaItem.type
                });

                // Add to recentPosts only the first image of each post
                if (!firstImageProcessed) {
                  processed.recentPosts.push({
                    id: postId,
                    imageUrl: cachedImageUrl,
                    likes: likes,
                    comments: comments
                  });
                  firstImageProcessed = true;
                }
              }
            }
          }
        }
      }
    }

    // Process YouTube data
    if (rawData.youtube) {
      const yt = rawData.youtube;

      // Add to platforms
      processed.influencerStats.platforms.push({
        name: 'YouTube',
        followers: yt.subscriber_count ? yt.subscriber_count.toLocaleString() : '0',
        engagement: yt.engagement_percent ? `${yt.engagement_percent}%` : '0%'
      });

      // Check if YouTube has higher engagement rate
      if (yt.engagement_percent && yt.engagement_percent > highestEngagementRate) {
        mainPlatform = 'YouTube';
        highestEngagementRate = yt.engagement_percent;

        // Update the engagement rate to use YouTube's rate
        processed.influencerStats.engagementRate = {
          value: `${yt.engagement_percent}%`,
          description: 'YouTube engagement rate'
        };
      }

      // Add to recent posts if there's a latest video
      if (yt.latest_video) {
        const video = yt.latest_video;
        processed.recentPosts.push({
          id: video.video_id || '',
          platform: 'YouTube',
          title: video.title || '',
          views: video.views ? video.views.toLocaleString() : '0',
          likes: video.likes ? video.likes.toLocaleString() : '0',
          comments: video.comments ? video.comments.toLocaleString() : '0'
        });
      }
    }

    // Process TikTok data
    if (rawData.tiktok) {
      const tt = rawData.tiktok;

      // Add to platforms
      processed.influencerStats.platforms.push({
        name: 'TikTok',
        followers: tt.follower_count ? tt.follower_count.toLocaleString() : '0',
        engagement: tt.engagement_percent ? `${tt.engagement_percent}%` : '0%'
      });

      // Check if TikTok has higher engagement rate
      if (tt.engagement_percent && tt.engagement_percent > highestEngagementRate) {
        mainPlatform = 'TikTok';
        highestEngagementRate = tt.engagement_percent;

        // Update the engagement rate to use TikTok's rate
        processed.influencerStats.engagementRate = {
          value: `${tt.engagement_percent}%`,
          description: 'TikTok engagement rate'
        };
      }

      // Add to recent posts if there's a latest post
      if (tt.latest_post) {
        const post = tt.latest_post;
        processed.recentPosts.push({
          id: post.post_id || '',
          platform: 'TikTok',
          description: post.description || '',
          views: post.engagement && post.engagement.views ? post.engagement.views.toLocaleString() : '0',
          likes: post.engagement && post.engagement.likes ? post.engagement.likes.toLocaleString() : '0',
          comments: post.engagement && post.engagement.comments ? post.engagement.comments.toLocaleString() : '0'
        });
      }
    }

    // Process any other platforms that might be in the raw data
    Object.keys(rawData).forEach(platformKey => {
      // Skip non-platform keys and already processed platforms
      if (platformKey === 'instagram' || platformKey === 'youtube' || platformKey === 'tiktok' ||
          !rawData[platformKey] || typeof rawData[platformKey] !== 'object') {
        return;
      }

      const platformData = rawData[platformKey];
      const platformName = platformKey.charAt(0).toUpperCase() + platformKey.slice(1);

      // Add to platforms if it has follower count or subscriber count
      if (platformData.follower_count || platformData.subscriber_count) {
        const followerCount = platformData.follower_count || platformData.subscriber_count || 0;
        const engagementPercent = platformData.engagement_percent || 0;

        processed.influencerStats.platforms.push({
          name: platformName,
          followers: followerCount.toLocaleString(),
          engagement: engagementPercent ? `${engagementPercent}%` : '0%'
        });

        // Check if this platform has higher engagement rate
        if (engagementPercent && engagementPercent > highestEngagementRate) {
          mainPlatform = platformName;
          highestEngagementRate = engagementPercent;

          // Update the engagement rate to use this platform's rate
          processed.influencerStats.engagementRate = {
            value: `${engagementPercent}%`,
            description: `${platformName} engagement rate`
          };
        }
      }

      // Add to recent posts if there's a latest post
      if (platformData.latest_post) {
        const post = platformData.latest_post;
        processed.recentPosts.push({
          id: post.post_id || '',
          platform: platformName,
          description: post.description || '',
          views: post.engagement && post.engagement.views ? post.engagement.views.toLocaleString() : '0',
          likes: post.engagement && post.engagement.likes ? post.engagement.likes.toLocaleString() : '0',
          comments: post.engagement && post.engagement.comments ? post.engagement.comments.toLocaleString() : '0'
        });
      }
    });

    // Extract brand mentions and partnerships
    processed.brandMentions = this.extractBrandMentions(rawData);
    processed.partnerships = this.extractPartnerships(rawData);

    // Add a note about which platform's engagement rate is being used
    if (mainPlatform) {
      console.log(`Using ${mainPlatform} engagement rate (${highestEngagementRate}%) as the primary engagement rate`);
    }

    return processed;
  }

  /**
   * Extract brand mentions from raw data using deterministic pattern matching
   * This is a deterministic analysis that uses regular expressions to extract mentions
   * from captions, not an ML-based inference
   * @param {Object} rawData - The raw data from Influencers Club API
   * @returns {Array} - Array of brand mentions
   */
  extractBrandMentions(rawData) {
    const brandMentions = [];

    // Extract from Instagram posts
    if (rawData.instagram && rawData.instagram.post_data) {
      rawData.instagram.post_data.forEach(post => {
        if (post.caption && post.caption.includes('@')) {
          // Extract mentions from caption
          const mentions = post.caption.match(/@(\w+)/g);
          if (mentions) {
            mentions.forEach(mention => {
              const brandName = mention.substring(1); // Remove the @ symbol

              // Check if this brand is already in the list
              const existingBrand = brandMentions.find(brand => brand.name === brandName);
              if (existingBrand) {
                existingBrand.count++;
              } else {
                brandMentions.push({
                  name: brandName,
                  platform: 'Instagram',
                  count: 1
                });
              }
            });
          }
        }
      });
    }

    // Extract from TikTok posts
    if (rawData.tiktok && rawData.tiktok.latest_post) {
      const post = rawData.tiktok.latest_post;
      if (post.description && post.description.includes('@')) {
        // Extract mentions from description
        const mentions = post.description.match(/@(\w+)/g);
        if (mentions) {
          mentions.forEach(mention => {
            const brandName = mention.substring(1); // Remove the @ symbol

            // Check if this brand is already in the list
            const existingBrand = brandMentions.find(brand => brand.name === brandName);
            if (existingBrand) {
              existingBrand.count++;
            } else {
              brandMentions.push({
                name: brandName,
                platform: 'TikTok',
                count: 1
              });
            }
          });
        }
      }
    }

    return brandMentions;
  }

  /**
   * Extract partnerships from raw data using deterministic pattern matching
   * This is a deterministic analysis that uses regular expressions and string matching
   * to identify partnerships based on hashtags like #ad, #sponsored, etc.
   * Not an ML-based inference
   * @param {Object} rawData - The raw data from Influencers Club API
   * @returns {Array} - Array of partnerships
   */
  extractPartnerships(rawData) {
    const partnerships = [];

    // Extract from Instagram posts
    if (rawData.instagram && rawData.instagram.post_data) {
      rawData.instagram.post_data.forEach(post => {
        if (post.caption && (post.caption.includes('#ad') || post.caption.includes('#sponsored') || post.caption.includes('#partner'))) {
          // Extract mentions from caption
          const mentions = post.caption.match(/@(\w+)/g);
          if (mentions) {
            mentions.forEach(mention => {
              const brandName = mention.substring(1); // Remove the @ symbol

              // Check if this brand is already in the list
              const existingPartnership = partnerships.find(partnership => partnership.brand === brandName);
              if (!existingPartnership) {
                partnerships.push({
                  brand: brandName,
                  platform: 'Instagram',
                  type: post.caption.includes('#ad') ? 'Ad' : (post.caption.includes('#sponsored') ? 'Sponsored' : 'Partner'),
                  date: post.timestamp ? new Date(post.timestamp * 1000).toISOString().split('T')[0] : ''
                });
              }
            });
          }
        }
      });
    }

    return partnerships;
  }

  /**
   * Fetch, process, and store influencer data
   * @param {string} filterValue - The value to search for (username, email, or URL)
   * @param {string} filterKey - The type of filter (username, email, or url)
   * @param {string} platform - The platform to fetch data from
   * @param {Object} options - Additional options
   * @returns {Object} - The processed data and influencer ID
   */
  async enrichInfluencer(filterValue, filterKey = "username", platform = "instagram", options = {}) {
    // Set default options
    const defaultOptions = {
      emailRequired: "must_have",
      postDataRequired: true,
      updateFirestore: true,
      forceRefresh: false
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Determine the username for querying Firestore
    let username = filterValue;
    if (filterKey === "url") {
      // Extract username from URL if possible
      const urlMatch = filterValue.match(/([^\/]+)\/?$/);
      if (urlMatch) {
        username = urlMatch[1];
      }
    }

    // Check if we already have this influencer in Firestore
    let influencerRef, influencerSnapshot, influencerId;
    let useExistingData = false;
    let existingProcessedData = null;
    let existingProcessedStoragePath = null;
    let rawData = null; // Initialize rawData here to avoid the "Cannot access before initialization" error

    if (finalOptions.updateFirestore) {
      influencerRef = this.db.collection('influencers').where('username', '==', username).limit(1);
      influencerSnapshot = await influencerRef.get();

      if (influencerSnapshot.empty) {
        // Create a new influencer document
        const newInfluencerRef = this.db.collection('influencers').doc();
        influencerId = newInfluencerRef.id;
        console.log(`No existing influencer found for ${username}. Creating new entry with ID: ${influencerId}`);
      } else {
        // Use existing influencer document
        influencerId = influencerSnapshot.docs[0].id;
        console.log(`Found existing influencer for ${username} with ID: ${influencerId}`);

        // Check if we have recent data (less than 30 days old)
        if (!finalOptions.forceRefresh) {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

          // Query the raw_data subcollection for the most recent entry
          const rawDataRef = this.db.collection('influencers')
            .doc(influencerId)
            .collection('raw_data')
            .orderBy('pulled_at', 'desc')
            .limit(1);

          const rawDataSnapshot = await rawDataRef.get();

          if (!rawDataSnapshot.empty) {
            const rawDataDoc = rawDataSnapshot.docs[0].data();
            const pulledAt = rawDataDoc.pulled_at.toDate();

            // Check if the data is less than 30 days old
            if (pulledAt > thirtyDaysAgo) {
              console.log(`Found cached data for ${username} from ${pulledAt.toISOString()}, which is less than 30 days old`);
              useExistingData = true;
              const rawStoragePathFromDoc = rawDataDoc.storage_path;
              existingProcessedStoragePath = rawDataDoc.processed_storage_path;

              // Get the raw data from Cloud Storage
              let existingRawData = null;
              try {
                if (rawStoragePathFromDoc) {
                  const rawFile = this.storage.bucket(this.bucketName).file(rawStoragePathFromDoc);
                  const [rawContents] = await rawFile.download();
                  existingRawData = JSON.parse(rawContents.toString('utf8'));
                  console.log(`Successfully loaded cached raw data for ${username}`);
                } else {
                  console.warn(`No raw storage path found for ${username}. Will not be able to update Firestore document.`);
                }
              } catch (error) {
                console.error(`Error loading cached raw data: ${error.message}`);
                // Continue even if raw data can't be loaded
              }

              // Get the processed data from Cloud Storage
              try {
                const file = this.storage.bucket(this.bucketName).file(existingProcessedStoragePath);
                const [contents] = await file.download();
                existingProcessedData = JSON.parse(contents.toString('utf8'));
                console.log(`Successfully loaded cached processed data for ${username}`);

                // Store the raw data for later use
                if (existingRawData) {
                  rawData = existingRawData;
                  console.log(`Successfully stored cached raw data for ${username} for reuse`);
                }
              } catch (error) {
                console.error(`Error loading cached processed data: ${error.message}`);
                useExistingData = false;
              }
            } else {
              console.log(`Found data for ${username} from ${pulledAt.toISOString()}, but it's older than 30 days. Will fetch fresh data.`);
            }
          } else {
            console.log(`No raw data records found for influencer ${username} with ID ${influencerId}`);
          }
        } else {
          console.log(`Force refresh requested for ${username}. Will fetch fresh data regardless of cache.`);
        }
      }
    } else {
      // Generate a temporary ID if not updating Firestore
      influencerId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
      console.log(`Not updating Firestore. Using temporary ID: ${influencerId}`);
    }

    // processedData is declared here, rawData was already declared above
    let processedData;
    let rawStoragePath = '';
    let processedStoragePath = '';
    const timestamp = Date.now();

    // If we have recent data and aren't forcing a refresh, use it
    if (useExistingData && existingProcessedData) {
      console.log(`Using cached data for ${username} (less than 30 days old)`);
      processedData = existingProcessedData;
      processedStoragePath = existingProcessedStoragePath;
      console.log(`Successfully reused cached data for ${username} - no API call needed`);
    } else {
      // Fetch fresh data from Influencers Club
      console.log(`Fetching fresh data for ${username} from Influencers Club API`);

      // Always fetch Instagram data for visual analysis
      let instagramRawData = null;
      let originalPlatformRawData = null;

      // If the requested platform is not Instagram, we need to fetch both
      if (platform !== "instagram") {
        console.log(`Requested platform is ${platform}, but we also need Instagram data for visual analysis`);

        // First fetch the data for the requested platform
        console.log(`Fetching data for ${username} on ${platform} platform...`);
        originalPlatformRawData = await this.fetchInfluencerData(
          filterValue,
          filterKey,
          platform,
          finalOptions.emailRequired,
          finalOptions.postDataRequired
        );

        // Then fetch Instagram data
        console.log(`Fetching Instagram data for ${username} for visual analysis...`);
        instagramRawData = await this.fetchInfluencerData(
          filterValue,
          filterKey,
          "instagram",
          finalOptions.emailRequired,
          finalOptions.postDataRequired
        );

        // Merge the data, with Instagram data as the base but preserving platform-specific engagement metrics
        rawData = instagramRawData;

        // Store the original platform data in the rawData object
        rawData[platform] = originalPlatformRawData[platform];

        console.log(`Successfully merged ${platform} and Instagram data for ${username}`);
      } else {
        // If the requested platform is Instagram, just fetch Instagram data
        rawData = await this.fetchInfluencerData(
          filterValue,
          filterKey,
          platform,
          finalOptions.emailRequired,
          finalOptions.postDataRequired
        );
      }

      // Store raw data in Cloud Storage if updating Firestore
      if (finalOptions.updateFirestore) {
        rawStoragePath = `influencers/${influencerId}/raw_data/influencers_club_${timestamp}.json`;
        await this.storage.bucket(this.bucketName).file(rawStoragePath).save(JSON.stringify(rawData), {
          metadata: { contentType: 'application/json' }
        });
        console.log(`Raw data stored at: ${rawStoragePath}`);
      }

      // Process the raw data
      console.log(`Processing raw data for ${username}`);
      processedData = await this.processRawData(rawData, influencerId);

      // Store processed data in Cloud Storage if updating Firestore
      if (finalOptions.updateFirestore) {
        processedStoragePath = `influencers/${influencerId}/processed_data/influencers_club_${timestamp}.json`;
        await this.storage.bucket(this.bucketName).file(processedStoragePath).save(JSON.stringify(processedData), {
          metadata: { contentType: 'application/json' }
        });
        console.log(`Processed data stored at: ${processedStoragePath}`);
      }
    }

    // Update Firestore with the latest data if requested
    if (finalOptions.updateFirestore) {
      // Prepare the base influencer data with appropriate fallbacks
      const influencerData = {
        username: processedData.profileInfo.username || username || '',
        full_name: processedData.profileInfo.fullName || 'Unknown Name',
        last_updated: Timestamp.now()
      };

      // Add raw data fields if available with appropriate fallbacks
      if (rawData) {
        influencerData.email = rawData.email || 'No Email Available';
        influencerData.location = rawData.location || 'Location Unknown';
        influencerData.speaking_language = rawData.speaking_language || 'en';
        influencerData.has_brand_deals = rawData.has_brand_deals !== undefined ? rawData.has_brand_deals : false;
        influencerData.has_link_in_bio = rawData.has_link_in_bio !== undefined ? rawData.has_link_in_bio : false;
        influencerData.is_business = rawData.is_business !== undefined ? rawData.is_business : false;
        influencerData.is_creator = rawData.is_creator !== undefined ? rawData.is_creator : false;
        influencerData.creator_has = Array.isArray(rawData.creator_has) ? rawData.creator_has : [];
      } else {
        // Set default values if no raw data is available
        influencerData.email = 'No Email Available';
        influencerData.location = 'Location Unknown';
        influencerData.speaking_language = 'en';
        influencerData.has_brand_deals = false;
        influencerData.has_link_in_bio = false;
        influencerData.is_business = false;
        influencerData.is_creator = false;
        influencerData.creator_has = [];
      }

      // Initialize platforms object
      influencerData.platforms = {};

      // Add platform-specific data if raw data is available
      if (rawData) {
        // Always add Instagram data if available (needed for visual analysis)
        if (rawData.instagram) {
          influencerData.platforms.instagram = {
            username: rawData.instagram.username || username || '',
            follower_count: rawData.instagram.follower_count || 0,
            engagement_percent: rawData.instagram.engagement_percent || 0,
            biography: rawData.instagram.biography || 'No Instagram Bio Available',
            profile_image_url: rawData.instagram.profile_picture_hd || '',
            niches: rawData.instagram.niches || {
              primary: 'Uncategorized',
              secondary: []
            },
            hashtags: Array.isArray(rawData.instagram.hashtags) ? rawData.instagram.hashtags : [],
            posting_frequency_recent_months: rawData.instagram.posting_frequency_recent_months || 0,
            follower_growth: rawData.instagram.follower_growth || rawData.instagram.creator_follower_growth || {
              three_months_ago: 0,
              six_months_ago: 0,
              nine_months_ago: 0,
              twelve_months_ago: 0
            }
          };
        }

        // Add YouTube data if available
        if (rawData.youtube) {
          influencerData.platforms.youtube = {
            username: rawData.youtube.username || username || '',
            subscriber_count: rawData.youtube.subscriber_count || 0,
            engagement_percent: rawData.youtube.engagement_percent || 0,
            description: rawData.youtube.description || 'No YouTube Description Available',
            profile_image_url: rawData.youtube.profile_picture || ''
          };
        }

        // Add TikTok data if available
        if (rawData.tiktok) {
          influencerData.platforms.tiktok = {
            username: rawData.tiktok.username || username || '',
            follower_count: rawData.tiktok.follower_count || 0,
            engagement_percent: rawData.tiktok.engagement_percent || 0,
            bio: rawData.tiktok.biography || 'No TikTok Bio Available', // Use biography field with meaningful fallback
            profile_image_url: rawData.tiktok.profile_picture || '',
            category: rawData.tiktok.category || 'Uncategorized',
            average_likes: rawData.tiktok.average_likes || 0,
            niches: rawData.tiktok.niches || {
              primary: 'Uncategorized',
              secondary: []
            },
            hashtags: Array.isArray(rawData.tiktok.hashtags) ? rawData.tiktok.hashtags : [],
            follower_growth: rawData.tiktok.follower_growth || rawData.tiktok.creator_follower_growth || {
              three_months_ago: 0,
              six_months_ago: 0,
              nine_months_ago: 0,
              twelve_months_ago: 0
            },
            has_tiktok_shop: rawData.tiktok.has_tiktok_shop !== undefined ? rawData.tiktok.has_tiktok_shop : false
          };
        }

        // Add data for any other platforms that might be in the raw data
        // This ensures we capture data from all platforms, not just the predefined ones
        Object.keys(rawData).forEach(platformKey => {
          // Skip non-platform keys and already processed platforms
          if (platformKey === 'instagram' || platformKey === 'youtube' || platformKey === 'tiktok' ||
              !rawData[platformKey] || typeof rawData[platformKey] !== 'object') {
            return;
          }

          // Add this platform's data
          const platformData = rawData[platformKey];
          influencerData.platforms[platformKey] = {
            username: platformData.username || username || '',
            follower_count: platformData.follower_count || platformData.subscriber_count || 0,
            engagement_percent: platformData.engagement_percent || 0,
            bio: platformData.biography || platformData.description || `No ${platformKey.charAt(0).toUpperCase() + platformKey.slice(1)} Bio Available`,
            profile_image_url: platformData.profile_picture || platformData.profile_picture_hd || '',
            // Add any other platform-specific fields that might be available
            niches: platformData.niches || {
              primary: 'Uncategorized',
              secondary: []
            },
            hashtags: Array.isArray(platformData.hashtags) ? platformData.hashtags : []
          };

          console.log(`Added data for platform: ${platformKey}`);
        });
      } else {
        console.log(`No raw data available for ${username}. Skipping platform-specific updates.`);
      }

      // Add or update the influencer document
      if (influencerSnapshot.empty) {
        influencerData.created_at = Timestamp.now();
        await this.db.collection('influencers').doc(influencerId).set(influencerData);
      } else {
        await this.db.collection('influencers').doc(influencerId).update(influencerData);
      }

      // Add a record of this data pull if we fetched fresh data
      if (!useExistingData) {
        await this.db.collection('influencers').doc(influencerId).collection('raw_data').add({
          source: 'influencers_club',
          storage_path: rawStoragePath,
          processed_storage_path: processedStoragePath,
          pulled_at: Timestamp.now(),
          processed_at: Timestamp.now()
        });
        console.log(`Added record of data pull for ${username} to Firestore`);
      }
    }

    return {
      influencerId,
      processedData
    };
  }
}

export default InfluencersClubConnector;


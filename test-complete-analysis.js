// test-complete-analysis.js
// Test script for the complete analysis endpoint

import fetch from 'node-fetch';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Get the directory name
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration
const API_URL = process.env.API_URL || 'https://palas-influencer-intelligence-9815718377.us-central1.run.app';
const CLIENT_ID = 'test_client';
const CAMPAIGN_ID = 'test_campaign_1';
const PLATFORM = 'instagram';

// Get username from command line arguments
const username = process.argv[2];

if (!username) {
  console.error('Please provide a username as a command line argument');
  console.error('Usage: node test-complete-analysis.js <username>');
  process.exit(1);
}

console.log(`Testing complete analysis for influencer: ${username}`);
console.log(`API URL: ${API_URL}`);
console.log(`Client ID: ${CLIENT_ID}`);
console.log(`Campaign ID: ${CAMPAIGN_ID}`);
console.log(`Platform: ${PLATFORM}`);

// Function to test the complete analysis endpoint
async function testCompleteAnalysis() {
  try {
    console.log('\n--- Starting Complete Analysis Test ---\n');
    
    const requestBody = {
      username,
      clientId: CLIENT_ID,
      campaignId: CAMPAIGN_ID,
      platform: PLATFORM,
      forceRefresh: false
    };
    
    console.log('Request body:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch(`${API_URL}/api/influencers/complete-analysis`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }
    
    const result = await response.json();
    
    console.log('\n--- Complete Analysis Result ---\n');
    console.log(JSON.stringify(result, null, 2));
    
    // Save the result to a file
    const outputPath = path.join(__dirname, `complete_analysis_${username}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
    console.log(`\nResult saved to: ${outputPath}`);
    
    return result;
  } catch (error) {
    console.error('Error testing complete analysis:', error);
    throw error;
  }
}

// Run the test
(async () => {
  try {
    await testCompleteAnalysis();
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('\nTest failed:', error);
    process.exit(1);
  }
})();

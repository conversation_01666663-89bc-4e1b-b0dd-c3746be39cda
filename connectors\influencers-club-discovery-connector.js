// influencers-club-discovery-connector.js
// Influencers Club Discovery API connector

import axios from 'axios';
import { getFirestoreDb } from '../config/firebase-config.js';
import { getStorageInstance } from '../config/firebase-config.js';
import { DEFAULT_BUCKET_NAME, INFLUENCERS_API_KEY } from '../config/constants.js';
import { Timestamp, FieldValue } from 'firebase-admin/firestore';

/**
 * Influencers Club Discovery API connector
 */
class InfluencersClubDiscoveryConnector {
  /**
   * Create a new Influencers Club Discovery connector
   * @param {string} apiKey - The Influencers Club API key
   * @param {string} bucketName - The storage bucket name
   */
  constructor(apiKey = INFLUENCERS_API_KEY, bucketName = DEFAULT_BUCKET_NAME) {
    this.apiKey = apiKey;
    this.bucketName = bucketName;
    this.db = getFirestoreDb();
    this.storage = getStorageInstance();

    // Debug logging for constructor
    console.log(`Connector initialized with API key first 10 chars: ${this.apiKey ? this.apiKey.substring(0, 10) : 'undefined'}...`);
    console.log(`API key length: ${this.apiKey ? this.apiKey.length : 'N/A'}`);
    console.log(`API key type: ${typeof this.apiKey}`);
  }

  /**
   * Discover influencers using the Discovery API
   * @param {Object} params - The discovery parameters
   * @returns {Object} - The discovery results
   */
  async discoverInfluencers(params) {
    const url = "https://api-dashboard.influencers.club/public/v1/discovery/";

    // Prepare request payload
    const payload = {
      platform: params.main_platform || "instagram",
      paging: {
        limit: params.limit || 10, // Default to 10 for production, can be overridden
        page: params.page || 1
      },
      sort: {
        sort_by: "relevancy",
        sort_order: "desc"  // Must be lowercase 'asc' or 'desc' according to API validation
      },
      filters: {
        // Primary search parameter
        ai_search: params.influencer_description,

        // Secondary parameters
        number_of_followers: {
          min: params.min_follower_count || 10000,
          max: params.max_follower_count || null
        }
      }
    };

    // Add optional filters if provided
    if (params.min_likes) {
      payload.filters.average_likes = {
        min: params.min_likes,
        max: null
      };
    }

    if (params.min_comments) {
      payload.filters.average_comments = {
        min: params.min_comments,
        max: null
      };
    }

    if (params.min_views) {
      payload.filters.average_views = {
        min: params.min_views,
        max: null
      };
    }

    if (params.location) {
      payload.filters.location = params.location;
    }

    if (params.gender) {
      payload.filters.gender = params.gender;
    }

    // Debug logging for API key
    console.log(`API Key first 10 chars: ${this.apiKey ? this.apiKey.substring(0, 10) : 'undefined'}...`);
    console.log(`API Key length: ${this.apiKey ? this.apiKey.length : 'N/A'}`);

    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };

    // Debug logging for headers
    console.log(`Authorization header first 17 chars: Bearer ${this.apiKey ? this.apiKey.substring(0, 10) : 'undefined'}...`);
    console.log(`Content-Type header: ${headers['Content-Type']}`);


    // Make request
    try {
      console.log(`Discovering influencers with AI search: "${params.influencer_description.substring(0, 50)}..."`);

      // Debug logging for request
      console.log(`Making request to: ${url}`);
      console.log(`Request payload: ${JSON.stringify(payload, null, 2).substring(0, 200)}...`);

      // Try to make the request with more detailed error handling
      let response;
      try {
        response = await axios.post(url, payload, { headers });
      } catch (requestError) {
        console.error('Detailed request error:', requestError.message);
        if (requestError.code) {
          console.error('Error code:', requestError.code);
        }
        if (requestError.config) {
          console.error('Request config:', JSON.stringify({
            url: requestError.config.url,
            method: requestError.config.method,
            headers: {
              'Content-Type': requestError.config.headers['Content-Type'],
              'Authorization': 'Bearer [REDACTED]' // Don't log the full token
            }
          }, null, 2));
        }
        throw requestError;
      }

      // Transform the API response to match expected structure
      const apiResponse = response.data;

      // Log the full raw API response
      console.log(`Raw API response from Discovery API: ${JSON.stringify(apiResponse)}`);

      // If the response has 'accounts' but not 'similar_accounts', transform it
      // We no longer need to transform accounts to similar_accounts
      // as we'll use the accounts directly in the discovery service
      if (apiResponse.accounts) {
        console.log(`Processing API response: Found ${apiResponse.accounts.length} accounts`);

        // Add discovery context to each account for reference
        apiResponse.accounts.forEach(account => {
          // Add discovery context directly to the account
          account.ai_search_used = params.influencer_description;
          account.discovery_params = {
            platform: params.main_platform || 'instagram',
            min_follower_count: params.min_follower_count || 10000,
            max_follower_count: params.max_follower_count || null,
            min_engagement_rate: params.min_engagement_rate || null,
            limit: params.limit || 10, // Default to 10 for production
            page: params.page || 1
          };
        });
      }

      return apiResponse;
    } catch (error) {
      // Handle errors
      if (error.response) {
        console.error(`Error ${error.response.status}: ${error.response.statusText}`);
        console.error('Error response data:', JSON.stringify(error.response.data, null, 2));

        // Detailed error inspection for validation errors
        if (error.response.data && Array.isArray(error.response.data)) {
          error.response.data.forEach((errorItem, index) => {
            console.error(`Error item ${index + 1}:`, JSON.stringify(errorItem, null, 2));

            // If there are nested objects, inspect them
            if (errorItem.error && Array.isArray(errorItem.error)) {
              errorItem.error.forEach((nestedError, nestedIndex) => {
                console.error(`  Nested error ${nestedIndex + 1}:`, JSON.stringify(nestedError, null, 2));
              });
            }
          });
        }
      } else if (error.request) {
        console.error('No response received from server:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }
      throw error;
    }
  }

  /**
   * Store discovery results in Firestore
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {Object} results - The discovery results
   * @param {string} influencerDescription - The influencer description used for AI search
   * @returns {string} - The discovery document ID
   */
  async storeDiscoveryResults(clientId, campaignId, results, influencerDescription) {
    // Validate client ID
    if (!clientId) {
      throw new Error('Client ID is required for storing discovery results');
    }

    // Store discovery results in Firestore
    const discoveryRef = this.db.collection('clients').doc(clientId)
      .collection('campaigns').doc(campaignId)
      .collection('influencer_discovery').doc();

    // Prepare the document data
    const discoveryData = {
      campaign_id: campaignId,
      client_id: clientId,
      timestamp: Timestamp.now(),
      influencer_description: influencerDescription,
      results: results,
      // Store additional metadata
      credits_left: results.credits_left,
      total_available: results.total || 0,
      limit: results.limit || 0
    };

    // Log what's being stored in Firestore
    console.log(`Storing discovery results in Firestore: ${JSON.stringify(discoveryData)}`);

    await discoveryRef.set(discoveryData);

    // Also store individual influencers
    // Check if we have similar_accounts (mapped from accounts) or need to use accounts directly
    const influencers = results.similar_accounts ||
      (results.accounts ? results.accounts.map(account => ({
        username: account.profile.username,
        full_name: account.profile.full_name,
        platform: 'instagram', // Default to Instagram if not specified
        follower_count: account.profile.followers,
        engagement_percent: account.profile.engagement_percent,
        profile_picture: account.profile.picture,
        profile_image_url: account.profile.picture, // Add profile_image_url for OpenAI schema
        user_id: account.user_id,
        ai_search_used: influencerDescription
      })) : []);

    // Store each influencer and create campaign associations
    for (const influencer of influencers) {
      const influencerId = await this.storeInfluencer(influencer);

      // Create influencer-campaign association
      await this.createInfluencerCampaignAssociation(clientId, campaignId, influencerId, influencer);
    }

    return discoveryRef.id;
  }

  /**
   * Create an association between an influencer and a campaign
   * @param {string} clientId - The client ID
   * @param {string} campaignId - The campaign ID
   * @param {string} influencerId - The influencer ID
   * @param {Object} influencerData - The influencer data
   * @returns {string} - The association document ID
   */
  async createInfluencerCampaignAssociation(clientId, campaignId, influencerId, influencerData) {
    // Create a unique ID for the association
    const associationRef = this.db.collection('influencer_campaign_associations')
      .doc(`${clientId}_${campaignId}_${influencerId}`);

    // Prepare association data
    const associationData = {
      client_id: clientId,
      campaign_id: campaignId,
      influencer_id: influencerId,
      username: influencerData.username,
      platform: influencerData.platform || 'instagram',
      status: 'discovered',
      discovery_date: Timestamp.now(),
      // Include key metrics for quick access
      follower_count: influencerData.follower_count,
      engagement_percent: influencerData.engagement_percent
    };

    // Store the association
    await associationRef.set(associationData);

    return associationRef.id;
  }

  /**
   * Store individual influencer data
   * @param {Object} influencerData - The influencer data
   * @returns {string} - The influencer document ID
   */
  async storeInfluencer(influencerData) {
    // Check if influencer already exists
    const influencerRef = this.db.collection('influencers')
      .where('username', '==', influencerData.username)
      .limit(1);

    const snapshot = await influencerRef.get();

    if (snapshot.empty) {
      // Create new influencer document
      const newInfluencerRef = this.db.collection('influencers').doc();

      // Ensure platform is always a valid string
      const platform = influencerData.platform || 'instagram';

      // Use a single source for profile picture URL
      const profilePictureUrl = influencerData.profile_image_url ||
                               influencerData.profile_picture ||
                               '';

      // Prepare influencer data
      const data = {
        username: influencerData.username,
        full_name: influencerData.full_name || '',
        // Add platform-specific username structure
        platform_usernames: {
          [platform]: influencerData.username
        },
        platforms: {
          [platform]: {
            follower_count: influencerData.follower_count,
            engagement_percent: influencerData.engagement_percent
          }
        },
        niches: influencerData.niches || [],
        created_at: Timestamp.now(),
        last_updated: Timestamp.now(),
        discovery_count: 1,
        // Store additional information with consistent profile picture URLs
        profile_picture: profilePictureUrl,
        profile_image_url: profilePictureUrl,
        user_id: influencerData.user_id || '',
        // Store discovery information
        discovery_info: {
          ai_search_used: influencerData.ai_search_used || '',
          discovery_params: influencerData.discovery_params || {}
        }
      };

      await newInfluencerRef.set(data);
      return newInfluencerRef.id;
    } else {
      // Update existing influencer document
      const docId = snapshot.docs[0].id;
      const docData = snapshot.docs[0].data();

      // Ensure platform is always a valid string
      const platform = influencerData.platform || 'instagram';

      // Use a single source for profile picture URL
      const profilePictureUrl = influencerData.profile_image_url ||
                               influencerData.profile_picture ||
                               docData.profile_image_url ||
                               docData.profile_picture ||
                               '';

      // Update platform data if needed
      const platforms = docData.platforms || {};
      platforms[platform] = {
        follower_count: influencerData.follower_count,
        engagement_percent: influencerData.engagement_percent
      };

      // Merge niches
      const niches = new Set([...(docData.niches || []), ...(influencerData.niches || [])]);

      // Update platform-specific usernames
      const platformUsernames = { ...(docData.platform_usernames || {}) };
      platformUsernames[platform] = influencerData.username;

      // Prepare update data
      const updateData = {
        platforms,
        platform_usernames: platformUsernames,
        niches: Array.from(niches),
        last_updated: Timestamp.now(),
        discovery_count: (docData.discovery_count || 0) + 1,
        // Update profile pictures with consistent values
        profile_picture: profilePictureUrl,
        profile_image_url: profilePictureUrl,
        // Update user_id if available
        user_id: influencerData.user_id || docData.user_id || ''
      };

      // Add discovery info if available
      if (influencerData.ai_search_used) {
        // Store the latest discovery information
        updateData.discovery_info = {
          ai_search_used: influencerData.ai_search_used,
          discovery_params: influencerData.discovery_params || {},
          last_discovery_date: Timestamp.now()
        };
      }

      await this.db.collection('influencers').doc(docId).update(updateData);

      return docId;
    }
  }
}

export default InfluencersClubDiscoveryConnector;

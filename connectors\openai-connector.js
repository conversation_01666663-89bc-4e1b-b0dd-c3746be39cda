// openai-connector.js
// OpenAI API connector

import OpenAI from 'openai';
import { OPENAI_API_KEY, PRICING_RATES } from '../config/constants.js';
import { extractJSON } from '../utils/string-utils.js';

/**
 * OpenAI API connector
 * Supports both Assistants API and Responses API
 */
class OpenAIConnector {
  /**
   * Create a new OpenAI connector
   * @param {string} apiKey - The OpenAI API key
   */
  constructor(apiKey = OPENAI_API_KEY) {
    this.client = new OpenAI({ apiKey });
    this.pricingRates = PRICING_RATES;
  }

  /**
   * Process a request using the Responses API with web search
   * @param {string} model - The model to use (e.g., "gpt-4.1")
   * @param {string} instructions - The system instructions
   * @param {string} prompt - The user prompt
   * @param {boolean} useWebSearch - Whether to use web search
   * @param {string} format - The output format (e.g., "json_object")
   * @returns {Object} - The processed result
   */
  async processWithResponses(model, instructions, prompt, useWebSearch = true, format = "json_object") {
    try {
      console.log(`Processing with Responses API using model ${model}...`);

      // Configure the request
      const requestOptions = {
        model: model,
        instructions: instructions,
        text: {
          format: {
            type: format
          }
        },
        input: prompt
      };

      // Add web search tool if requested
      if (useWebSearch) {
        requestOptions.tools = [{
          type: "web_search_preview",
          search_context_size: "high" // Use high context size for comprehensive research
        }];
      }

      // Make the request
      const response = await this.client.responses.create(requestOptions);

      // Extract the JSON response
      let parsedResponse;

      // Check if the response has output_text
      if (response.output_text) {
        try {
          parsedResponse = JSON.parse(response.output_text);
        } catch (error) {
          console.error('Error parsing JSON from response.output_text:', error);
          // Try to extract JSON from the text using our utility function
          parsedResponse = extractJSON(response.output_text);
        }
      } else if (response.output && response.output.length > 0) {
        // Find the message in the output
        const messageOutput = response.output.find(item => item.type === "message");
        if (messageOutput && messageOutput.content && messageOutput.content.length > 0) {
          const textContent = messageOutput.content.find(content => content.type === "output_text");
          if (textContent && textContent.text) {
            parsedResponse = extractJSON(textContent.text);
          }
        }
      }

      if (!parsedResponse) {
        throw new Error('Failed to extract JSON from the response');
      }

      return parsedResponse;
    } catch (error) {
      console.error('Error processing with Responses API:', error);
      throw error;
    }
  }

  /**
   * Process an agent with a prompt
   * @param {string} agentID - The agent ID
   * @param {string} prompt - The prompt
   * @returns {Object} - The processed result
   */
  async processAgent(agentID, prompt) {
    let attempt = 0;

    while (true) {
      attempt++;
      console.log(`Starting attempt ${attempt}...`);

      // Create a new thread and send the prompt as a user message
      const thread = await this.client.beta.threads.create();
      await this.client.beta.threads.messages.create(thread.id, {
        role: "user",
        content: prompt,
      });

      // Start the run with the specified assistant
      const run = await this.client.beta.threads.runs.create(thread.id, {
        assistant_id: agentID,
      });

      let cancelled = false;

      // Create a timeout promise that cancels the run after 120 seconds
      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => {
          // Cancel the run and mark as cancelled
          this.client.beta.threads.runs.cancel(thread.id, run.id).catch(() => {});
          cancelled = true;
          resolve("timeout");
        }, 120000);
      });

      // Poll for the run's completion status every 3 seconds
      const pollPromise = (async () => {
        let runStatus = await this.client.beta.threads.runs.retrieve(thread.id, run.id);
        while (runStatus.status !== "completed" && !cancelled) {
          await new Promise((resolve) => setTimeout(resolve, 3000));
          runStatus = await this.client.beta.threads.runs.retrieve(thread.id, run.id);
          console.log(`Run status: ${runStatus.status}`);
        }
        return "completed";
      })();

      // Race between the polling and the timeout
      const outcome = await Promise.race([timeoutPromise, pollPromise]);

      if (outcome === "completed") {
        // Retrieve messages from the thread
        const messagesResponse = await this.client.beta.threads.messages.list(thread.id);
        const assistantMessages = messagesResponse.data.filter(msg => msg.role === "assistant");

        if (assistantMessages.length === 0) {
          throw new Error("No assistant response found");
        }

        // Assume the last assistant message is the desired reply
        const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
        const assistantReply = lastAssistantMessage.content[0].text.value;

        // Attempt to parse the assistant's reply as JSON
        let parsedReply;
        try {
          parsedReply = JSON.parse(assistantReply);
          console.log("Parsed reply:", JSON.stringify(parsedReply));
        } catch (error) {
          console.error("Error parsing JSON from assistant:", error);
          throw new Error("Assistant responded with invalid JSON");
        }

        return parsedReply;
      } else {
        console.log(`Attempt ${attempt} timed out. Resubmitting the call to OpenAI...`);
        // Loop will retry by creating a new thread and resubmitting the prompt.
      }
    }
  }

  /**
   * Parse a completion from OpenAI
   * @param {string} model - The model to use
   * @param {Array} messages - The messages
   * @param {number} maxCompletionTokens - The maximum number of tokens to generate
   * @returns {Object} - The parsed completion
   */
  async parseCompletion(model, messages, maxCompletionTokens = 16000) {
    let attempt = 1;
    const maxAttempts = 10;

    while (attempt <= maxAttempts) {
      try {
        const completionResults = await this.client.beta.chat.completions.parse({
          model: model,
          messages: messages,
          max_completion_tokens: maxCompletionTokens,
        });

        const contentString = completionResults.choices[0].message.content;
        const parsedJSON = extractJSON(contentString);

        if (parsedJSON !== null) {
          console.log(`Success on attempt ${attempt}`);
          return parsedJSON;
        }

        console.log(`Attempt ${attempt} returned null. Retrying...`);
        attempt++;
      } catch (error) {
        console.error(`Error on attempt ${attempt}:`, error);

        // If we've reached the maximum number of attempts, throw the error
        if (attempt >= maxAttempts) {
          throw error;
        }

        attempt++;
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error(`Failed to parse completion after ${maxAttempts} attempts`);
  }
}

export default OpenAIConnector;

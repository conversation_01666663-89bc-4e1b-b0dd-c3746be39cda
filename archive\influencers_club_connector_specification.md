# Influencers Club Connector Specification

## Overview

This document provides a detailed specification for the Influencers Club connector, which is responsible for fetching, processing, and storing influencer data from the Influencers Club API. The connector must capture ALL relevant data from the API to ensure comprehensive influencer analysis.

## API Endpoint

The connector will use the `/public/v1/enrichment/single_enrich/` endpoint to fetch detailed influencer data.

## Data Capture Requirements

Based on the Influencers Club API documentation, the connector must capture the following data:

### General Information

| Field | Type | Description |
|-------|------|-------------|
| `email` | String | Influencer's email (if available and requested) |
| `location` | String | City, country (if available) |
| `speaking_language` | String | Detected primary language |
| `first_name` | String | First name (if publicly available) |
| `has_brand_deals` | Boolean | Indication that the creator has worked with brands |
| `has_link_in_bio` | Boolean | Whether there's a link in the creator's bio |
| `is_business` | Boolean | Whether the profile is marked as a business |
| `is_creator` | Boolean | Whether the profile belongs to a creator |

### Instagram Insights

| Field | Type | Description |
|-------|------|-------------|
| `username` | String | Official Instagram handle |
| `follower_count` | Number | Current number of followers |
| `biography` | String | Self-description, often includes brand collaborations |
| `engagement_percent` | Number | Percentage of audience interacting with posts |
| `niches.primary` | String | Primary niche classification |
| `niches.secondary` | Array<String> | Secondary niche classifications |
| `hashtags` | Array<String> | Commonly used hashtags |
| `posting_frequency_recent_months` | Number | How often they post content |
| `follower_growth` | Object | Growth history over past 3, 6, 9, and 12 months |
| `latest_post` | Object | Post data including caption, engagement, media type, hashtags |
| `latest_post.location` | Object | Geotagged location data |
| `latest_post.tagged_users` | Array<String> | Users tagged in posts |

### YouTube Insights

| Field | Type | Description |
|-------|------|-------------|
| `title` | String | Channel title |
| `description` | String | Channel description |
| `subscriber_count` | Number | Number of subscribers |
| `niches.primary` | String | Primary niche classification |
| `niches.secondary` | Array<String> | Secondary niche classifications |
| `video_count` | Number | Total number of videos |
| `average_views` | Number | Average views per video |
| `engagement_percent` | Number | Likes, comments relative to views |
| `hashtags` | Array<String> | Commonly used hashtags |
| `is_monetized` | Boolean | Whether the channel is eligible for ads |
| `has_community_posts` | Boolean | Whether the channel has community posts |
| `follower_growth` | Object | Growth history over past 3, 6, 9, and 12 months |
| `latest_video` | Object | Video data including title, engagement, topic categories |

### TikTok Insights

| Field | Type | Description |
|-------|------|-------------|
| `username` | String | Official TikTok handle |
| `biography` | String | Self-description |
| `category` | String | TikTok's classification of content type |
| `follower_count` | Number | Number of followers |
| `niches.primary` | String | Primary niche classification |
| `niches.secondary` | Array<String> | Secondary niche classifications |
| `average_likes` | Number | Average likes per video |
| `engagement_percent` | Number | How engaged the audience is |
| `hashtags` | Array<String> | Commonly used hashtags |
| `follower_growth` | Object | Growth history over past 3, 6, 9, and 12 months |
| `latest_post` | Object | Post data including caption, engagement, media |
| `has_tiktok_shop` | Boolean | Whether the creator uses TikTok's shopping feature |

### Platform Presence

| Field | Type | Description |
|-------|------|-------------|
| `creator_has` | Array<Object> | List of all social media platforms for the creator |

## Connector Interface

The connector should provide the following methods:

### 1. fetchInfluencerData

```javascript
/**
 * Fetch influencer data from Influencers Club API
 * @param {string} filterValue - The value to search for (username, email, or URL)
 * @param {string} filterKey - The type of filter (username, email, or url)
 * @param {string} platform - The platform to fetch data from
 * @param {string|boolean} emailRequired - Email requirement (must_have, preferred, not_needed, true, false)
 * @param {boolean} postDataRequired - Whether to include post data
 * @returns {Object} - The raw API response
 */
async fetchInfluencerData(filterValue, filterKey = "username", platform = "instagram", emailRequired = "must_have", postDataRequired = true)
```

### 2. processRawData

```javascript
/**
 * Process raw influencer data into standardized format
 * @param {Object} rawData - The raw data from Influencers Club API
 * @returns {Object} - The processed data in standardized format
 */
async processRawData(rawData)
```

### 3. enrichInfluencer

```javascript
/**
 * Fetch, process, and store influencer data
 * @param {string} filterValue - The value to search for (username, email, or URL)
 * @param {string} filterKey - The type of filter (username, email, or url)
 * @param {string} platform - The platform to fetch data from
 * @param {Object} options - Additional options
 * @returns {Object} - The processed data and influencer ID
 */
async enrichInfluencer(filterValue, filterKey = "username", platform = "instagram", options = {})
```

## Standardized Output Format

The connector should process the raw API data into a standardized format that can be used by the platform:

```javascript
{
  profileInfo: {
    username: String,
    fullName: String,
    profileImageUrl: String,
    category: String,
    bio: String,
    email: String,
    location: String,
    speakingLanguage: String,
    isBusinessAccount: Boolean,
    isCreator: Boolean,
    hasBrandDeals: Boolean,
    hasLinkInBio: Boolean
  },
  influencerStats: {
    platforms: [
      {
        name: String,
        followers: String,
        engagement: String
      }
    ],
    engagementRate: {
      value: String,
      description: String
    },
    contentTypes: Array<String>,
    postingFrequency: Number,
    followerGrowth: {
      threeMonths: Number,
      sixMonths: Number,
      nineMonths: Number,
      twelveMonths: Number
    }
  },
  audienceInsights: {
    demographics: {
      age: [
        {
          range: String,
          percentage: Number
        }
      ],
      gender: {
        male: Number,
        female: Number,
        other: Number
      }
    },
    locations: [
      {
        location: String,
        percentage: Number
      }
    ],
    interests: [
      {
        category: String,
        percentage: Number
      }
    ]
  },
  recentPosts: [
    {
      id: String,
      imageUrl: String,
      caption: String,
      likes: String,
      comments: String,
      views: String,
      date: String,
      location: {
        name: String,
        latitude: Number,
        longitude: Number
      },
      taggedUsers: Array<String>
    }
  ],
  brandMentions: [
    {
      name: String,
      count: Number,
      platform: String,
      isPotentialPartner: Boolean
    }
  ],
  partnerships: [
    {
      brand: String,
      evidence: String,
      platform: String,
      date: String
    }
  ],
  platformPresence: [
    {
      platform: String,
      url: String
    }
  ]
}
```

## Storage Strategy

The connector should store both raw and processed data:

1. **Raw Data**: Store the complete API response in Cloud Storage
   - Path: `/influencers/{influencer_id}/raw_data/influencers_club_{timestamp}.json`

2. **Processed Data**: Store the standardized format in Cloud Storage
   - Path: `/influencers/{influencer_id}/processed_data/influencers_club_{timestamp}.json`

3. **Firestore Data**: Store essential data in Firestore for querying
   - Collection: `/influencers/{influencer_id}`
   - Subcollection: `/influencers/{influencer_id}/raw_data/{source_id}`

4. **Images**: Cache images in Cloud Storage to avoid CORS issues
   - Path: `/palas-image-cache/{image_id}.jpg`

## Error Handling

The connector should handle the following error scenarios:

1. **API Errors**:
   - 400 Bad Request - Invalid parameters
   - 401 Unauthorized - Invalid API key
   - 403 Forbidden - Insufficient permissions
   - 404 Not Found - No data found
   - 429 Too Many Requests - Rate limit exceeded

2. **Processing Errors**:
   - Missing or invalid data
   - Image download failures
   - Storage failures

3. **Firestore Errors**:
   - Document not found
   - Permission denied
   - Quota exceeded

## Implementation Considerations

1. **Rate Limiting**: Implement backoff strategies for API rate limits
2. **Caching**: Cache frequently accessed data to reduce API calls
3. **Batch Processing**: Process multiple influencers in batches when possible
4. **Logging**: Log all API calls and errors for debugging
5. **Monitoring**: Monitor API usage and quota consumption

## Testing Strategy

1. **Unit Tests**: Test individual methods with mock data
2. **Integration Tests**: Test the entire connector with the actual API
3. **Error Tests**: Test error handling with simulated errors
4. **Performance Tests**: Test with large datasets to ensure efficiency

## Conclusion

This specification provides a comprehensive guide for implementing the Influencers Club connector. By following these guidelines, the connector will capture ALL relevant data from the API and provide a standardized format for use by the platform.

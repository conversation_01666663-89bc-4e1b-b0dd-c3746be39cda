// json-helpers.js
// JSON processing helpers

import { cacheImage } from './image-processors.js';
import { formatNumber, computeEngagementRate } from '../utils/string-utils.js';

/**
 * Extract influencer profile from raw data
 * @param {Object} data - The raw influencer data
 * @returns {Object} - The extracted profile
 */
async function extractInfluencerProfileDirect(data) {
  // -- Basic Fields (from root) --
  const email = data.email || "";
  const location = data.location || "";
  const speakingLanguage = data.speaking_language || "";
  const firstName = data.first_name || "";
  const profilePicture = await cacheImage(data.instagram?.profile_picture_hd) || "";
  const hasBrandDeals = data.has_brand_deals || false;
  const hasLinkInBio = data.has_link_in_bio || false;
  const isBusiness = data.is_business || false;
  const isCreator = data.is_creator || false;
  const postData = data.post_data || {};
  const influencerCategories = data.instagram?.niche_class?.join(' - ') || "";

  // -- Instagram Data Extraction --
  const insta = data.instagram || {};
  const instaUsername = insta.username || "";
  const instaFollowerCount = insta.follower_count != null ? insta.follower_count : 0;
  const instaBio = insta.biography || "";
  const instaEngagementPercent = insta.engagement_percent != null ? insta.engagement_percent : 0;
  const instaLatestPost = insta.latest_post || {};
  const instaPostEngagement = instaLatestPost.engagement || {};
  const instaPostMedia = Array.isArray(instaLatestPost.media) ? instaLatestPost.media : [];

  // -- YouTube Data Extraction --
  const yt = data.youtube || {};
  const ytSubscriberCount = yt.subscriber_count != null ? yt.subscriber_count : 0;
  const ytLatestVideo = yt.latest_video || {};
  const ytVideoViews = ytLatestVideo.views != null ? ytLatestVideo.views : 0;
  const ytVideoLikes = ytLatestVideo.likes != null ? ytLatestVideo.likes : 0;
  const ytVideoComments = ytLatestVideo.comments != null ? ytLatestVideo.comments : 0;

  // -- TikTok Data Extraction --
  const tt = data.tiktok || {};
  const ttUsername = tt.username || "";
  const ttFollowerCount = tt.follower_count != null ? tt.follower_count : 0;
  const ttLatestPost = tt.latest_post || {};
  const ttPostMedia = ttLatestPost.media || {};
  const ttPostEngagement = ttLatestPost.engagement || {};

  // -- Creator Has --
  const creatorHas = Array.isArray(data.creator_has) ? data.creator_has : [];

  // -- Assemble profileInfo --
  const profileInfo = {
    username: instaUsername,
    profileImageUrl: profilePicture,
    category: influencerCategories,
    bio: instaBio
  };

  // -- Assemble Influencer Stats --
  const platforms = [];
  
  // Instagram platform: use provided engagement percent
  platforms.push({
    name: "Instagram",
    followers: formatNumber(instaFollowerCount),
    engagement: instaEngagementPercent != null ? `${instaEngagementPercent}%` : ""
  });

  // YouTube platform: compute engagement rate if video exists
  if (ytLatestVideo.video_id) {
    const ytTotalEngagement = (ytVideoLikes || 0) + (ytVideoComments || 0);
    platforms.push({
      name: "YouTube",
      followers: formatNumber(ytSubscriberCount),
      engagement: ytSubscriberCount > 0 ? computeEngagementRate(ytTotalEngagement, ytSubscriberCount) : ""
    });
  }

  // TikTok platform: compute engagement rate using likes, comments, and shares
  if (ttLatestPost.post_id) {
    const ttTotalEngagement = (ttPostEngagement.likes || 0) + (ttPostEngagement.comments || 0) + (ttPostEngagement.shares || 0);
    platforms.push({
      name: "TikTok",
      followers: formatNumber(ttFollowerCount),
      engagement: ttFollowerCount > 0 ? computeEngagementRate(ttTotalEngagement, ttFollowerCount) : ""
    });
  }

  // Engagement Rate object (using Instagram metric)
  const engagementRate = {
    value: instaEngagementPercent != null ? `${instaEngagementPercent}%` : "",
    description: "Instagram engagement rate."
  };

  // Content Types: Extract unique media types from all Instagram posts.
  const contentTypesSet = new Set();
  if (Array.isArray(insta.post_data)) {
    insta.post_data.forEach(post => {
      if (Array.isArray(post.media)) {
        post.media.forEach(mediaItem => {
          if (mediaItem.type) {
            contentTypesSet.add(mediaItem.type);
          }
        });
      }
    });
  }
  const contentTypes = Array.from(contentTypesSet);

  const influencerStats = {
    platforms,
    engagementRate,
    contentTypes
  };

  // -- Recent Posts --
  const recentPosts = Array.isArray(insta.post_data)
    ? await Promise.all(insta.post_data.map(async post => {
        let mediaUrl = "";
        if (Array.isArray(post.media) && post.media.length > 0) {
          const firstMedia = post.media[0];
          if (firstMedia.type === "image") {
            mediaUrl = firstMedia.url;
          }
        }
        return {
          id: parseInt(post.post_id, 10) || 0,
          imageUrl: mediaUrl ? await cacheImage(mediaUrl) : "",
          likes: post.engagement && post.engagement.likes != null ? formatNumber(post.engagement.likes) : "0",
          comments: post.engagement && post.engagement.comments != null ? formatNumber(post.engagement.comments) : "0"
        };
      }))
    : [];

  // -- Return the assembled influencer profile --
  return {
    profileInfo,
    influencerStats,
    recentPosts
  };
}

export {
  extractInfluencerProfileDirect
};

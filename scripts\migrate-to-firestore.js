// migrate-to-firestore.js
// Data migration script

import dotenv from 'dotenv';
import { initializeFirebase } from '../config/firebase.js';
import { initializeStorage } from '../config/storage.js';
import { getFirestore } from 'firebase-admin/firestore';

// Load environment variables
dotenv.config();

// Initialize Firebase
const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT 
  ? JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT)
  : require('../serviceAccountKey.json');
const storageBucket = process.env.STORAGE_BUCKET || 'palas-run-cache';
const { app, db, storage } = initializeFirebase(serviceAccount, storageBucket);

/**
 * Migrate influencer data
 * @returns {Promise<void>}
 */
async function migrateInfluencerData() {
  console.log('Migrating influencer data...');
  
  // Get all influencer JSON files from Cloud Storage
  const [files] = await storage.bucket(storageBucket).getFiles({ prefix: 'influencers/' });
  
  // Process each file
  for (const file of files) {
    // Skip non-JSON files
    if (!file.name.endsWith('_raw_data.json')) {
      continue;
    }
    
    // Extract username from filename
    const username = file.name.split('/')[1].split('_raw_data.json')[0];
    
    console.log(`Processing influencer: ${username}`);
    
    // Download and parse the JSON file
    const [contents] = await file.download();
    const rawData = JSON.parse(contents.toString('utf8'));
    
    // Create or update influencer document
    const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
    const influencerSnapshot = await influencerRef.get();
    
    let influencerId;
    if (influencerSnapshot.empty) {
      // Create a new influencer document
      const newInfluencerRef = db.collection('influencers').doc();
      influencerId = newInfluencerRef.id;
      
      // Prepare influencer data
      const influencerData = {
        username: username,
        full_name: rawData.instagram?.full_name || '',
        email: rawData.email || '',
        location: rawData.location || '',
        speaking_language: rawData.speaking_language || '',
        has_brand_deals: rawData.has_brand_deals || false,
        has_link_in_bio: rawData.has_link_in_bio || false,
        is_business: rawData.is_business || false,
        is_creator: rawData.is_creator || false,
        platforms: {},
        creator_has: rawData.creator_has || [],
        created_at: db.Timestamp.now(),
        last_updated: db.Timestamp.now()
      };
      
      // Add platform-specific data
      if (rawData.instagram) {
        influencerData.platforms.instagram = {
          username: rawData.instagram.username,
          follower_count: rawData.instagram.follower_count,
          engagement_percent: rawData.instagram.engagement_percent,
          biography: rawData.instagram.biography,
          profile_image_url: rawData.instagram.profile_picture_hd,
          niches: rawData.instagram.niches || {
            primary: '',
            secondary: []
          },
          hashtags: rawData.instagram.hashtags || [],
          posting_frequency_recent_months: rawData.instagram.posting_frequency_recent_months,
          follower_growth: rawData.instagram.follower_growth || rawData.instagram.creator_follower_growth || {
            three_months_ago: 0,
            six_months_ago: 0,
            nine_months_ago: 0,
            twelve_months_ago: 0
          }
        };
      }
      
      // Add other platforms
      // ...
      
      // Save influencer document
      await db.collection('influencers').doc(influencerId).set(influencerData);
    } else {
      // Use existing influencer document
      influencerId = influencerSnapshot.docs[0].id;
    }
    
    // Store raw data reference
    const timestamp = Date.now();
    const rawStoragePath = `influencers/${influencerId}/raw_data/influencers_club_${timestamp}.json`;
    
    // Copy raw data to new location
    await storage.bucket(storageBucket).file(file.name).copy(storage.bucket(storageBucket).file(rawStoragePath));
    
    // Add raw data reference to Firestore
    await db.collection('influencers').doc(influencerId).collection('raw_data').add({
      source: 'influencers_club',
      storage_path: rawStoragePath,
      processed_storage_path: '',
      pulled_at: db.Timestamp.now(),
      processed_at: db.Timestamp.now()
    });
    
    console.log(`Migrated influencer: ${username}`);
  }
  
  console.log('Influencer data migration complete');
}

/**
 * Migrate campaign data
 * @returns {Promise<void>}
 */
async function migrateCampaignData() {
  console.log('Migrating campaign data...');
  
  // Get all campaign JSON files from Cloud Storage
  const [files] = await storage.bucket(storageBucket).getFiles({ prefix: 'runs/' });
  
  // Group files by report ID
  const reportGroups = {};
  for (const file of files) {
    const match = file.name.match(/\/runs\/(.+?)_(.+?)\.json/);
    if (match) {
      const reportId = match[1];
      const fileType = match[2];
      
      if (!reportGroups[reportId]) {
        reportGroups[reportId] = {};
      }
      
      reportGroups[reportId][fileType] = file;
    }
  }
  
  // Process each report group
  for (const [reportId, files] of Object.entries(reportGroups)) {
    console.log(`Processing campaign: ${reportId}`);
    
    // Create client if it doesn't exist
    const clientId = 'default_client'; // Replace with actual client ID
    const clientRef = db.collection('clients').doc(clientId);
    const clientDoc = await clientRef.get();
    
    if (!clientDoc.exists) {
      await clientRef.set({
        name: 'Default Client',
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now(),
        settings: {}
      });
    }
    
    // Create campaign
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(reportId);
    
    // Process campaign_analysis file
    if (files.campaign_analysis) {
      const [contents] = await files.campaign_analysis.download();
      const campaignData = JSON.parse(contents.toString('utf8'));
      
      await campaignRef.set({
        name: campaignData.name || 'Untitled Campaign',
        report_id: reportId,
        product_description: campaignData.product_description || '',
        influencer_gender: campaignData.influencer_gender || '',
        influencer_niche: campaignData.influencer_niche || '',
        influencer_age: campaignData.influencer_age || '',
        influencer_personality: campaignData.influencer_personality || '',
        influencer_aesthetic: campaignData.influencer_aesthetic || '',
        min_follower_count: campaignData.min_follower_count || 0,
        max_follower_count: campaignData.max_follower_count || 0,
        min_engagement_rate: campaignData.min_engagement_rate || 0,
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now(),
        status: 'active'
      });
    }
    
    // Process discovery_results file
    if (files.discovery_results) {
      const [contents] = await files.discovery_results.download();
      const discoveryData = JSON.parse(contents.toString('utf8'));
      
      const discoveryRef = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(reportId)
        .collection('discovered_influencers').doc();
        
      await discoveryRef.set({
        similar_accounts: discoveryData.similar_accounts || [],
        created_at: db.Timestamp.now()
      });
    }
    
    // Process influencer-specific files
    const influencerFiles = {};
    for (const [fileType, file] of Object.entries(files)) {
      const match = file.name.match(/\/runs\/(.+?)_(.+?)_(.+?)\.json/);
      if (match) {
        const reportId = match[1];
        const username = match[2];
        const analysisType = match[3];
        
        if (!influencerFiles[username]) {
          influencerFiles[username] = {};
        }
        
        influencerFiles[username][analysisType] = file;
      }
    }
    
    // Process each influencer
    for (const [username, files] of Object.entries(influencerFiles)) {
      console.log(`Processing campaign influencer: ${username}`);
      
      // Get influencer ID
      const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
      const influencerSnapshot = await influencerRef.get();
      
      if (influencerSnapshot.empty) {
        console.warn(`Influencer not found: ${username}`);
        continue;
      }
      
      const influencerId = influencerSnapshot.docs[0].id;
      
      // Create campaign influencer
      const campaignInfluencerRef = db.collection('clients').doc(clientId)
        .collection('campaigns').doc(reportId)
        .collection('campaign_influencers').doc(influencerId);
        
      await campaignInfluencerRef.set({
        influencer_id: influencerId,
        username: username,
        status: 'enriched',
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now()
      });
      
      // Process web_analysis file
      if (files.web_analysis) {
        const [contents] = await files.web_analysis.download();
        const webAnalysisData = JSON.parse(contents.toString('utf8'));
        
        const webAnalysisRef = await db.collection('influencers').doc(influencerId)
          .collection('web_analysis').doc();
          
        await webAnalysisRef.set({
          name: webAnalysisData.name || '',
          sentiment_score: webAnalysisData.sentiment_score || 0,
          risk_level: webAnalysisData.risk_level || 'Medium',
          deep_dive_report: webAnalysisData.deep_dive_report || {},
          created_at: db.Timestamp.now(),
          updated_at: db.Timestamp.now()
        });
      }
      
      // Process aesthetic_analysis file
      if (files.aesthetic_analysis) {
        const [contents] = await files.aesthetic_analysis.download();
        const aestheticAnalysisData = JSON.parse(contents.toString('utf8'));
        
        const aestheticAnalysisRef = await campaignInfluencerRef
          .collection('aesthetic_analysis').doc();
          
        await aestheticAnalysisRef.set({
          name: aestheticAnalysisData.name || '',
          brand_fit_score: aestheticAnalysisData.brand_fit_score || 0,
          content_analysis: aestheticAnalysisData.content_analysis || {},
          created_at: db.Timestamp.now()
        });
      }
      
      // Process roi_analysis file
      if (files.roi_analysis) {
        const [contents] = await files.roi_analysis.download();
        const roiAnalysisData = JSON.parse(contents.toString('utf8'));
        
        const roiAnalysisRef = await campaignInfluencerRef
          .collection('roi_analysis').doc();
          
        await roiAnalysisRef.set({
          brand_fit_score: roiAnalysisData.brand_fit_score || 0,
          brand_fit_description: roiAnalysisData.brand_fit_description || '',
          risk_level: roiAnalysisData.risk_level || 'Medium',
          risk_description: roiAnalysisData.risk_description || '',
          influencer_analysis: roiAnalysisData.influencer_analysis || {},
          created_at: db.Timestamp.now()
        });
      }
      
      console.log(`Migrated campaign influencer: ${username}`);
    }
    
    console.log(`Migrated campaign: ${reportId}`);
  }
  
  console.log('Campaign data migration complete');
}

/**
 * Validate migrated data
 * @returns {Promise<void>}
 */
async function validateMigratedData() {
  console.log('Validating migrated data...');
  
  // Validate influencer data
  const influencersSnapshot = await db.collection('influencers').get();
  console.log(`Migrated ${influencersSnapshot.size} influencers`);
  
  // Validate campaign data
  const clientsSnapshot = await db.collection('clients').get();
  for (const clientDoc of clientsSnapshot.docs) {
    const clientId = clientDoc.id;
    const campaignsSnapshot = await db.collection('clients').doc(clientId).collection('campaigns').get();
    console.log(`Migrated ${campaignsSnapshot.size} campaigns for client ${clientId}`);
    
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      const campaignInfluencersSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').get();
      console.log(`Migrated ${campaignInfluencersSnapshot.size} influencers for campaign ${campaignId}`);
    }
  }
  
  // Validate web analysis data
  const webAnalysisCount = await countWebAnalyses(db);
  console.log(`Migrated ${webAnalysisCount} web analyses`);
  
  // Validate aesthetic analysis data
  const aestheticAnalysisCount = await countAestheticAnalyses(db);
  console.log(`Migrated ${aestheticAnalysisCount} aesthetic analyses`);
  
  // Validate ROI analysis data
  const roiAnalysisCount = await countROIAnalyses(db);
  console.log(`Migrated ${roiAnalysisCount} ROI analyses`);
  
  console.log('Validation complete');
}

/**
 * Count web analyses
 * @param {Object} db - The Firestore database
 * @returns {Promise<number>} - The number of web analyses
 */
async function countWebAnalyses(db) {
  let count = 0;
  const influencersSnapshot = await db.collection('influencers').get();
  for (const influencerDoc of influencersSnapshot.docs) {
    const influencerId = influencerDoc.id;
    const webAnalysesSnapshot = await db.collection('influencers').doc(influencerId)
      .collection('web_analysis').get();
    count += webAnalysesSnapshot.size;
  }
  return count;
}

/**
 * Count aesthetic analyses
 * @param {Object} db - The Firestore database
 * @returns {Promise<number>} - The number of aesthetic analyses
 */
async function countAestheticAnalyses(db) {
  let count = 0;
  const clientsSnapshot = await db.collection('clients').get();
  for (const clientDoc of clientsSnapshot.docs) {
    const clientId = clientDoc.id;
    const campaignsSnapshot = await db.collection('clients').doc(clientId).collection('campaigns').get();
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      const campaignInfluencersSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').get();
      for (const campaignInfluencerDoc of campaignInfluencersSnapshot.docs) {
        const influencerId = campaignInfluencerDoc.id;
        const aestheticAnalysesSnapshot = await db.collection('clients').doc(clientId)
          .collection('campaigns').doc(campaignId)
          .collection('campaign_influencers').doc(influencerId)
          .collection('aesthetic_analysis').get();
        count += aestheticAnalysesSnapshot.size;
      }
    }
  }
  return count;
}

/**
 * Count ROI analyses
 * @param {Object} db - The Firestore database
 * @returns {Promise<number>} - The number of ROI analyses
 */
async function countROIAnalyses(db) {
  let count = 0;
  const clientsSnapshot = await db.collection('clients').get();
  for (const clientDoc of clientsSnapshot.docs) {
    const clientId = clientDoc.id;
    const campaignsSnapshot = await db.collection('clients').doc(clientId).collection('campaigns').get();
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      const campaignInfluencersSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').get();
      for (const campaignInfluencerDoc of campaignInfluencersSnapshot.docs) {
        const influencerId = campaignInfluencerDoc.id;
        const roiAnalysesSnapshot = await db.collection('clients').doc(clientId)
          .collection('campaigns').doc(campaignId)
          .collection('campaign_influencers').doc(influencerId)
          .collection('roi_analysis').get();
        count += roiAnalysesSnapshot.size;
      }
    }
  }
  return count;
}

/**
 * Main migration function
 * @returns {Promise<void>}
 */
async function migrateToFirestore() {
  try {
    // Migrate influencer data
    await migrateInfluencerData();
    
    // Migrate campaign data
    await migrateCampaignData();
    
    // Validate migrated data
    await validateMigratedData();
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateToFirestore();

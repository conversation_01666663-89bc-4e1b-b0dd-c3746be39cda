// test-analysis-flow.js
// Test script for the full analysis flow using real data and production setup

import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp } from 'firebase-admin/app';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { performWebAnalysis, performAestheticAnalysis, performROIAnalysis } from '../services/analysis-service.js';
import { generateCampaignBrief } from '../services/campaign-service.js';
import { getSeedInfluencers, discoverInfluencers } from '../services/discovery-service.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';
import { getCachedJson, writeJsonToBucket, bucketName } from '../helpers/storage-helpers.js';
import InfluencersClubConnector from '../connectors/influencers-club-connector.js';

// Initialize Firebase
initializeApp();
const db = getFirestore();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Load a JSON file from the sample data directory
 * @param {string} filename - The filename to load
 * @returns {Object} - The parsed JSON data
 */
function loadSampleJson(filename) {
  const filePath = path.join(__dirname, 'sample_data', filename);
  console.log(`Loading sample data from ${filePath}`);
  
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error(`Error loading sample data from ${filePath}:`, error);
    throw error;
  }
}

/**
 * Test the full analysis flow for an influencer
 * @param {string} username - The influencer username
 */
async function testAnalysisFlow(username) {
  try {
    console.log('='.repeat(80));
    console.log(`🧪 STARTING ANALYSIS FLOW TEST FOR INFLUENCER: ${username}`);
    console.log('='.repeat(80));
    
    // Step 1: Load sample campaign data
    console.log('\n📋 STEP 1: Loading sample campaign data...');
    const sampleCampaign = loadSampleJson('sample_campaign.json');
    console.log(`✅ Loaded sample campaign: ${sampleCampaign.name}`);
    
    // Step 2: Generate campaign brief using the Assistants API
    console.log('\n🔍 STEP 2: Generating campaign brief using Assistants API...');
    console.log('   🔄 Calling generateCampaignBrief...');
    
    const campaignBriefResult = await generateCampaignBrief({
      campaign: sampleCampaign,
      client_id: DEFAULT_CLIENT_ID
    });
    
    const campaignId = campaignBriefResult.campaignId;
    const campaignData = campaignBriefResult.campaignData;
    
    console.log(`   ✅ Campaign brief generated with ID: ${campaignId}`);
    console.log(`   📊 Campaign brief data: ${JSON.stringify(campaignData, null, 2).substring(0, 200)}...`);
    
    // Step 3: Get seed influencers using the Assistants API
    console.log('\n🌱 STEP 3: Getting seed influencers using Assistants API...');
    console.log('   🔄 Calling getSeedInfluencers...');
    
    const seedInfluencersData = await getSeedInfluencers(DEFAULT_CLIENT_ID, campaignId, campaignData);
    
    console.log(`   ✅ Seed influencers retrieved`);
    console.log(`   👥 Seed influencers: ${JSON.stringify(seedInfluencersData.recommended_influencers || seedInfluencersData.influencers, null, 2).substring(0, 200)}...`);
    
    // Step 4: Discover influencers using the Assistants API
    console.log('\n🔎 STEP 4: Discovering influencers using Assistants API...');
    console.log('   🔄 Calling discoverInfluencers...');
    
    const discoveryData = await discoverInfluencers(DEFAULT_CLIENT_ID, campaignId, campaignData);
    
    console.log(`   ✅ Influencers discovered`);
    console.log(`   👥 Discovered influencers: ${JSON.stringify(discoveryData.similar_accounts || discoveryData.candidates, null, 2).substring(0, 200)}...`);
    
    // Step 5: Fetch real influencer data from Influencers Club
    console.log('\n📊 STEP 5: Fetching real influencer data from Influencers Club...');
    console.log(`   🔄 Fetching data for ${username}...`);
    
    const connector = new InfluencersClubConnector();
    const influencerData = await connector.fetchInfluencerData(username, 'username', 'instagram');
    
    console.log(`   ✅ Influencer data fetched for ${username}`);
    
    // Step 6: Create influencer in Firestore
    console.log('\n👤 STEP 6: Creating influencer in Firestore...');
    
    // Extract relevant data
    const instagram = influencerData.instagram || {};
    
    // Create influencer object
    const influencer = {
      username: username,
      name: instagram.full_name || username,
      platform: 'instagram',
      follower_count: instagram.follower_count || 0,
      engagement_rate: instagram.engagement_percent || 0,
      bio: instagram.biography || '',
      profile_picture: instagram.profile_picture_hd || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Check if influencer already exists
    const influencersSnapshot = await db.collection('influencers')
      .where('username', '==', username)
      .limit(1)
      .get();
    
    let influencerId;
    
    if (!influencersSnapshot.empty) {
      // Update existing influencer
      influencerId = influencersSnapshot.docs[0].id;
      await db.collection('influencers').doc(influencerId).update({
        ...influencer,
        updated_at: new Date().toISOString()
      });
      console.log(`   ✅ Updated existing influencer with ID: ${influencerId}`);
    } else {
      // Create new influencer
      const influencerRef = await db.collection('influencers').add(influencer);
      influencerId = influencerRef.id;
      console.log(`   ✅ Created new influencer with ID: ${influencerId}`);
    }
    
    // Step 7: Associate influencer with campaign
    console.log('\n🔗 STEP 7: Associating influencer with campaign...');
    
    // Create association
    const association = {
      influencer_id: influencerId,
      username: username,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    await db.collection('clients').doc(DEFAULT_CLIENT_ID)
      .collection('campaigns').doc(campaignId)
      .collection('campaign_influencers').doc(influencerId)
      .set(association);
    
    console.log('   ✅ Influencer associated with campaign');
    
    // Step 8: Perform web analysis using the Responses API
    console.log('\n🌐 STEP 8: Performing web analysis using Responses API...');
    console.log('   🔄 Calling performWebAnalysis...');
    
    const influencerName = instagram.full_name || username;
    const webAnalysisData = await performWebAnalysis(
      DEFAULT_CLIENT_ID, 
      campaignId, 
      influencerId, 
      influencerName, 
      username
    );
    
    console.log('   ✅ Web analysis completed');
    console.log(`   📊 Web analysis data: ${JSON.stringify(webAnalysisData, null, 2).substring(0, 200)}...`);
    
    // Step 9: Perform aesthetic analysis using the Responses API
    console.log('\n🎨 STEP 9: Performing aesthetic analysis using Responses API...');
    console.log('   🔄 Calling performAestheticAnalysis...');
    
    const aestheticAnalysisData = await performAestheticAnalysis(
      DEFAULT_CLIENT_ID, 
      campaignId, 
      influencerId, 
      influencerName, 
      influencerData, 
      webAnalysisData
    );
    
    console.log('   ✅ Aesthetic analysis completed');
    console.log(`   📊 Aesthetic analysis data: ${JSON.stringify(aestheticAnalysisData, null, 2).substring(0, 200)}...`);
    
    // Step 10: Perform ROI analysis using the Assistants API
    console.log('\n💰 STEP 10: Performing ROI analysis using Assistants API...');
    console.log('   🔄 Calling performROIAnalysis...');
    
    // Get campaign data
    const campaignDoc = await db.collection('clients').doc(DEFAULT_CLIENT_ID)
      .collection('campaigns').doc(campaignId).get();
    const campaignDataForROI = campaignDoc.data();
    
    // Process the influencer data for ROI analysis
    const processedInfluencerData = {
      profileInfo: {
        username: username,
        name: influencerName,
        bio: influencerData.instagram?.biography || '',
        profilePicture: influencerData.instagram?.profile_picture_hd || '',
        followers: influencerData.instagram?.follower_count || 0,
        engagement: influencerData.instagram?.engagement_percent || 0
      }
    };
    
    const roiAnalysisData = await performROIAnalysis(
      DEFAULT_CLIENT_ID, 
      campaignId, 
      influencerId, 
      influencerName, 
      campaignDataForROI, 
      processedInfluencerData, 
      webAnalysisData, 
      aestheticAnalysisData
    );
    
    console.log('   ✅ ROI analysis completed');
    console.log(`   📊 ROI analysis data: ${JSON.stringify(roiAnalysisData, null, 2).substring(0, 200)}...`);
    
    console.log('\n='.repeat(80));
    console.log('🎉 ANALYSIS FLOW TEST COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(80));
    console.log(`📊 Campaign ID: ${campaignId}`);
    console.log(`👤 Influencer ID: ${influencerId}`);
    
    return {
      campaignId,
      influencerId,
      webAnalysis: webAnalysisData,
      aestheticAnalysis: aestheticAnalysisData,
      roiAnalysis: roiAnalysisData
    };
  } catch (error) {
    console.error('❌ ERROR IN ANALYSIS FLOW TEST:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

// Run the test if this file is executed directly
if (process.argv[1].endsWith('test-analysis-flow.js')) {
  const username = process.argv[2] || 'cristiano'; // Default to 'cristiano' if no username provided
  
  console.log(`Running analysis flow test for ${username}`);
  
  testAnalysisFlow(username)
    .then(results => {
      console.log('\n📊 ANALYSIS RESULTS SUMMARY:');
      
      // Print a summary of each analysis
      if (results.webAnalysis) {
        console.log('\n🌐 Web Analysis Summary:');
        console.log(`Risk Level: ${results.webAnalysis.risk_level || 'N/A'}`);
        console.log(`Sentiment Score: ${results.webAnalysis.sentiment_score || 'N/A'}`);
      }
      
      if (results.aestheticAnalysis) {
        console.log('\n🎨 Aesthetic Analysis Summary:');
        console.log(`Brand Fit Score: ${results.aestheticAnalysis.brand_fit_score || 'N/A'}`);
      }
      
      if (results.roiAnalysis) {
        console.log('\n💰 ROI Analysis Summary:');
        if (results.roiAnalysis.finalists && results.roiAnalysis.finalists.length > 0) {
          const finalist = results.roiAnalysis.finalists[0];
          console.log(`Brand Fit Score: ${finalist.brand_fit_score || 'N/A'}`);
          console.log(`Risk Level: ${finalist.risk_level || 'N/A'}`);
        }
      }
      
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export default testAnalysisFlow;

// analysis-routes.js
// Analysis-related routes

import express from 'express';
import {
  performWebAnalysis,
  performAestheticAnalysis,
  performPartnershipAnalysis,
  performROIAnalysis,
  performAllAnalysesConcurrently,
  getCombinedAnalysis,
  getMergedAnalysis,
  getAllClientMergedAnalyses,
  performFormatterAnalysis
} from '../services/analysis-service.js';
import { DEFAULT_CLIENT_ID } from '../config/constants.js';

const router = express.Router();

/**
 * Perform web analysis
 * POST /api/analysis/web
 */
router.post('/web', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerUsername } = req.body;
    const result = await performWebAnalysis(clientId, campaignId, influencerId, influencerName, influencerUsername);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing web analysis:', error);
    res.status(500).json({ error: 'Failed to perform web analysis' });
  }
});

/**
 * Perform aesthetic analysis
 * POST /api/analysis/aesthetic
 */
router.post('/aesthetic', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerData, webAnalysisData } = req.body;
    const result = await performAestheticAnalysis(clientId, campaignId, influencerId, influencerName, influencerData, webAnalysisData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing aesthetic analysis:', error);
    res.status(500).json({ error: 'Failed to perform aesthetic analysis' });
  }
});

/**
 * Perform partnership analysis
 * POST /api/analysis/partnership
 */
router.post('/partnership', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID, campaignId, influencerId, influencerName, influencerUsername, campaignData, influencerData } = req.body;
    const result = await performPartnershipAnalysis(clientId, campaignId, influencerId, influencerName, influencerUsername, campaignData, influencerData);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing partnership analysis:', error);
    res.status(500).json({ error: 'Failed to perform partnership analysis' });
  }
});

/**
 * Perform ROI analysis
 * POST /api/analysis/roi
 */
router.post('/roi', async (req, res) => {
  try {
    const {
      clientId = DEFAULT_CLIENT_ID,
      campaignId,
      influencerId,
      influencerName,
      campaignData,
      influencerData,
      webAnalysisData,
      aestheticAnalysisData,
      partnershipAnalysisData = null
    } = req.body;

    const result = await performROIAnalysis(
      clientId,
      campaignId,
      influencerId,
      influencerName,
      campaignData,
      influencerData,
      webAnalysisData,
      aestheticAnalysisData,
      partnershipAnalysisData
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing ROI analysis:', error);
    res.status(500).json({ error: 'Failed to perform ROI analysis' });
  }
});

/**
 * Perform all analyses concurrently
 * POST /api/analysis/concurrent
 */
router.post('/concurrent', async (req, res) => {
  try {
    const {
      clientId = DEFAULT_CLIENT_ID,
      campaignId,
      influencerId,
      influencerName,
      influencerUsername,
      campaignData,
      influencerData
    } = req.body;

    const result = await performAllAnalysesConcurrently(
      clientId,
      campaignId,
      influencerId,
      influencerName,
      influencerUsername,
      campaignData,
      influencerData
    );

    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing concurrent analyses:', error);
    res.status(500).json({ error: 'Failed to perform concurrent analyses' });
  }
});

/**
 * Get combined analysis
 * GET /api/analysis/combined/:campaignId/:influencerId
 */
router.get('/combined/:campaignId/:influencerId', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId = DEFAULT_CLIENT_ID } = req.query;
    const result = await getCombinedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting combined analysis:', error);
    res.status(500).json({ error: 'Failed to get combined analysis' });
  }
});

/**
 * Get merged analysis
 * GET /api/analysis/merged/:campaignId/:influencerId
 */
router.get('/merged/:campaignId/:influencerId', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId = DEFAULT_CLIENT_ID } = req.query;
    const result = await getMergedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting merged analysis:', error);
    res.status(500).json({ error: 'Failed to get merged analysis' });
  }
});

/**
 * Get all merged analyses for a client
 * GET /api/analysis/client/:clientId/merged
 */
router.get('/client/:clientId/merged', async (req, res) => {
  try {
    const { clientId } = req.params;
    console.log(`Received request for all merged analyses for client: ${clientId}`);
    const result = await getAllClientMergedAnalyses(clientId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting client merged analyses:', error);
    res.status(500).json({ error: 'Failed to get client merged analyses', message: error.message });
  }
});

/**
 * Perform formatter analysis
 * POST /api/analysis/formatter
 */
router.post('/formatter', async (req, res) => {
  try {
    const { clientId = DEFAULT_CLIENT_ID, campaignId, influencerId } = req.body;

    if (!campaignId || !influencerId) {
      return res.status(400).json({ error: 'campaignId and influencerId are required' });
    }

    console.log(`Received request for formatter analysis - Client: ${clientId}, Campaign: ${campaignId}, Influencer: ${influencerId}`);
    const result = await performFormatterAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing formatter analysis:', error);
    res.status(500).json({ error: 'Failed to perform formatter analysis', message: error.message });
  }
});

export default router;

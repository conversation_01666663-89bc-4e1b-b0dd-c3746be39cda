// test_client_merged_analyses.js
// Test script for the client merged analyses endpoint

import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// API URL - use the deployed server
const API_URL = 'https://palas-influencer-intelligence-9815718377.us-central1.run.app';

// Default client ID - replace with a real client ID for testing
const CLIENT_ID = 'test_client';

/**
 * Test the client merged analyses endpoint
 * @param {string} clientId - The client ID to test with
 */
async function testClientMergedAnalyses(clientId = CLIENT_ID) {
  console.log(`Testing client merged analyses endpoint for client: ${clientId}`);

  try {
    // Make the API request
    console.log(`Making request to: ${API_URL}/api/analysis/client/${clientId}/merged`);
    const response = await fetch(`${API_URL}/api/analysis/client/${clientId}/merged`);

    // Check if the request was successful
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }

    // Parse the response
    const data = await response.json();

    // Log the results
    console.log(`Retrieved merged analyses for client ${clientId}`);
    console.log(`Found ${Object.keys(data).length} campaigns`);

    // Count total influencers
    let totalInfluencers = 0;
    for (const campaignId in data) {
      const campaignData = data[campaignId];
      const influencersCount = campaignData.influencers.length;
      totalInfluencers += influencersCount;
      console.log(`Campaign ${campaignId} (${campaignData.campaign.name || 'Unnamed'}): ${influencersCount} influencers`);
    }
    console.log(`Total influencers: ${totalInfluencers}`);

    // Create the test_outputs directory if it doesn't exist
    const outputDir = path.join(__dirname, '..', 'test_outputs');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Save the results to a file
    const outputPath = path.join(outputDir, `client_merged_analyses_${Date.now()}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(data, null, 2));
    console.log(`Results saved to: ${outputPath}`);

    return data;
  } catch (error) {
    console.error('Error testing client merged analyses:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  // Get the client ID from the command line arguments, if provided
  const clientId = process.argv[2] || CLIENT_ID;

  testClientMergedAnalyses(clientId)
    .then(() => console.log('Test completed successfully'))
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

export { testClientMergedAnalyses };

// analysis-generators.js
// Combined and merged analysis generators

import { getFirestore } from 'firebase-admin/firestore';
import { storage, bucketName } from './storage-helpers.js';
import { deepMerge } from '../utils/object-utils.js';

/**
 * Generate a combined analysis for an influencer in a specific campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The combined analysis
 */
async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  const db = getFirestore();

  try {
    // Get the campaign influencer document
    const campaignInfluencerRef = db.collection('clients')
      .doc(clientId)
      .collection('campaigns')
      .doc(campaignId)
      .collection('campaign_influencers')
      .doc(influencerId);

    const campaignInfluencerDoc = await campaignInfluencerRef.get();

    if (!campaignInfluencerDoc.exists) {
      throw new Error(`Campaign influencer not found: ${influencerId}`);
    }

    // Get the aesthetic analysis
    const aestheticAnalysisSnapshot = await campaignInfluencerRef
      .collection('aesthetic_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    // Get the ROI analysis
    const roiAnalysisSnapshot = await campaignInfluencerRef
      .collection('roi_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    // Get the partnership analysis
    const partnershipAnalysisSnapshot = await campaignInfluencerRef
      .collection('partnership_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    // Get the web analysis from the global influencer document
    const influencerRef = db.collection('influencers').doc(influencerId);
    const webAnalysisSnapshot = await influencerRef
      .collection('web_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();

    // Get the global influencer data
    const influencerDoc = await influencerRef.get();

    if (!influencerDoc.exists) {
      throw new Error(`Influencer not found: ${influencerId}`);
    }

    // Get the influencer data
    const influencerData = influencerDoc.data();

    // Initialize the combined analysis object
    const combinedAnalysis = {
      profileInfo: {
        name: influencerData.full_name || '',
        username: influencerData.username || '',
        followers: 0,
        engagement: '0%',
        metrics: {},
        aestheticAnalysis: {},
        partnershipAnalysis: {}, // Add partnership analysis
        platformMetrics: [] // Add platform-specific metrics
      },
      roiProjection: {},
      webAnalysis: {},
      brandFit: {}
    };

    // Add platform-specific metrics if available
    if (influencerData.platforms) {
      // Get the highest follower count and engagement rate across all platforms
      let highestFollowerCount = 0;
      let highestEngagementRate = 0;

      // Process each platform and add to platformMetrics
      Object.entries(influencerData.platforms).forEach(([platformName, platformData]) => {
        // Add to platform metrics array
        let followers = 0;
        let engagement = '0%';

        // Handle platform-specific data
        switch(platformName) {
          case 'instagram':
            followers = platformData.follower_count || 0;
            engagement = platformData.engagement_percent ? `${platformData.engagement_percent}%` : '0%';
            break;
          case 'youtube':
            followers = platformData.subscriber_count || 0;
            engagement = platformData.engagement_percent ? `${platformData.engagement_percent}%` : '0%';
            break;
          case 'tiktok':
            followers = platformData.follower_count || 0;
            engagement = platformData.engagement_percent ? `${platformData.engagement_percent}%` : '0%';
            break;
          default:
            // Handle other platforms
            if (platformData.follower_count) {
              followers = platformData.follower_count;
            } else if (platformData.subscriber_count) {
              followers = platformData.subscriber_count;
            }

            if (platformData.engagement_percent) {
              engagement = `${platformData.engagement_percent}%`;
            }
        }

        // Add to platform metrics
        combinedAnalysis.profileInfo.platformMetrics.push({
          platform: platformName.charAt(0).toUpperCase() + platformName.slice(1), // Capitalize platform name
          followers: followers.toLocaleString(),
          engagement
        });

        // Update highest metrics
        if (followers > highestFollowerCount) {
          highestFollowerCount = followers;
        }

        const engagementValue = parseFloat(engagement);
        if (!isNaN(engagementValue) && engagementValue > highestEngagementRate) {
          highestEngagementRate = engagementValue;
        }
      });

      // Set the overall followers and engagement to the highest values
      combinedAnalysis.profileInfo.followers = highestFollowerCount;
      combinedAnalysis.profileInfo.engagement = `${highestEngagementRate}%`;
    }

    // Add aesthetic analysis data if available
    if (!aestheticAnalysisSnapshot.empty) {
      const aestheticAnalysis = aestheticAnalysisSnapshot.docs[0].data();

      combinedAnalysis.profileInfo.metrics = {
        brandAesthetic: `${aestheticAnalysis.content_analysis?.visual_fit || 0}/100`,
        styleAnalysis: ((aestheticAnalysis.brand_fit_score || 0) / 10).toFixed(1),
        vibeAlignment: ((aestheticAnalysis.brand_fit_score || 0) / 10).toFixed(1),
        visualAlignment: ((aestheticAnalysis.content_analysis?.visual_fit || 0) / 10).toFixed(1)
      };

      combinedAnalysis.profileInfo.aestheticAnalysis = {
        keyObservations: aestheticAnalysis.content_analysis?.content_themes || [],
        verdict: aestheticAnalysis.content_analysis?.verdict || aestheticAnalysis.content_analysis?.overall_assessment || "",
        verdictDescription: aestheticAnalysis.content_analysis?.tone_fit || ""
      };
    }

    // Add ROI analysis data if available
    if (!roiAnalysisSnapshot.empty) {
      const roiAnalysis = roiAnalysisSnapshot.docs[0].data();

      combinedAnalysis.profileInfo.metrics.roiPotential = roiAnalysis.influencer_analysis?.roi_projection?.roi_rating || "Medium";
      combinedAnalysis.profileInfo.metrics.brandFitScore = ((roiAnalysis.brand_fit_score || 0) / 10).toFixed(1);

      combinedAnalysis.roiProjection = {
        expectedImpressions: roiAnalysis.influencer_analysis?.roi_projection?.expected_impressions?.toLocaleString() || "0",
        expectedEngagementRate: `${roiAnalysis.influencer_analysis?.roi_projection?.expected_engagement_rate || 0}%`,
        expectedEngagements: roiAnalysis.influencer_analysis?.roi_projection?.expected_engagements?.toLocaleString() || "0",
        expectedEngagementDescription: roiAnalysis.influencer_analysis?.roi_projection?.roi_rationale || "",
        roiRating: roiAnalysis.influencer_analysis?.roi_projection?.roi_rating || "Medium",
        roiPotentialScore: roiAnalysis.influencer_analysis?.roi_projection?.roi_potential_score || roiAnalysis.influencer_analysis?.roi_projection?.roi_score || "",
        riskAssessment: roiAnalysis.risk_level || "Medium",
        riskAssessmentDescription: roiAnalysis.risk_description || "",
        audienceQuality: roiAnalysis.influencer_analysis?.roi_projection?.audience_quality || roiAnalysis.influencer_analysis?.audience_quality || "",
        audienceQualityDescription: roiAnalysis.influencer_analysis?.roi_projection?.audience_quality_description || roiAnalysis.influencer_analysis?.audience_quality_description || ""
      };

      combinedAnalysis.brandFit = {
        score: roiAnalysis.brand_fit_score || 0,
        description: roiAnalysis.brand_fit_description || "",
        strengths: roiAnalysis.influencer_analysis?.roi_projection?.strengths || [],
        weaknesses: roiAnalysis.influencer_analysis?.roi_projection?.weaknesses || []
      };
    }

    // Add partnership analysis data if available
    if (!partnershipAnalysisSnapshot.empty) {
      const partnershipAnalysis = partnershipAnalysisSnapshot.docs[0].data();

      combinedAnalysis.profileInfo.partnershipAnalysis = {
        partnershipType: partnershipAnalysis.partnership_status?.partnership_type || 'Unknown',
        contractualReadiness: partnershipAnalysis.contractual_readiness?.fluency_score || 0,
        conflictSeverity: partnershipAnalysis.conflict_analysis?.conflict_severity || 'None',
        recommendation: partnershipAnalysis.recommendation?.fit_status || 'Unknown'
      };

      // Add partnership data to brand fit section
      combinedAnalysis.brandFit.partnershipFit = {
        status: partnershipAnalysis.recommendation?.fit_status || 'Unknown',
        reasoning: partnershipAnalysis.recommendation?.primary_reasoning || '',
        integrationNotes: partnershipAnalysis.recommendation?.integration_notes || ''
      };
    }

    // Add web analysis data if available
    if (!webAnalysisSnapshot.empty) {
      const webAnalysis = webAnalysisSnapshot.docs[0].data();

      combinedAnalysis.webAnalysis = {
        sentimentScore: webAnalysis.sentiment_score || 0,
        riskLevel: webAnalysis.risk_level || "Medium",
        timeline: webAnalysis.deep_dive_report?.timeline_events || [],
        brandMentions: webAnalysis.deep_dive_report?.brand_mentions || [],
        partnerships: webAnalysis.deep_dive_report?.partnerships || [],
        controversies: webAnalysis.deep_dive_report?.controversies || []
      };
    }

    return combinedAnalysis;
  } catch (error) {
    console.error('Error generating combined analysis:', error);
    throw error;
  }
}

/**
 * Get the processed influencer data from Cloud Storage
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The processed influencer data
 */
async function getProcessedInfluencerData(influencerId) {
  const db = getFirestore();

  try {
    // Get the latest processed data reference
    const rawDataSnapshot = await db.collection('influencers')
      .doc(influencerId)
      .collection('raw_data')
      .orderBy('processed_at', 'desc')
      .limit(1)
      .get();

    if (rawDataSnapshot.empty) {
      throw new Error(`No processed data found for influencer: ${influencerId}`);
    }

    const rawDataDoc = rawDataSnapshot.docs[0].data();
    const processedStoragePath = rawDataDoc.processed_storage_path;

    // Get the processed data from Cloud Storage
    const file = storage.bucket(bucketName).file(processedStoragePath);

    const [contents] = await file.download();
    const processedData = JSON.parse(contents.toString('utf8'));

    return processedData;
  } catch (error) {
    console.error('Error getting processed influencer data:', error);
    throw error;
  }
}

/**
 * Generate a merged analysis by combining the processed influencer data
 * with the combined analysis
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The merged analysis
 */
async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  try {
    // Get the combined analysis
    const combinedAnalysis = await generateCombinedAnalysis(clientId, campaignId, influencerId);

    // Get the processed influencer data
    const processedData = await getProcessedInfluencerData(influencerId);

    // Merge the two objects
    return deepMerge(combinedAnalysis, processedData);
  } catch (error) {
    console.error('Error generating merged analysis:', error);
    throw error;
  }
}

export {
  generateCombinedAnalysis,
  getProcessedInfluencerData,
  generateMergedAnalysis
};

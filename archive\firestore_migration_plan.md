# Firestore Migration Plan

This document outlines the detailed plan for migrating the existing JSON-based data to the new Firestore database structure.

## Overview

The migration process will involve:

1. Setting up the Firestore database structure
2. Migrating existing JSON data to Firestore
3. Updating references to point to the new Firestore data
4. Validating the migrated data
5. Implementing a fallback mechanism for backward compatibility

## Firestore Database Structure

The Firestore database will have the following structure:

```
/influencers/{influencer_id}/
  - username: String
  - full_name: String
  - email: String
  - location: String
  - speaking_language: String
  - has_brand_deals: Boolean
  - has_link_in_bio: Boolean
  - is_business: Boolean
  - is_creator: Boolean
  - platforms: Map
    - instagram: Map
    - youtube: Map
    - tiktok: Map
    - twitter: Map
  - creator_has: Array<Map>
  - last_updated: Timestamp
  - created_at: Timestamp

/influencers/{influencer_id}/web_analysis/{analysis_id}/
  - name: String
  - sentiment_score: Number
  - risk_level: String
  - deep_dive_report: Map
  - created_at: Timestamp
  - updated_at: Timestamp

/influencers/{influencer_id}/raw_data/{source_id}/
  - source: String
  - storage_path: String
  - processed_storage_path: String
  - pulled_at: Timestamp
  - processed_at: Timestamp

/clients/{client_id}/
  - name: String
  - created_at: Timestamp
  - updated_at: Timestamp
  - settings: Map

/clients/{client_id}/campaigns/{campaign_id}/
  - name: String
  - report_id: String
  - product_description: String
  - influencer_gender: String
  - influencer_niche: String
  - influencer_age: String
  - influencer_personality: String
  - influencer_aesthetic: String
  - min_follower_count: Number
  - max_follower_count: Number
  - min_engagement_rate: Number
  - created_at: Timestamp
  - updated_at: Timestamp
  - status: String

/clients/{client_id}/campaigns/{campaign_id}/discovered_influencers/{discovery_id}/
  - similar_accounts: Array<Map>
  - created_at: Timestamp

/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/
  - influencer_id: String
  - username: String
  - status: String
  - created_at: Timestamp
  - updated_at: Timestamp

/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/aesthetic_analysis/{analysis_id}/
  - name: String
  - brand_fit_score: Number
  - content_analysis: Map
  - created_at: Timestamp

/clients/{client_id}/campaigns/{campaign_id}/campaign_influencers/{influencer_id}/roi_analysis/{analysis_id}/
  - brand_fit_score: Number
  - brand_fit_description: String
  - risk_level: String
  - risk_description: String
  - influencer_analysis: Map
  - created_at: Timestamp
```

## Migration Process

### 1. Setup Firestore Database

```javascript
// Pseudocode
async function setupFirestoreDatabase() {
  // Initialize Firebase
  const app = initializeApp({
    credential: cert(serviceAccount),
    storageBucket: bucketName
  });
  
  // Initialize Firestore
  const db = getFirestore();
  db.settings({
    ignoreUndefinedProperties: true
  });
  
  // Create indexes
  // ...
  
  return db;
}
```

### 2. Migrate Influencer Data

```javascript
// Pseudocode
async function migrateInfluencerData(db, storage, bucketName) {
  // Get all influencer JSON files from Cloud Storage
  const [files] = await storage.bucket(bucketName).getFiles({ prefix: 'influencers/' });
  
  // Process each file
  for (const file of files) {
    // Skip non-JSON files
    if (!file.name.endsWith('_raw_data.json')) {
      continue;
    }
    
    // Extract username from filename
    const username = file.name.split('/')[1].split('_raw_data.json')[0];
    
    // Download and parse the JSON file
    const [contents] = await file.download();
    const rawData = JSON.parse(contents.toString('utf8'));
    
    // Create or update influencer document
    const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
    const influencerSnapshot = await influencerRef.get();
    
    let influencerId;
    if (influencerSnapshot.empty) {
      // Create a new influencer document
      const newInfluencerRef = db.collection('influencers').doc();
      influencerId = newInfluencerRef.id;
      
      // Prepare influencer data
      const influencerData = {
        username: username,
        full_name: rawData.instagram?.full_name || '',
        email: rawData.email || '',
        location: rawData.location || '',
        speaking_language: rawData.speaking_language || '',
        has_brand_deals: rawData.has_brand_deals || false,
        has_link_in_bio: rawData.has_link_in_bio || false,
        is_business: rawData.is_business || false,
        is_creator: rawData.is_creator || false,
        platforms: {},
        creator_has: rawData.creator_has || [],
        created_at: db.Timestamp.now(),
        last_updated: db.Timestamp.now()
      };
      
      // Add platform-specific data
      if (rawData.instagram) {
        influencerData.platforms.instagram = {
          username: rawData.instagram.username,
          follower_count: rawData.instagram.follower_count,
          engagement_percent: rawData.instagram.engagement_percent,
          biography: rawData.instagram.biography,
          profile_image_url: rawData.instagram.profile_picture_hd,
          niches: rawData.instagram.niches || {
            primary: '',
            secondary: []
          },
          hashtags: rawData.instagram.hashtags || [],
          posting_frequency_recent_months: rawData.instagram.posting_frequency_recent_months,
          follower_growth: rawData.instagram.follower_growth || rawData.instagram.creator_follower_growth || {
            three_months_ago: 0,
            six_months_ago: 0,
            nine_months_ago: 0,
            twelve_months_ago: 0
          }
        };
      }
      
      // Add other platforms
      // ...
      
      // Save influencer document
      await db.collection('influencers').doc(influencerId).set(influencerData);
    } else {
      // Use existing influencer document
      influencerId = influencerSnapshot.docs[0].id;
    }
    
    // Store raw data reference
    const timestamp = Date.now();
    const rawStoragePath = `influencers/${influencerId}/raw_data/influencers_club_${timestamp}.json`;
    
    // Copy raw data to new location
    await storage.bucket(bucketName).file(file.name).copy(storage.bucket(bucketName).file(rawStoragePath));
    
    // Add raw data reference to Firestore
    await db.collection('influencers').doc(influencerId).collection('raw_data').add({
      source: 'influencers_club',
      storage_path: rawStoragePath,
      processed_storage_path: '',
      pulled_at: db.Timestamp.now(),
      processed_at: db.Timestamp.now()
    });
    
    console.log(`Migrated influencer: ${username}`);
  }
}
```

### 3. Migrate Campaign Data

```javascript
// Pseudocode
async function migrateCampaignData(db, storage, bucketName) {
  // Get all campaign JSON files from Cloud Storage
  const [files] = await storage.bucket(bucketName).getFiles({ prefix: 'runs/' });
  
  // Group files by report ID
  const reportGroups = {};
  for (const file of files) {
    const match = file.name.match(/\/runs\/(.+?)_(.+?)\.json/);
    if (match) {
      const reportId = match[1];
      const fileType = match[2];
      
      if (!reportGroups[reportId]) {
        reportGroups[reportId] = {};
      }
      
      reportGroups[reportId][fileType] = file;
    }
  }
  
  // Process each report group
  for (const [reportId, files] of Object.entries(reportGroups)) {
    // Create client if it doesn't exist
    const clientId = 'default_client'; // Replace with actual client ID
    const clientRef = db.collection('clients').doc(clientId);
    const clientDoc = await clientRef.get();
    
    if (!clientDoc.exists) {
      await clientRef.set({
        name: 'Default Client',
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now(),
        settings: {}
      });
    }
    
    // Create campaign
    const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(reportId);
    
    // Process campaign_analysis file
    if (files.campaign_analysis) {
      const [contents] = await files.campaign_analysis.download();
      const campaignData = JSON.parse(contents.toString('utf8'));
      
      await campaignRef.set({
        name: campaignData.name || 'Untitled Campaign',
        report_id: reportId,
        product_description: campaignData.product_description || '',
        influencer_gender: campaignData.influencer_gender || '',
        influencer_niche: campaignData.influencer_niche || '',
        influencer_age: campaignData.influencer_age || '',
        influencer_personality: campaignData.influencer_personality || '',
        influencer_aesthetic: campaignData.influencer_aesthetic || '',
        min_follower_count: campaignData.min_follower_count || 0,
        max_follower_count: campaignData.max_follower_count || 0,
        min_engagement_rate: campaignData.min_engagement_rate || 0,
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now(),
        status: 'active'
      });
    }
    
    // Process discovery_results file
    if (files.discovery_results) {
      const [contents] = await files.discovery_results.download();
      const discoveryData = JSON.parse(contents.toString('utf8'));
      
      const discoveryRef = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(reportId)
        .collection('discovered_influencers').doc();
        
      await discoveryRef.set({
        similar_accounts: discoveryData.similar_accounts || [],
        created_at: db.Timestamp.now()
      });
    }
    
    // Process influencer-specific files
    const influencerFiles = {};
    for (const [fileType, file] of Object.entries(files)) {
      const match = file.name.match(/\/runs\/(.+?)_(.+?)_(.+?)\.json/);
      if (match) {
        const reportId = match[1];
        const username = match[2];
        const analysisType = match[3];
        
        if (!influencerFiles[username]) {
          influencerFiles[username] = {};
        }
        
        influencerFiles[username][analysisType] = file;
      }
    }
    
    // Process each influencer
    for (const [username, files] of Object.entries(influencerFiles)) {
      // Get influencer ID
      const influencerRef = db.collection('influencers').where('username', '==', username).limit(1);
      const influencerSnapshot = await influencerRef.get();
      
      if (influencerSnapshot.empty) {
        console.warn(`Influencer not found: ${username}`);
        continue;
      }
      
      const influencerId = influencerSnapshot.docs[0].id;
      
      // Create campaign influencer
      const campaignInfluencerRef = db.collection('clients').doc(clientId)
        .collection('campaigns').doc(reportId)
        .collection('campaign_influencers').doc(influencerId);
        
      await campaignInfluencerRef.set({
        influencer_id: influencerId,
        username: username,
        status: 'enriched',
        created_at: db.Timestamp.now(),
        updated_at: db.Timestamp.now()
      });
      
      // Process web_analysis file
      if (files.web_analysis) {
        const [contents] = await files.web_analysis.download();
        const webAnalysisData = JSON.parse(contents.toString('utf8'));
        
        const webAnalysisRef = await db.collection('influencers').doc(influencerId)
          .collection('web_analysis').doc();
          
        await webAnalysisRef.set({
          name: webAnalysisData.name || '',
          sentiment_score: webAnalysisData.sentiment_score || 0,
          risk_level: webAnalysisData.risk_level || 'Medium',
          deep_dive_report: webAnalysisData.deep_dive_report || {},
          created_at: db.Timestamp.now(),
          updated_at: db.Timestamp.now()
        });
      }
      
      // Process aesthetic_analysis file
      if (files.aesthetic_analysis) {
        const [contents] = await files.aesthetic_analysis.download();
        const aestheticAnalysisData = JSON.parse(contents.toString('utf8'));
        
        const aestheticAnalysisRef = await campaignInfluencerRef
          .collection('aesthetic_analysis').doc();
          
        await aestheticAnalysisRef.set({
          name: aestheticAnalysisData.name || '',
          brand_fit_score: aestheticAnalysisData.brand_fit_score || 0,
          content_analysis: aestheticAnalysisData.content_analysis || {},
          created_at: db.Timestamp.now()
        });
      }
      
      // Process roi_analysis file
      if (files.roi_analysis) {
        const [contents] = await files.roi_analysis.download();
        const roiAnalysisData = JSON.parse(contents.toString('utf8'));
        
        const roiAnalysisRef = await campaignInfluencerRef
          .collection('roi_analysis').doc();
          
        await roiAnalysisRef.set({
          brand_fit_score: roiAnalysisData.brand_fit_score || 0,
          brand_fit_description: roiAnalysisData.brand_fit_description || '',
          risk_level: roiAnalysisData.risk_level || 'Medium',
          risk_description: roiAnalysisData.risk_description || '',
          influencer_analysis: roiAnalysisData.influencer_analysis || {},
          created_at: db.Timestamp.now()
        });
      }
      
      console.log(`Migrated campaign influencer: ${username}`);
    }
    
    console.log(`Migrated campaign: ${reportId}`);
  }
}
```

### 4. Validate Migrated Data

```javascript
// Pseudocode
async function validateMigratedData(db, storage, bucketName) {
  // Validate influencer data
  const influencersSnapshot = await db.collection('influencers').get();
  console.log(`Migrated ${influencersSnapshot.size} influencers`);
  
  // Validate campaign data
  const clientsSnapshot = await db.collection('clients').get();
  for (const clientDoc of clientsSnapshot.docs) {
    const clientId = clientDoc.id;
    const campaignsSnapshot = await db.collection('clients').doc(clientId).collection('campaigns').get();
    console.log(`Migrated ${campaignsSnapshot.size} campaigns for client ${clientId}`);
    
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      const campaignInfluencersSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').get();
      console.log(`Migrated ${campaignInfluencersSnapshot.size} influencers for campaign ${campaignId}`);
    }
  }
  
  // Validate web analysis data
  const webAnalysisCount = await countWebAnalyses(db);
  console.log(`Migrated ${webAnalysisCount} web analyses`);
  
  // Validate aesthetic analysis data
  const aestheticAnalysisCount = await countAestheticAnalyses(db);
  console.log(`Migrated ${aestheticAnalysisCount} aesthetic analyses`);
  
  // Validate ROI analysis data
  const roiAnalysisCount = await countROIAnalyses(db);
  console.log(`Migrated ${roiAnalysisCount} ROI analyses`);
}

async function countWebAnalyses(db) {
  let count = 0;
  const influencersSnapshot = await db.collection('influencers').get();
  for (const influencerDoc of influencersSnapshot.docs) {
    const influencerId = influencerDoc.id;
    const webAnalysesSnapshot = await db.collection('influencers').doc(influencerId)
      .collection('web_analysis').get();
    count += webAnalysesSnapshot.size;
  }
  return count;
}

async function countAestheticAnalyses(db) {
  let count = 0;
  const clientsSnapshot = await db.collection('clients').get();
  for (const clientDoc of clientsSnapshot.docs) {
    const clientId = clientDoc.id;
    const campaignsSnapshot = await db.collection('clients').doc(clientId).collection('campaigns').get();
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      const campaignInfluencersSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').get();
      for (const campaignInfluencerDoc of campaignInfluencersSnapshot.docs) {
        const influencerId = campaignInfluencerDoc.id;
        const aestheticAnalysesSnapshot = await db.collection('clients').doc(clientId)
          .collection('campaigns').doc(campaignId)
          .collection('campaign_influencers').doc(influencerId)
          .collection('aesthetic_analysis').get();
        count += aestheticAnalysesSnapshot.size;
      }
    }
  }
  return count;
}

async function countROIAnalyses(db) {
  let count = 0;
  const clientsSnapshot = await db.collection('clients').get();
  for (const clientDoc of clientsSnapshot.docs) {
    const clientId = clientDoc.id;
    const campaignsSnapshot = await db.collection('clients').doc(clientId).collection('campaigns').get();
    for (const campaignDoc of campaignsSnapshot.docs) {
      const campaignId = campaignDoc.id;
      const campaignInfluencersSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').get();
      for (const campaignInfluencerDoc of campaignInfluencersSnapshot.docs) {
        const influencerId = campaignInfluencerDoc.id;
        const roiAnalysesSnapshot = await db.collection('clients').doc(clientId)
          .collection('campaigns').doc(campaignId)
          .collection('campaign_influencers').doc(influencerId)
          .collection('roi_analysis').get();
        count += roiAnalysesSnapshot.size;
      }
    }
  }
  return count;
}
```

### 5. Implement Fallback Mechanism

```javascript
// Pseudocode
async function getCombinedData(clientId, campaignId, influencerId) {
  try {
    // Try to get data from Firestore
    const combinedAnalysis = await generateCombinedAnalysis(clientId, campaignId, influencerId);
    return combinedAnalysis;
  } catch (error) {
    console.error('Error getting data from Firestore:', error);
    
    // Fall back to JSON files
    try {
      // Get campaign influencer
      const campaignInfluencerSnapshot = await db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').doc(influencerId)
        .get();
      
      if (!campaignInfluencerSnapshot.exists) {
        throw new Error(`Campaign influencer not found: ${influencerId}`);
      }
      
      const campaignInfluencer = campaignInfluencerSnapshot.data();
      const username = campaignInfluencer.username;
      
      // Get combined analysis from JSON file
      const combinedAnalysisPath = `runs/${campaignId}_${username}_combined_analyses.json`;
      const combinedAnalysis = await getCachedJson(bucketName, combinedAnalysisPath);
      
      if (combinedAnalysis === false) {
        throw new Error(`Combined analysis not found: ${combinedAnalysisPath}`);
      }
      
      return combinedAnalysis;
    } catch (fallbackError) {
      console.error('Error getting data from JSON files:', fallbackError);
      throw error; // Throw the original error
    }
  }
}
```

## Migration Script

```javascript
// Pseudocode
async function migrateToFirestore() {
  try {
    // Setup Firestore database
    const db = await setupFirestoreDatabase();
    
    // Initialize Cloud Storage
    const storage = new Storage();
    const bucketName = 'palas-run-cache';
    
    // Migrate influencer data
    await migrateInfluencerData(db, storage, bucketName);
    
    // Migrate campaign data
    await migrateCampaignData(db, storage, bucketName);
    
    // Validate migrated data
    await validateMigratedData(db, storage, bucketName);
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}
```

## Execution Plan

The migration will be executed in the following steps:

1. **Backup**: Back up all existing JSON data
2. **Test Migration**: Run the migration script on a test environment
3. **Validate Test Migration**: Validate the migrated data in the test environment
4. **Production Migration**: Run the migration script on the production environment
5. **Validate Production Migration**: Validate the migrated data in the production environment
6. **Update Application**: Update the application to use Firestore
7. **Monitor**: Monitor the application for any issues

## Rollback Plan

If the migration fails or causes issues, the following rollback plan will be executed:

1. **Revert Code**: Revert the application code to use JSON files
2. **Restore Backup**: Restore the backup of the JSON files if necessary
3. **Investigate**: Investigate the cause of the failure
4. **Fix Issues**: Fix any issues identified
5. **Retry Migration**: Retry the migration with the fixed issues

## Conclusion

This migration plan provides a detailed roadmap for transitioning from JSON files to Firestore. By following this plan, the migration can be executed smoothly and with minimal disruption to the application.

# Influencers Club Discovery API Implementation Plan

## Overview
This document outlines the plan to replace the current influencer identification flow (lines 898-1066 in `old_implementation/analysisFlow.js`) with the Influencers Club Discovery API. The implementation will leverage the Discovery API's `ai_search` parameter as the primary search mechanism, along with platform-specific filtering.

## Current Flow Analysis
The current implementation:
1. Uses a seed influencer agent to identify initial influencers
2. Fetches lookalike creators for each seed influencer
3. Filters accounts based on campaign requirements
4. Ranks influencers based on campaign fit

## New Implementation Plan

### 1. Update Campaign Analysis to Include Discovery Parameters

#### File: `services/campaign-service.js`

```javascript
// Pseudocode
async function generateCampaignBrief(campaignData) {
  // Existing campaign analysis logic

  // Add new fields for Discovery API
  const campaignPrompt = `
    Based on the following campaign requirements, generate:

    1. A detailed influencer description (2-3 sentences) that can be used for AI search
    2. The main platform to search for influencers (instagram, youtube, tiktok, etc.)
    3. Any additional search parameters that would help find the right influencers

    Campaign data: ${JSON.stringify(campaignData)}

    Output format:
    {
      // Existing campaign analysis fields
      ...

      // New fields for Discovery API
      "influencer_description": "Detailed description for AI search",
      "main_platform": "instagram", // or youtube, tiktok, etc.
      "additional_search_params": {
        // Any additional parameters
      }
    }
  `;

  // Process with OpenAI
  const enhancedCampaignAnalysis = await openai.processAgent(CAMPAIGN_ANALYSIS_AGENT, campaignPrompt);

  return enhancedCampaignAnalysis;
}
```

### 2. Create Influencers Club Discovery Connector

#### File: `connectors/influencers-club-discovery-connector.js`

```javascript
// Pseudocode
class InfluencersClubDiscoveryConnector {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.db = getFirestoreDb();
  }

  // Method to discover influencers using the Discovery API
  async discoverInfluencers(params) {
    const url = "https://api-dashboard.influencers.club/public/v1/discovery/";

    const payload = {
      platform: params.main_platform || "instagram",
      paging: {
        limit: params.limit || 50,
        page: params.page || 1
      },
      sort: {
        sort_by: "relevancy",
        sort_order: "DESC"
      },
      filters: {
        // Primary search parameter
        ai_search: params.influencer_description,

        // Secondary parameters
        number_of_followers: {
          min: params.min_follower_count || 10000,
          max: params.max_follower_count || null
        },
        average_likes: {
          min: params.min_likes || null,
          max: null
        },
        average_comments: {
          min: params.min_comments || null,
          max: null
        },
        average_views: {
          min: params.min_views || null,
          max: null
        },
        location: params.location || "",
        gender: params.gender || "",
        // Additional filters as needed
      }
    };

    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };

    try {
      const response = await axios.post(url, payload, { headers });
      return response.data;
    } catch (error) {
      console.error('Error calling Discovery API:', error);
      throw error;
    }
  }

  // Method to store discovery results in Firestore
  async storeDiscoveryResults(clientId, campaignId, results, influencerDescription) {
    // Store discovery results in Firestore
    const discoveryRef = this.db.collection('influencer_discovery').doc();

    await discoveryRef.set({
      campaign_id: campaignId,
      client_id: clientId,
      timestamp: this.db.Timestamp.now(),
      influencer_description: influencerDescription,
      results: results
    });

    // Also store individual influencers
    for (const influencer of results.similar_accounts) {
      await this.storeInfluencer(influencer);
    }

    return discoveryRef.id;
  }

  // Method to store individual influencer data
  async storeInfluencer(influencerData) {
    // Check if influencer already exists
    const influencerRef = this.db.collection('influencers')
      .where('username', '==', influencerData.username)
      .limit(1);

    const snapshot = await influencerRef.get();

    if (snapshot.empty) {
      // Create new influencer document
      await this.db.collection('influencers').doc().set({
        username: influencerData.username,
        full_name: influencerData.full_name || '',
        platforms: {
          [influencerData.platform]: {
            follower_count: influencerData.follower_count,
            engagement_percent: influencerData.engagement_percent
          }
        },
        niches: influencerData.niches || [],
        created_at: this.db.Timestamp.now(),
        last_updated: this.db.Timestamp.now(),
        discovery_count: 1
      });
    } else {
      // Update existing influencer document
      const docId = snapshot.docs[0].id;
      const docData = snapshot.docs[0].data();

      // Update platform data if needed
      const platforms = docData.platforms || {};
      platforms[influencerData.platform] = {
        follower_count: influencerData.follower_count,
        engagement_percent: influencerData.engagement_percent
      };

      // Merge niches
      const niches = new Set([...(docData.niches || []), ...(influencerData.niches || [])]);

      await this.db.collection('influencers').doc(docId).update({
        platforms,
        niches: Array.from(niches),
        last_updated: this.db.Timestamp.now(),
        discovery_count: (docData.discovery_count || 0) + 1
      });
    }
  }
}

export default InfluencersClubDiscoveryConnector;
```

### 3. Update Discovery Service

#### File: `services/discovery-service.js`

```javascript
// Pseudocode
import InfluencersClubDiscoveryConnector from '../connectors/influencers-club-discovery-connector.js';
import { processAgent } from '../connectors/openai-connector.js';

// Constants
const RANKING_AGENT = 'asst_we8mAAj5oluautOVautmiJqn';

// Function to discover influencers using the Discovery API
async function discoverInfluencers(campaignData) {
  const connector = new InfluencersClubDiscoveryConnector();

  // Prepare parameters for Discovery API
  const discoveryParams = {
    main_platform: campaignData.main_platform || 'instagram',
    influencer_description: campaignData.influencer_description,
    min_follower_count: campaignData.min_follower_count,
    max_follower_count: campaignData.max_follower_count,
    min_engagement_rate: campaignData.min_engagement_rate,
    // Additional parameters as needed
  };

  // Call Discovery API
  const discoveryResults = await connector.discoverInfluencers(discoveryParams);

  return discoveryResults;
}

// Function to get existing niches from the database
async function getExistingNiches() {
  const db = getFirestoreDb();
  const nichesSnapshot = await db.collection('niches').get();

  const existingNiches = [];
  nichesSnapshot.forEach(doc => {
    existingNiches.push(doc.id);
  });

  return existingNiches;
}

// Function to prioritize and add niches to influencers
async function prioritizeInfluencers(campaignData, discoveredInfluencers) {
  // Get existing niches from the database
  const existingNiches = await getExistingNiches();

  // Prepare prompt for ranking agent
  const rankingPrompt = `
    Analyze these influencers: [${JSON.stringify(discoveredInfluencers)}]
    For the campaign: [${JSON.stringify(campaignData)}]

    Your tasks:
    1. Rank the influencers based on their fit for the campaign
    2. For each influencer, identify 5-10 specific niches that best describe their content focus

    Here are the existing niches in our system: ${JSON.stringify(existingNiches)}

    IMPORTANT: When assigning niches to influencers, prioritize using existing niches from the list above when they apply.
    Only create new niches when none of the existing ones accurately describe an important aspect of the influencer's content.
    This helps standardize our categorization while allowing for evolution when needed.

    Output format:
    {
      "similar_accounts": [
        {
          "username": "...",
          "follower_count": 123456,
          "engagement_percent": 3.2,
          "sort_rationale": "...",
          "niches": ["fitness", "nutrition", "wellness", ...]
        },
        ...
      ]
    }
  `;

  // Process with OpenAI
  const rankedInfluencers = await processAgent(RANKING_AGENT, rankingPrompt);

  // Update niches collection with any new niches
  await updateNichesCollection(rankedInfluencers);

  return rankedInfluencers;
}

// Function to update the niches collection with any new niches
async function updateNichesCollection(rankedInfluencers) {
  const db = getFirestoreDb();
  const existingNiches = await getExistingNiches();

  // Extract all niches from the ranked influencers
  const allNiches = new Set();
  rankedInfluencers.similar_accounts.forEach(influencer => {
    if (influencer.niches && Array.isArray(influencer.niches)) {
      influencer.niches.forEach(niche => allNiches.add(niche));
    }
  });

  // Add any new niches to the database
  for (const niche of allNiches) {
    if (!existingNiches.includes(niche)) {
      await db.collection('niches').doc(niche).set({
        count: 1,
        created_at: db.Timestamp.now(),
        last_used: db.Timestamp.now()
      });
    } else {
      // Update existing niche usage count and timestamp
      await db.collection('niches').doc(niche).update({
        count: db.FieldValue.increment(1),
        last_used: db.Timestamp.now()
      });
    }
  }
}

// Function to store discovery results
async function storeDiscoveryResults(clientId, campaignId, results, campaignData) {
  const connector = new InfluencersClubDiscoveryConnector();

  return await connector.storeDiscoveryResults(
    clientId,
    campaignId,
    results,
    campaignData.influencer_description
  );
}

export { discoverInfluencers, prioritizeInfluencers, storeDiscoveryResults };
```

### 4. Update Main Analysis Flow

#### File: `analysis-flow.js` (or equivalent new implementation)

```javascript
// Pseudocode for the new influencer discovery flow
async function discoverInfluencersForCampaign(clientId, campaignData) {
  // Generate campaign analysis with discovery parameters
  const campaignAnalysis = await generateCampaignBrief(campaignData);

  // Discover influencers using Discovery API
  const discoveredInfluencers = await discoverInfluencers(campaignAnalysis);

  // Prioritize influencers and add niches
  const prioritizedInfluencers = await prioritizeInfluencers(
    campaignAnalysis,
    discoveredInfluencers
  );

  // Store results in Firestore with niches for future reference
  await storeDiscoveryResults(
    clientId,
    campaignId,
    prioritizedInfluencers,
    campaignAnalysis
  );

  return prioritizedInfluencers;
}
```

## Firestore Data Structure

### Collection: `influencer_discovery`
- Document ID: Auto-generated
- Fields:
  - `campaign_id`: Reference to campaign
  - `client_id`: Reference to client
  - `timestamp`: When the discovery was performed
  - `influencer_description`: The description used for AI search
  - `results`: Array of discovered influencers with rankings

### Collection: `influencers`
- Document ID: Auto-generated or based on username
- Fields:
  - `username`: Influencer username
  - `full_name`: Influencer's full name
  - `platforms`: Object containing platform-specific data
    ```javascript
    {
      "instagram": {
        "follower_count": 123456,
        "engagement_percent": 3.2
      },
      "youtube": {
        "subscriber_count": 50000,
        "engagement_percent": 4.5
      }
    }
    ```
  - `niches`: Array of niches/categories identified by the ranking agent
  - `created_at`: Timestamp of first discovery
  - `last_updated`: Timestamp of last update
  - `discovery_count`: Number of times this influencer has been discovered

### Collection: `niches`
- Document ID: The niche name (e.g., "fitness", "beauty", "tech")
- Fields:
  - `count`: Number of influencers associated with this niche
  - `created_at`: Timestamp when the niche was first created
  - `last_used`: Timestamp when the niche was last assigned to an influencer
  - `related_niches`: Array of frequently co-occurring niches (optional, for future enhancement)
  - `parent_category`: Broader category this niche belongs to (optional, for future enhancement)

## Testing Plan

1. Create test cases with various campaign requirements
2. Test the Discovery API with different parameters
3. Verify niche extraction and storage
4. Compare results with the old implementation

## Migration Strategy

1. Implement the new connector and service
2. Run both implementations in parallel for validation
3. Gradually transition to the new implementation

## Future Enhancements

In the future, when a critical mass of influencers is in the database:

1. Implement a function to search for prequalified influencers in Firestore
2. Add a step to check for existing influencers before calling the Discovery API
3. Create a ranking system that combines both new discoveries and existing influencers

// storage.js
// Cloud Storage configuration

import { Storage } from '@google-cloud/storage';
import { DEFAULT_BUCKET_NAME } from './constants.js';

/**
 * Initialize Google Cloud Storage
 * @returns {Object} - The Storage instance and bucket name
 */
function initializeStorage() {
  const storage = new Storage();
  const bucketName = process.env.STORAGE_BUCKET || DEFAULT_BUCKET_NAME;
  
  return { storage, bucketName };
}

export { initializeStorage };

# Firestore Implementation Strategy

This document outlines the strategy for implementing the Firestore database structure while preserving the existing functionality of the system.

## Implementation Principles

1. **Preserve Existing Functionality**: All existing functionality must be preserved exactly as is.
2. **Small, Deliberate Changes**: Make changes in small, deliberate chunks to minimize risk.
3. **Backward Compatibility**: Maintain backward compatibility with the existing JSON-based system.
4. **Thorough Testing**: Test each change thoroughly before moving on to the next.

## Implementation Phases

The implementation will be divided into several phases, each focusing on a specific aspect of the system:

### Phase 1: Firebase/Firestore Setup

1. **Initialize Firebase**: Set up Firebase and Firestore in the application.
2. **Configure Security Rules**: Implement security rules to protect the data.
3. **Create Indexes**: Create necessary indexes for efficient queries.

```javascript
// Initialize Firebase
const app = initializeApp({
  credential: cert(serviceAccount),
  storageBucket: bucketName
});

// Initialize Firestore
const db = getFirestore();
db.settings({
  ignoreUndefinedProperties: true
});
```

### Phase 2: Update Helper Functions

1. **Update cacheImage**: Modify to include influencer ID in the path.
2. **Update getCachedJson**: Add Firestore support while maintaining Cloud Storage fallback.
3. **Update writeJsonToBucket**: Add Firestore support while continuing to write to Cloud Storage.

### Phase 3: Implement Influencers Club Connector

1. **Create Connector Class**: Implement the InfluencersClubConnector class.
2. **Implement fetchInfluencerData**: Method to fetch data from the API.
3. **Implement processRawData**: Method to process raw data.
4. **Implement enrichInfluencer**: Method to enrich influencer data.

### Phase 4: Update Campaign Analysis Flow

1. **Update Campaign Analysis**: Modify to store in Firestore.
2. **Update Influencer Discovery**: Modify to store in Firestore.
3. **Update Web Analysis**: Modify to store in Firestore.
4. **Update Aesthetic Analysis**: Modify to store in Firestore.
5. **Update ROI Analysis**: Modify to store in Firestore.

### Phase 5: Implement Analysis Helpers

1. **Implement generateCombinedAnalysis**: Function to generate combined analysis on-demand.
2. **Implement generateMergedAnalysis**: Function to generate merged analysis on-demand.

### Phase 6: Update API Endpoints

1. **Update Campaign Endpoints**: Modify to use Firestore.
2. **Update Influencer Endpoints**: Modify to use Firestore.
3. **Update Analysis Endpoints**: Modify to use Firestore.

### Phase 7: Data Migration

1. **Migrate Existing Data**: Move existing JSON data to Firestore.
2. **Validate Migrated Data**: Ensure all data is properly migrated.

## Detailed Implementation Plan

### Phase 1: Firebase/Firestore Setup

#### Step 1: Initialize Firebase

```javascript
// firebase.js
import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';

/**
 * Initialize Firebase Admin SDK
 * @param {Object} serviceAccount - The service account credentials
 * @param {string} storageBucket - The storage bucket name
 * @returns {Object} - The initialized Firebase app
 */
function initializeFirebase(serviceAccount, storageBucket) {
  // Check if Firebase is already initialized
  try {
    const app = initializeApp({
      credential: cert(serviceAccount),
      storageBucket: storageBucket
    });
    
    // Initialize Firestore with settings
    const db = getFirestore();
    db.settings({
      ignoreUndefinedProperties: true
    });
    
    console.log('Firebase initialized successfully');
    return app;
  } catch (error) {
    if (error.code === 'app/duplicate-app') {
      console.log('Firebase already initialized');
      return initializeApp();
    } else {
      console.error('Error initializing Firebase:', error);
      throw error;
    }
  }
}

export { initializeFirebase };
```

#### Step 2: Configure Security Rules

```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && request.auth.token.admin == true;
    }
    
    function isClientMember(clientId) {
      return isAuthenticated() && 
        (request.auth.token.client_id == clientId || 
         request.auth.token.client_ids.hasAny([clientId]));
    }
    
    // Global influencer data can be read by any authenticated user
    match /influencers/{influencerId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      
      // Web analysis can be read by any authenticated user
      match /web_analysis/{analysisId} {
        allow read: if isAuthenticated();
        allow write: if isAdmin();
      }
      
      // Raw data references can be read by any authenticated user
      match /raw_data/{sourceId} {
        allow read: if isAuthenticated();
        allow write: if isAdmin();
      }
    }
    
    // Client data can only be accessed by the respective client or admin
    match /clients/{clientId} {
      allow read: if isClientMember(clientId) || isAdmin();
      allow write: if isAdmin();
      
      // Campaigns can be accessed by client members or admin
      match /campaigns/{campaignId} {
        allow read: if isClientMember(clientId) || isAdmin();
        allow write: if isClientMember(clientId) || isAdmin();
        
        // Discovered influencers can be accessed by client members or admin
        match /discovered_influencers/{discoveryId} {
          allow read: if isClientMember(clientId) || isAdmin();
          allow write: if isClientMember(clientId) || isAdmin();
        }
        
        // Campaign influencers can be accessed by client members or admin
        match /campaign_influencers/{influencerId} {
          allow read: if isClientMember(clientId) || isAdmin();
          allow write: if isClientMember(clientId) || isAdmin();
          
          // Aesthetic analysis can be accessed by client members or admin
          match /aesthetic_analysis/{analysisId} {
            allow read: if isClientMember(clientId) || isAdmin();
            allow write: if isClientMember(clientId) || isAdmin();
          }
          
          // ROI analysis can be accessed by client members or admin
          match /roi_analysis/{analysisId} {
            allow read: if isClientMember(clientId) || isAdmin();
            allow write: if isClientMember(clientId) || isAdmin();
          }
        }
      }
    }
  }
}
```

### Phase 2: Update Helper Functions

#### Step 1: Update cacheImage

```javascript
async function cacheImage(imageUrl, influencerId = null) {
  if (!imageUrl) return "";
  
  try {
    // Generate a unique filename based on the URL
    const urlHash = crypto.createHash('md5').update(imageUrl).digest('hex');
    const filename = `${urlHash}.jpg`;
    
    // If influencerId is provided, include it in the path
    const cachePath = influencerId 
      ? `influencers/${influencerId}/images/${filename}`
      : `image-cache/${filename}`;
    
    // Check if the image is already cached
    try {
      await storage.bucket(bucketName).file(cachePath).getMetadata();
      console.log(`Image already cached: ${imageUrl}`);
      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (error) {
      // Image not cached, proceed with download
    }
    
    // Download the image
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const buffer = Buffer.from(response.data, 'binary');
    
    // Upload to Cloud Storage
    await storage.bucket(bucketName).file(cachePath).save(buffer, {
      metadata: {
        contentType: response.headers['content-type'] || 'image/jpeg'
      }
    });
    
    // Make the file publicly accessible
    await storage.bucket(bucketName).file(cachePath).makePublic();
    
    console.log(`Image cached: ${imageUrl}`);
    return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
  } catch (error) {
    console.error(`Error caching image: ${error.message}`);
    return imageUrl; // Return the original URL if caching fails
  }
}
```

#### Step 2: Update getCachedJson

```javascript
async function getCachedJson(bucketName, filePath, firestoreRef = null) {
  try {
    // If a Firestore reference is provided, try to get the data from Firestore first
    if (firestoreRef) {
      const doc = await firestoreRef.get();
      if (doc.exists) {
        console.log(`Data found in Firestore: ${firestoreRef.path}`);
        return doc.data();
      }
    }
    
    // Fall back to Cloud Storage
    const file = storage.bucket(bucketName).file(filePath);
    const [exists] = await file.exists();
    
    if (!exists) {
      console.log(`File not found: ${filePath}`);
      return false;
    }
    
    const [content] = await file.download();
    return JSON.parse(content.toString());
  } catch (error) {
    console.error(`Error getting cached JSON: ${error.message}`);
    return false;
  }
}
```

#### Step 3: Update writeJsonToBucket

```javascript
async function writeJsonToBucket(filePath, jsonData, firestoreRef = null) {
  try {
    // Write to Cloud Storage
    const file = storage.bucket(bucketName).file(filePath);
    await file.save(JSON.stringify(jsonData, null, 2), {
      metadata: { contentType: 'application/json' }
    });
    console.log(`JSON written to ${filePath}`);
    
    // If a Firestore reference is provided, also write to Firestore
    if (firestoreRef) {
      await firestoreRef.set(jsonData);
      console.log(`JSON written to Firestore: ${firestoreRef.path}`);
    }
    
    return true;
  } catch (error) {
    console.error(`Error writing JSON: ${error.message}`);
    throw error;
  }
}
```

### Phase 3: Implement Influencers Club Connector

See the `influencers_club_connector_implementation_plan.md` document for detailed implementation steps.

### Phase 4: Update Campaign Analysis Flow

#### Step 1: Update Campaign Analysis

```javascript
// Current Implementation
const campaignJSON = await processAgent(CAMPAIGN_ANALYSIS_AGENT, phase1Prompt);
await writeJsonToBucket(`/runs/${report_id}_campaign_analysis.json`, campaignJSON);

// Firestore Implementation
const campaignJSON = await processAgent(CAMPAIGN_ANALYSIS_AGENT, phase1Prompt);

// Store in Firestore
const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
await campaignRef.set({
  name: campaignJSON.name,
  report_id: campaignId,
  product_description: campaignJSON.product_description,
  influencer_gender: campaignJSON.influencer_gender,
  influencer_niche: campaignJSON.influencer_niche,
  influencer_age: campaignJSON.influencer_age,
  influencer_personality: campaignJSON.influencer_personality,
  influencer_aesthetic: campaignJSON.influencer_aesthetic,
  min_follower_count: campaignJSON.min_follower_count,
  max_follower_count: campaignJSON.max_follower_count,
  min_engagement_rate: campaignJSON.min_engagement_rate,
  created_at: db.Timestamp.now(),
  updated_at: db.Timestamp.now(),
  status: "active"
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/campaign_analysis.json`, campaignJSON, campaignRef);
```

#### Step 2: Update Influencer Discovery

```javascript
// Current Implementation
const discoveryJSON = await processAgent(DISCOVERY_AGENT, discoveryPrompt);
await writeJsonToBucket(`/runs/${report_id}_discovery_results.json`, discoveryJSON);

// Firestore Implementation
const discoveryJSON = await processAgent(DISCOVERY_AGENT, discoveryPrompt);

// Store in Firestore
const discoveryRef = await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('discovered_influencers').doc();
  
await discoveryRef.set({
  similar_accounts: discoveryJSON.similar_accounts,
  created_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/discovery_results.json`, discoveryJSON, discoveryRef);
```

#### Step 3: Update Web Analysis

```javascript
// Current Implementation
const deepDiveResultsJSON = await processAgent(WEB_ANALYSIS_AGENT, phase3Prompt);
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_web_analysis.json`, deepDiveResultsJSON);

// Firestore Implementation
const deepDiveResultsJSON = await processAgent(WEB_ANALYSIS_AGENT, phase3Prompt);

// Store in Firestore
const webAnalysisRef = await db.collection('influencers').doc(influencerId)
  .collection('web_analysis').doc();
  
await webAnalysisRef.set({
  name: deepDiveResultsJSON.name,
  sentiment_score: deepDiveResultsJSON.sentiment_score,
  risk_level: deepDiveResultsJSON.risk_level,
  deep_dive_report: deepDiveResultsJSON.deep_dive_report,
  created_at: db.Timestamp.now(),
  updated_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/influencers/${influencerId}/web_analysis/${webAnalysisRef.id}.json`, deepDiveResultsJSON, webAnalysisRef);
```

#### Step 4: Update Aesthetic Analysis

```javascript
// Current Implementation
let aestheticAnalysisJSON = null;
for (let attempt = 1; attempt <= 10; attempt++) {
  const contentAnalysisResults = await openaiClient.beta.chat.completions.parse({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: phase5Instructions },
      { 
        role: "user", 
        content: [
          { type: "text", text: phase5Prompt },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${influencerImagesBase64}` } }
        ]
      }
    ],
    max_completion_tokens: 16000,
  });

  const contentString = contentAnalysisResults.choices[0].message.content;
  aestheticAnalysisJSON = extractJSON(contentString);

  if (aestheticAnalysisJSON !== null) {
    console.log(`Success on attempt ${attempt}`);
    break;
  }
  console.log(`Attempt ${attempt} returned null. Retrying...`);
}

await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_aesthetic_analysis.json`, aestheticAnalysisJSON);

// Firestore Implementation
let aestheticAnalysisJSON = null;
for (let attempt = 1; attempt <= 10; attempt++) {
  const contentAnalysisResults = await openaiClient.beta.chat.completions.parse({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: phase5Instructions },
      { 
        role: "user", 
        content: [
          { type: "text", text: phase5Prompt },
          { type: "image_url", image_url: { url: `data:image/jpeg;base64,${influencerImagesBase64}` } }
        ]
      }
    ],
    max_completion_tokens: 16000,
  });

  const contentString = contentAnalysisResults.choices[0].message.content;
  aestheticAnalysisJSON = extractJSON(contentString);

  if (aestheticAnalysisJSON !== null) {
    console.log(`Success on attempt ${attempt}`);
    break;
  }
  console.log(`Attempt ${attempt} returned null. Retrying...`);
}

// Store in Firestore
const aestheticAnalysisRef = await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('campaign_influencers').doc(influencerId)
  .collection('aesthetic_analysis').doc();
  
await aestheticAnalysisRef.set({
  name: aestheticAnalysisJSON.name,
  brand_fit_score: aestheticAnalysisJSON.brand_fit_score,
  content_analysis: aestheticAnalysisJSON.content_analysis,
  created_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/aesthetic_analysis.json`, aestheticAnalysisJSON, aestheticAnalysisRef);
```

#### Step 5: Update ROI Analysis

```javascript
// Current Implementation
const roiAnalysisJSON = await processAgent(ROI_ANALYSIS_AGENT, phase6Prompt);
await writeJsonToBucket(`/runs/${report_id}_${influencerInstaUsername}_roi_analysis.json`, roiAnalysisJSON);

// Firestore Implementation
const roiAnalysisJSON = await processAgent(ROI_ANALYSIS_AGENT, phase6Prompt);

// Store in Firestore
const roiAnalysisRef = await db.collection('clients').doc(clientId)
  .collection('campaigns').doc(campaignId)
  .collection('campaign_influencers').doc(influencerId)
  .collection('roi_analysis').doc();
  
await roiAnalysisRef.set({
  brand_fit_score: roiAnalysisJSON.brand_fit_score,
  brand_fit_description: roiAnalysisJSON.brand_fit_description,
  risk_level: roiAnalysisJSON.risk_level,
  risk_description: roiAnalysisJSON.risk_description,
  influencer_analysis: roiAnalysisJSON.influencer_analysis,
  created_at: db.Timestamp.now()
});

// For backward compatibility, also store as JSON
await writeJsonToBucket(`/clients/${clientId}/campaigns/${campaignId}/influencers/${influencerId}/roi_analysis.json`, roiAnalysisJSON, roiAnalysisRef);
```

### Phase 5: Implement Analysis Helpers

See the `analysis_helpers_implementation_plan.md` document for detailed implementation steps.

### Phase 6: Update API Endpoints

```javascript
// Current Implementation
app.post('/api/analyze', async (req, res) => {
  try {
    const result = await performInfluencerAnalysis(req.body);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error performing analysis:', error);
    res.status(500).json({ error: 'Failed to perform analysis' });
  }
});

// Firestore Implementation
app.post('/api/campaigns', async (req, res) => {
  try {
    const { campaignData, clientId } = req.body;
    const campaignId = await storeCampaignAnalysis(clientId, null, campaignData);
    res.status(200).json({ campaignId });
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({ error: 'Failed to create campaign' });
  }
});

app.post('/api/campaigns/:campaignId/discover', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { clientId } = req.body;
    const discoveryData = await discoverInfluencers(campaignId, clientId);
    res.status(200).json(discoveryData);
  } catch (error) {
    console.error('Error discovering influencers:', error);
    res.status(500).json({ error: 'Failed to discover influencers' });
  }
});

app.post('/api/campaigns/:campaignId/enrich', async (req, res) => {
  try {
    const { campaignId } = req.params;
    const { influencerUsername, clientId } = req.body;
    const result = await enrichInfluencer(influencerUsername, campaignId, clientId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error enriching influencer:', error);
    res.status(500).json({ error: 'Failed to enrich influencer' });
  }
});

app.get('/api/campaigns/:campaignId/influencers/:influencerId', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId } = req.query;
    const result = await generateMergedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting influencer analysis:', error);
    res.status(500).json({ error: 'Failed to get influencer analysis' });
  }
});
```

### Phase 7: Data Migration

See the `firestore_migration_plan.md` document for detailed migration steps.

## Testing Strategy

Each phase of the implementation should be thoroughly tested before moving on to the next:

1. **Unit Tests**: Test individual functions with mock data.
2. **Integration Tests**: Test the interaction between different components.
3. **End-to-End Tests**: Test the entire flow from campaign creation to influencer analysis.
4. **Backward Compatibility Tests**: Ensure the system still works with existing JSON data.

## Rollback Strategy

In case of issues, a rollback strategy should be in place:

1. **Revert Code Changes**: Revert to the previous version of the code.
2. **Continue Using JSON Files**: Fall back to using JSON files for storage.
3. **Maintain Dual Storage**: Continue writing to both Firestore and Cloud Storage during the transition.

## Conclusion

By following this implementation strategy, we can transition from JSON files to Firestore while preserving the existing functionality of the system. The key is to make small, deliberate changes and thoroughly test each change before moving on to the next.

#!/usr/bin/env node
/**
 * test-discovery-endpoint.js
 * Test script for the Discovery API endpoint
 *
 * This script tests the Discovery API endpoint by:
 * - Taking a campaign JSON file as input
 * - Sending it to the /api/discovery/discover endpoint
 * - Displaying the results
 *
 * Usage:
 * node test-discovery-endpoint.js --file=sample_jsons/campaign_analysis.json
 * or
 * node test-discovery-endpoint.js --campaign_id=existing_campaign_id
 */

// Core imports
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import { performance } from 'perf_hooks';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Constants
// Use the deployed Cloud Run URL
const API_BASE_URL = 'https://palas-influencer-intelligence-9815718377.us-central1.run.app/api';
const TEST_OUTPUTS_DIR = path.join(__dirname, '..', 'test_outputs');

// Ensure test_outputs directory exists
if (!fs.existsSync(TEST_OUTPUTS_DIR)) {
  fs.mkdirSync(TEST_OUTPUTS_DIR, { recursive: true });
}

// Logger utility
const logger = {
  section: (title) => {
    console.log('\n' + '='.repeat(80));
    console.log(`${title}`);
    console.log('='.repeat(80));
  },
  info: (message) => console.log(`ℹ️ ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  error: (message, error) => {
    console.error(`❌ ${message}`);
    if (error) {
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      } else if (error.request) {
        console.error('No response received');
      } else {
        console.error('Error message:', error.message);
      }
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }
  },
  debug: (message, data) => {
    if (process.env.DEBUG) {
      console.log(`🔍 ${message}`);
      if (data) console.log(data);
    }
  }
};

/**
 * Parse command line arguments
 * @returns {Object} - The parsed arguments
 */
function parseCommandLineArgs() {
  const args = {};
  process.argv.slice(2).forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      args[key] = value || true;
    }
  });
  return args;
}

/**
 * Display help message
 */
function displayHelp() {
  console.log(`
Usage: node test-discovery-endpoint.js [options]

Options:
  --file=<path>           Path to a JSON file containing campaign data
  --campaign_id=<id>      ID of an existing campaign to use
  --client_id=<id>        Client ID (default: 'default_client')
  --help, -h              Display this help message

Example:
  node test-discovery-endpoint.js --file=sample_jsons/campaign_analysis.json
  node test-discovery-endpoint.js --campaign_id=existing_campaign_id
  `);
}

/**
 * Load campaign data from a file
 * @param {string} filePath - Path to the campaign data file
 * @returns {Object} - The campaign data
 */
async function loadCampaignData(filePath) {
  try {
    logger.info(`Loading campaign data from ${filePath}`);
    const fullPath = path.resolve(__dirname, '..', filePath);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${fullPath}`);
    }

    const fileContent = fs.readFileSync(fullPath, 'utf8');
    const campaignData = JSON.parse(fileContent);

    logger.success(`Campaign data loaded successfully`);
    return campaignData;
  } catch (error) {
    logger.error(`Failed to load campaign data from ${filePath}`, error);
    throw error;
  }
}

/**
 * Test the discovery endpoint with campaign data
 * @param {Object} campaignData - The campaign data
 * @param {string} clientId - The client ID
 * @returns {Object} - The discovery results
 */
async function testDiscoveryEndpoint(campaignData, clientId = 'default_client') {
  try {
    logger.section('TESTING DISCOVERY ENDPOINT');
    logger.info(`Using client ID: ${clientId}`);

    // Generate a test campaign ID if not provided
    const campaignId = campaignData.id || `test_campaign_${Date.now()}`;
    logger.info(`Using campaign ID: ${campaignId}`);

    // Prepare the request payload
    const payload = {
      clientId,
      campaignId,
      campaignData
    };

    logger.debug('Request payload:', payload);

    // Start timing
    const startTime = performance.now();

    // Make the API request
    logger.info('Sending request to /api/discovery/discover endpoint...');
    const response = await axios.post(`${API_BASE_URL}/discovery/discover`, payload);

    // End timing
    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // Convert to seconds

    logger.success(`Discovery API call completed in ${duration.toFixed(2)} seconds`);

    // Check the response
    if (!response.data) {
      throw new Error('Empty response from Discovery API');
    }

    // Save the results to a file
    const timestamp = Date.now();
    const outputPath = path.join(TEST_OUTPUTS_DIR, `discovery_endpoint_results_${timestamp}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
    logger.info(`Results saved to ${outputPath}`);

    // Display summary of results
    const influencers = response.data.similar_accounts || [];
    logger.section('DISCOVERY RESULTS SUMMARY');
    logger.info(`Found ${influencers.length} influencers`);

    if (influencers.length > 0) {
      logger.info('Top influencers:');
      influencers.slice(0, 3).forEach((influencer, index) => {
        console.log(`  ${index + 1}. ${influencer.username} (${influencer.name || 'No name'}) - ${influencer.followers_count || 'Unknown'} followers`);
      });
    }

    return response.data;
  } catch (error) {
    logger.error('Discovery endpoint test failed', error);
    throw error;
  }
}

/**
 * Test the discovery endpoint with an existing campaign ID
 * @param {string} campaignId - The campaign ID
 * @param {string} clientId - The client ID
 * @returns {Object} - The discovery results
 */
async function testDiscoveryEndpointWithCampaignId(campaignId, clientId = 'default_client') {
  try {
    logger.section('TESTING DISCOVERY ENDPOINT WITH EXISTING CAMPAIGN');
    logger.info(`Using client ID: ${clientId}`);
    logger.info(`Using campaign ID: ${campaignId}`);

    // Prepare the request payload
    const payload = {
      clientId
    };

    // Start timing
    const startTime = performance.now();

    // Make the API request
    logger.info(`Sending request to /api/discovery/campaigns/${campaignId}/discover endpoint...`);
    const response = await axios.post(`${API_BASE_URL}/discovery/campaigns/${campaignId}/discover`, payload);

    // End timing
    const endTime = performance.now();
    const duration = (endTime - startTime) / 1000; // Convert to seconds

    logger.success(`Discovery API call completed in ${duration.toFixed(2)} seconds`);

    // Check the response
    if (!response.data) {
      throw new Error('Empty response from Discovery API');
    }

    // Save the results to a file
    const timestamp = Date.now();
    const outputPath = path.join(TEST_OUTPUTS_DIR, `discovery_endpoint_results_${timestamp}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(response.data, null, 2));
    logger.info(`Results saved to ${outputPath}`);

    // Display summary of results
    const influencers = response.data.similar_accounts || [];
    logger.section('DISCOVERY RESULTS SUMMARY');
    logger.info(`Found ${influencers.length} influencers`);

    if (influencers.length > 0) {
      logger.info('Top influencers:');
      influencers.slice(0, 3).forEach((influencer, index) => {
        console.log(`  ${index + 1}. ${influencer.username} (${influencer.name || 'No name'}) - ${influencer.followers_count || 'Unknown'} followers`);
      });
    }

    return response.data;
  } catch (error) {
    logger.error('Discovery endpoint test failed', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    const options = parseCommandLineArgs();

    if (options.help || options.h) {
      displayHelp();
      return;
    }

    if (options.file) {
      // Test with campaign data from file
      const campaignData = await loadCampaignData(options.file);
      await testDiscoveryEndpoint(campaignData, options.client_id);
    } else if (options.campaign_id) {
      // Test with existing campaign ID
      await testDiscoveryEndpointWithCampaignId(options.campaign_id, options.client_id);
    } else {
      logger.error('No file or campaign_id specified');
      displayHelp();
    }
  } catch (error) {
    logger.error('Test failed', error);
    process.exit(1);
  }
}

// Run the script if it's called directly
if (process.argv[1].endsWith('test-discovery-endpoint.js')) {
  main();
}

export {
  testDiscoveryEndpoint,
  testDiscoveryEndpointWithCampaignId
};

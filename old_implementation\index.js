// index.js
import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import { performInfluencerAnalysis } from './analysisFlow.js';

const app = express();

// CORS configuration: allow any origin ending with "lovableproject.com" or "lovable.app"
const corsOptions = {
  origin: (origin, callback) => {
    if (!origin) return callback(null, true);
    if (origin.endsWith("lovableproject.com") || origin.endsWith("lovable.app") || origin.endsWith("realizeanalytics.com")) {
      return callback(null, true);
    } else {
      return callback(new Error("Not allowed by CORS"));
    }
  },
};

app.use(cors(corsOptions));
app.options("*", cors(corsOptions));
app.use(bodyParser.json());

app.post('/influencerAnalysis', async (req, res) => {
  try {
    const input = req.body;
    let result;
    if (input.campaign) {
        console.log('Running new campaign request')
        result = await performInfluencerAnalysis(input);
        res.status(200).json(result);
    } else if(input.selected_account && input.report_id){
        console.log(`Enriching ${input.selected_account} for campaign ${input.report_id}`);
        result = await performInfluencerAnalysis(input);
        res.status(200).json(result);
    } else {
      return res.status(400).send('Invalid input format.');
    }
  } catch (err) {
    console.error('Error during analysis:', err);
    res.status(500).send('Internal Server Error');
  }
});

// Expose the Express app as the Cloud Function entry point.
export const influencerAnalysis = app;

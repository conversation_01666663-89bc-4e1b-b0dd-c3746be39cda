# Analysis Helpers Implementation Plan

This document outlines the detailed implementation plan for the analysis helper functions that will replace the combined_analyses.json and merged_analyses.json files in the new Firestore database structure.

## Overview

The analysis helpers will provide functions to generate combined and merged analyses on-demand, rather than storing them as static JSON files. This approach offers several advantages:

1. **Real-time Data**: The analyses will always reflect the latest data in the database
2. **Reduced Storage**: No need to store redundant data
3. **Flexibility**: The analyses can be customized based on the specific needs of the client
4. **Consistency**: The analyses will always be consistent with the underlying data

## Helper Functions

### 1. generateCombinedAnalysis

This function will combine data from the aesthetic analysis, ROI analysis, and web analysis into a single object.

```javascript
/**
 * Generate a combined analysis for an influencer in a specific campaign
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The combined analysis
 */
async function generateCombinedAnalysis(clientId, campaignId, influencerId) {
  const db = getFirestore();
  
  try {
    // Get the campaign influencer document
    const campaignInfluencerRef = db.collection('clients')
      .doc(clientId)
      .collection('campaigns')
      .doc(campaignId)
      .collection('campaign_influencers')
      .doc(influencerId);
    
    const campaignInfluencerDoc = await campaignInfluencerRef.get();
    
    if (!campaignInfluencerDoc.exists) {
      throw new Error(`Campaign influencer not found: ${influencerId}`);
    }
    
    // Get the aesthetic analysis
    const aestheticAnalysisSnapshot = await campaignInfluencerRef
      .collection('aesthetic_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();
    
    // Get the ROI analysis
    const roiAnalysisSnapshot = await campaignInfluencerRef
      .collection('roi_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();
    
    // Get the web analysis from the global influencer document
    const influencerRef = db.collection('influencers').doc(influencerId);
    const webAnalysisSnapshot = await influencerRef
      .collection('web_analysis')
      .orderBy('created_at', 'desc')
      .limit(1)
      .get();
    
    // Get the global influencer data
    const influencerDoc = await influencerRef.get();
    
    if (!influencerDoc.exists) {
      throw new Error(`Influencer not found: ${influencerId}`);
    }
    
    // Initialize the combined analysis object
    const combinedAnalysis = {
      profileInfo: {
        metrics: {},
        aestheticAnalysis: {}
      },
      roiProjection: {},
      webAnalysis: {},
      brandFit: {}
    };
    
    // Add aesthetic analysis data if available
    if (!aestheticAnalysisSnapshot.empty) {
      const aestheticAnalysis = aestheticAnalysisSnapshot.docs[0].data();
      
      combinedAnalysis.profileInfo.metrics = {
        brandAesthetic: `${aestheticAnalysis.content_analysis?.visual_fit || 0}/100`,
        styleAnalysis: ((aestheticAnalysis.brand_fit_score || 0) / 10).toFixed(1),
        vibeAlignment: ((aestheticAnalysis.brand_fit_score || 0) / 10).toFixed(1),
        visualAlignment: ((aestheticAnalysis.content_analysis?.visual_fit || 0) / 10).toFixed(1)
      };
      
      combinedAnalysis.profileInfo.aestheticAnalysis = {
        keyObservations: aestheticAnalysis.content_analysis?.content_themes || [],
        verdict: "Cohesive, brand-aligned aesthetic",
        verdictDescription: aestheticAnalysis.content_analysis?.tone_fit || ""
      };
    }
    
    // Add ROI analysis data if available
    if (!roiAnalysisSnapshot.empty) {
      const roiAnalysis = roiAnalysisSnapshot.docs[0].data();
      
      combinedAnalysis.profileInfo.metrics.roiPotential = roiAnalysis.influencer_analysis?.roi_projection?.roi_rating || "Medium";
      combinedAnalysis.profileInfo.metrics.brandFitScore = ((roiAnalysis.brand_fit_score || 0) / 10).toFixed(1);
      
      combinedAnalysis.roiProjection = {
        expectedImpressions: roiAnalysis.influencer_analysis?.roi_projection?.expected_impressions?.toLocaleString() || "0",
        expectedEngagementRate: `${roiAnalysis.influencer_analysis?.roi_projection?.expected_engagement_rate || 0}%`,
        expectedEngagements: roiAnalysis.influencer_analysis?.roi_projection?.expected_engagements?.toLocaleString() || "0",
        expectedEngagementDescription: roiAnalysis.influencer_analysis?.roi_projection?.roi_rationale || "",
        roiRating: roiAnalysis.influencer_analysis?.roi_projection?.roi_rating || "Medium",
        roiPotentialScore: "5",
        riskAssessment: roiAnalysis.risk_level || "Medium",
        riskAssessmentDescription: roiAnalysis.risk_description || "",
        audienceQuality: "High",
        audienceQualityDescription: "Engaged audience with strong interest in relevant topics."
      };
      
      combinedAnalysis.brandFit = {
        score: roiAnalysis.brand_fit_score || 0,
        description: roiAnalysis.brand_fit_description || "",
        strengths: roiAnalysis.influencer_analysis?.roi_projection?.strengths || [],
        weaknesses: roiAnalysis.influencer_analysis?.roi_projection?.weaknesses || []
      };
    }
    
    // Add web analysis data if available
    if (!webAnalysisSnapshot.empty) {
      const webAnalysis = webAnalysisSnapshot.docs[0].data();
      
      combinedAnalysis.webAnalysis = {
        sentimentScore: webAnalysis.sentiment_score || 0,
        riskLevel: webAnalysis.risk_level || "Medium",
        timeline: webAnalysis.deep_dive_report?.timeline_events || [],
        brandMentions: webAnalysis.deep_dive_report?.brand_mentions || [],
        partnerships: webAnalysis.deep_dive_report?.partnerships || [],
        controversies: webAnalysis.deep_dive_report?.controversies || []
      };
    }
    
    return combinedAnalysis;
  } catch (error) {
    console.error('Error generating combined analysis:', error);
    throw error;
  }
}
```

### 2. getProcessedInfluencerData

This function will retrieve the processed influencer data from Cloud Storage.

```javascript
/**
 * Get the processed influencer data from Cloud Storage
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The processed influencer data
 */
async function getProcessedInfluencerData(influencerId) {
  const db = getFirestore();
  const storage = new Storage();
  
  try {
    // Get the latest processed data reference
    const rawDataSnapshot = await db.collection('influencers')
      .doc(influencerId)
      .collection('raw_data')
      .orderBy('processed_at', 'desc')
      .limit(1)
      .get();
    
    if (rawDataSnapshot.empty) {
      throw new Error(`No processed data found for influencer: ${influencerId}`);
    }
    
    const rawDataDoc = rawDataSnapshot.docs[0].data();
    const processedStoragePath = rawDataDoc.processed_storage_path;
    
    // Get the processed data from Cloud Storage
    const bucket = storage.bucket(processedStoragePath.split('/')[0]);
    const file = bucket.file(processedStoragePath.split('/').slice(1).join('/'));
    
    const [contents] = await file.download();
    const processedData = JSON.parse(contents.toString('utf8'));
    
    return processedData;
  } catch (error) {
    console.error('Error getting processed influencer data:', error);
    throw error;
  }
}
```

### 3. generateMergedAnalysis

This function will merge the combined analysis with the processed influencer data.

```javascript
/**
 * Generate a merged analysis by combining the processed influencer data
 * with the combined analysis
 * @param {string} clientId - The client ID
 * @param {string} campaignId - The campaign ID
 * @param {string} influencerId - The influencer ID
 * @returns {Object} - The merged analysis
 */
async function generateMergedAnalysis(clientId, campaignId, influencerId) {
  try {
    // Get the combined analysis
    const combinedAnalysis = await generateCombinedAnalysis(clientId, campaignId, influencerId);
    
    // Get the processed influencer data
    const processedData = await getProcessedInfluencerData(influencerId);
    
    // Merge the two objects
    return deepMerge(combinedAnalysis, processedData);
  } catch (error) {
    console.error('Error generating merged analysis:', error);
    throw error;
  }
}
```

### 4. deepMerge

This utility function will merge two objects recursively.

```javascript
/**
 * Deep merge two objects
 * @param {Object} target - The target object
 * @param {Object} source - The source object
 * @returns {Object} - The merged object
 */
function deepMerge(target, source) {
  const output = { ...target };
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  
  return output;
}

/**
 * Check if a value is an object
 * @param {*} item - The value to check
 * @returns {boolean} - Whether the value is an object
 */
function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}
```

## Integration with Analysis Flow

The analysis helpers will be integrated into the analysis flow as follows:

```javascript
// Pseudocode
async function performInfluencerAnalysis(input) {
  // Phase 1: Campaign Brief & Audience Analysis
  // ...
  
  // Phase 2: Broad Influencer Discovery
  // ...
  
  // Phase 3: Influencer Enrichment
  // ...
  
  // Phase 4: Deep-Dive Web Search & Historical Analysis
  // ...
  
  // Phase 5: Visual & Content Analysis
  // ...
  
  // Phase 6: ROI & Strategic Fit Evaluation
  // ...
  
  // Generate merged analysis
  const mergedAnalysis = await generateMergedAnalysis(clientId, campaignId, influencerId);
  
  return mergedAnalysis;
}
```

## API Endpoints

The analysis helpers will be exposed through API endpoints:

```javascript
// Pseudocode
app.get('/api/campaigns/:campaignId/influencers/:influencerId/combined', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId } = req.query;
    const result = await generateCombinedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting combined analysis:', error);
    res.status(500).json({ error: 'Failed to get combined analysis' });
  }
});

app.get('/api/campaigns/:campaignId/influencers/:influencerId/merged', async (req, res) => {
  try {
    const { campaignId, influencerId } = req.params;
    const { clientId } = req.query;
    const result = await generateMergedAnalysis(clientId, campaignId, influencerId);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting merged analysis:', error);
    res.status(500).json({ error: 'Failed to get merged analysis' });
  }
});
```

## Error Handling

The analysis helpers will implement robust error handling to deal with various error scenarios:

1. **Firestore Errors**: Handle errors when interacting with Firestore
2. **Cloud Storage Errors**: Handle errors when interacting with Cloud Storage
3. **Data Processing Errors**: Handle errors when processing data
4. **Missing Data**: Handle cases where data is missing

## Caching Strategy

To improve performance, the analysis helpers will implement a caching strategy:

```javascript
// Pseudocode
const cache = new Map();

async function getCachedCombinedAnalysis(clientId, campaignId, influencerId) {
  const cacheKey = `combined_${clientId}_${campaignId}_${influencerId}`;
  
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }
  
  const result = await generateCombinedAnalysis(clientId, campaignId, influencerId);
  cache.set(cacheKey, result);
  
  return result;
}

async function getCachedMergedAnalysis(clientId, campaignId, influencerId) {
  const cacheKey = `merged_${clientId}_${campaignId}_${influencerId}`;
  
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }
  
  const result = await generateMergedAnalysis(clientId, campaignId, influencerId);
  cache.set(cacheKey, result);
  
  return result;
}
```

## Testing Strategy

The analysis helpers will be tested using the following strategy:

1. **Unit Tests**: Test individual functions with mock data
2. **Integration Tests**: Test the functions with real data in Firestore
3. **Error Tests**: Test error handling with simulated errors
4. **Performance Tests**: Test with large datasets to ensure efficiency

## Implementation Steps

1. **Create Helper Functions**: Implement the helper functions
2. **Implement Error Handling**: Add robust error handling
3. **Implement Caching**: Add caching to improve performance
4. **Test Helpers**: Test the helpers with real data
5. **Integrate with Analysis Flow**: Integrate the helpers into the analysis flow
6. **Create API Endpoints**: Expose the helpers through API endpoints

## Conclusion

The analysis helpers will provide a flexible and efficient way to generate combined and merged analyses on-demand, rather than storing them as static JSON files. This approach will ensure that the analyses always reflect the latest data in the database, while reducing storage requirements and improving flexibility.

// test-campaign-retrieval.js
// Comprehensive test script to diagnose campaign data retrieval issues

import dotenv from 'dotenv';
import { initializeFirebase } from './config/firebase-config.js';
import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { getCampaignData } from './services/analysis-service.js';

// Load environment variables
dotenv.config();

// Initialize Firebase
console.log('🔥 Initializing Firebase...');
initializeFirebase(true); // verbose logging
const db = getFirestore();

/**
 * Test campaign data retrieval with extensive logging
 */
async function testCampaignRetrieval() {
  console.log('\n=== CAMPAIGN RETRIEVAL DIAGNOSTIC TEST ===\n');

  // Test parameters from your logs
  const testCampaignId = 'Wfs5DW2kCVV0xy4tkSGQ';
  const testClientId = 'KVywTcxSRAS5yPXD7vMx3WkUKXw1';

  console.log(`📋 Test Parameters:`);
  console.log(`   Campaign ID: ${testCampaignId}`);
  console.log(`   Client ID: ${testClientId}`);
  console.log(`   Firestore Path: /clients/${testClientId}/campaigns/${testCampaignId}`);

  try {
    console.log('\n🔍 Step 1: Direct Firestore Query');
    console.log('=====================================');

    const campaignRef = db.collection('clients').doc(testClientId).collection('campaigns').doc(testCampaignId);
    console.log(`📍 Campaign Reference Path: ${campaignRef.path}`);

    const startTime = performance.now();
    const campaignDoc = await campaignRef.get();
    const endTime = performance.now();

    console.log(`⏱️  Query Time: ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`📄 Document Exists: ${campaignDoc.exists}`);

    if (!campaignDoc.exists) {
      console.log('❌ Campaign document does not exist in Firestore');

      // Check if client exists
      console.log('\n🔍 Checking if client exists...');
      const clientRef = db.collection('clients').doc(testClientId);
      const clientDoc = await clientRef.get();
      console.log(`👤 Client Exists: ${clientDoc.exists}`);

      if (clientDoc.exists) {
        // List all campaigns for this client
        console.log('\n📋 Listing all campaigns for client...');
        const campaignsSnapshot = await clientRef.collection('campaigns').get();
        console.log(`📊 Total Campaigns Found: ${campaignsSnapshot.size}`);

        campaignsSnapshot.forEach((doc, index) => {
          console.log(`   ${index + 1}. Campaign ID: ${doc.id}`);
          const data = doc.data();
          console.log(`      Name: ${data.name || 'No name'}`);
          console.log(`      Created: ${data.created_at ? data.created_at.toDate() : 'No timestamp'}`);
        });
      }

      return;
    }

    console.log('\n📊 Step 2: Raw Document Data Analysis');
    console.log('=====================================');

    const rawData = campaignDoc.data();
    console.log(`📦 Raw Data Type: ${typeof rawData}`);
    console.log(`📦 Raw Data Keys: ${Object.keys(rawData || {}).join(', ')}`);
    console.log(`📦 Raw Data Size: ${JSON.stringify(rawData).length} characters`);

    // Log each field individually
    console.log('\n🔍 Field-by-Field Analysis:');
    const expectedFields = [
      'name', 'report_id', 'product_description', 'influencer_gender',
      'influencer_niche', 'influencer_age', 'influencer_personality',
      'influencer_aesthetic', 'min_follower_count', 'max_follower_count',
      'min_engagement_rate', 'created_at', 'updated_at', 'status',
      'influencer_description', 'main_platform'
    ];

    expectedFields.forEach(field => {
      const value = rawData[field];
      const type = typeof value;
      const exists = value !== undefined && value !== null;
      console.log(`   ${field}: ${exists ? '✅' : '❌'} (${type}) = ${JSON.stringify(value)}`);
    });

    console.log('\n🧪 Step 3: Validation Logic Test');
    console.log('=================================');

    // Test the exact validation logic from getCampaignData
    const hasData = rawData !== null && rawData !== undefined;
    const hasName = rawData && rawData.name;
    const nameType = typeof rawData?.name;
    const nameValue = rawData?.name;

    console.log(`📊 Validation Results:`);
    console.log(`   Has Data: ${hasData}`);
    console.log(`   Has Name Field: ${hasName}`);
    console.log(`   Name Type: ${nameType}`);
    console.log(`   Name Value: "${nameValue}"`);
    console.log(`   Name Length: ${nameValue ? nameValue.length : 'N/A'}`);
    console.log(`   Validation Passes: ${hasData && hasName}`);

    if (!hasData || !hasName) {
      console.log('❌ VALIDATION FAILURE - This explains the error!');

      if (!hasData) {
        console.log('   🔍 Issue: Document data is null/undefined');
      }
      if (!hasName) {
        console.log('   🔍 Issue: Name field is missing, null, undefined, or empty string');
      }
    } else {
      console.log('✅ VALIDATION SUCCESS - Campaign data should be valid');
    }

    console.log('\n🔧 Step 4: Testing getCampaignData Function');
    console.log('============================================');

    try {
      const campaignData = await getCampaignData(testClientId, testCampaignId);
      console.log('✅ getCampaignData() succeeded!');
      console.log(`📊 Returned Data Keys: ${Object.keys(campaignData).join(', ')}`);
      console.log(`📊 Campaign Name: "${campaignData.name}"`);
    } catch (functionError) {
      console.log('❌ getCampaignData() failed with error:');
      console.log(`   Error Message: ${functionError.message}`);
      console.log(`   Error Stack: ${functionError.stack}`);
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }
}

/**
 * Test the complete analysis endpoint parameters
 */
async function testCompleteAnalysisParams() {
  console.log('\n=== COMPLETE ANALYSIS ENDPOINT TEST ===\n');

  const testParams = {
    username: 'fbehery',
    campaignId: 'Wfs5DW2kCVV0xy4tkSGQ',
    clientId: 'KVywTcxSRAS5yPXD7vMx3WkUKXw1',
    platform: 'instagram',
    forceRefresh: false
  };

  console.log('📋 Test Parameters:');
  Object.entries(testParams).forEach(([key, value]) => {
    console.log(`   ${key}: ${JSON.stringify(value)} (${typeof value})`);
  });

  // Test parameter validation
  console.log('\n🧪 Parameter Validation:');
  const validations = [
    { field: 'username', value: testParams.username, required: true },
    { field: 'campaignId', value: testParams.campaignId, required: true },
    { field: 'clientId', value: testParams.clientId, required: true },
    { field: 'platform', value: testParams.platform, required: true }
  ];

  validations.forEach(({ field, value, required }) => {
    const isValid = required ? (value && value.length > 0) : true;
    console.log(`   ${field}: ${isValid ? '✅' : '❌'} ${required ? '(required)' : '(optional)'}`);
  });
}

/**
 * Test CORS configuration
 */
async function testCORSConfiguration() {
  console.log('\n=== CORS CONFIGURATION TEST ===\n');

  const testOrigins = [
    'https://palas-find-influencer-22.lovable.app',
    'https://palas-find-influencer-22.lovable.app/select-influencer',
    'https://palas.realizeanalytics.com',
    'https://preview--palas-find-influencer-22.lovable.app'
  ];

  console.log('🌐 Testing CORS Origins:');
  testOrigins.forEach((origin, index) => {
    console.log(`   ${index + 1}. ${origin}`);
  });

  // Import CORS options to test
  try {
    const { corsOptions } = await import('./middleware/cors.js');
    console.log('\n🧪 CORS Options Loaded Successfully');
    console.log(`   Methods: ${corsOptions.methods?.join(', ') || 'Not specified'}`);
    console.log(`   Credentials: ${corsOptions.credentials}`);
    console.log(`   Headers: ${corsOptions.allowedHeaders?.join(', ') || 'Not specified'}`);
  } catch (error) {
    console.log('❌ Failed to load CORS options:', error.message);
  }
}

/**
 * Test Firebase connection and permissions
 */
async function testFirebaseConnection() {
  console.log('\n=== FIREBASE CONNECTION TEST ===\n');

  try {
    // Test basic Firestore connection
    console.log('🔥 Testing Firestore Connection...');
    const testRef = db.collection('_test').doc('connection-test');
    await testRef.set({
      timestamp: Timestamp.now(),
      test: 'connection-test'
    });

    const testDoc = await testRef.get();
    if (testDoc.exists) {
      console.log('✅ Firestore Write/Read: SUCCESS');
      await testRef.delete(); // Clean up
    } else {
      console.log('❌ Firestore Read: FAILED');
    }

    // Test client collection access
    console.log('\n👤 Testing Client Collection Access...');
    const clientsSnapshot = await db.collection('clients').limit(1).get();
    console.log(`📊 Clients Collection Access: ${clientsSnapshot.size >= 0 ? '✅ SUCCESS' : '❌ FAILED'}`);

  } catch (error) {
    console.log('❌ Firebase Connection Test Failed:', error.message);
  }
}

/**
 * Main test runner
 */
async function runTests() {
  try {
    await testFirebaseConnection();
    await testCORSConfiguration();
    await testCampaignRetrieval();
    await testCompleteAnalysisParams();

    console.log('\n✅ All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   - Check the validation results above');
    console.log('   - Look for any ❌ indicators');
    console.log('   - The getCampaignData test will show if the function works');
    console.log('   - CORS configuration shows allowed origins');
    console.log('   - Firebase connection test verifies database access');

  } catch (error) {
    console.error('💥 Test suite failed:', error);
  } finally {
    console.log('\n🏁 Test suite finished');
    process.exit(0);
  }
}

// Run the tests
runTests();

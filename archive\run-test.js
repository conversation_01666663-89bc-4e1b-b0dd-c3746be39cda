import testInfluencerAnalysis from './test-influencer-analysis.js';

// Get command line arguments
const username = process.argv[2];
const platform = process.argv[3] || 'instagram';
const useCache = process.argv[4] !== 'false';
const skipSteps = process.argv[5] !== 'false';

if (!username) {
  console.error('❌ Error: Please provide an influencer username');
  console.log('Usage: node run-test.js <username> [platform] [useCache] [skipSteps]');
  console.log('  - username: The influencer username (required)');
  console.log('  - platform: The platform (default: instagram)');
  console.log('  - useCache: Whether to use cached data (default: true, set to "false" to disable)');
  console.log('  - skipSteps: Whether to skip steps that have already been completed (default: true, set to "false" to disable)');
  console.log('\nExample: node run-test.js cristiano instagram true true');
  process.exit(1);
}

console.log(`🚀 Running analysis for ${username} on ${platform}`);
console.log(`🔧 Settings: useCache=${useCache}, skipSteps=${skipSteps}`);

testInfluencerAnalysis(username, platform, useCache, skipSteps)
  .then(results => {
    console.log('\n📊 ANALYSIS RESULTS SUMMARY:');
    console.log(JSON.stringify(results, null, 2));
    console.log('\n✅ Test completed successfully');
  })
  .catch(error => {
    console.error('\n❌ Test failed with error:');
    console.error(error);
  });
